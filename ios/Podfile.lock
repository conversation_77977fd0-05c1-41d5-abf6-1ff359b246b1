PODS:
  - DKImagePickerController/Core (4.3.4):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.4)
  - DKImagePickerController/PhotoGallery (4.3.4):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.4)
  - DKPhotoGallery (0.0.17):
    - DKPhotoGallery/Core (= 0.0.17)
    - DKPhotoGallery/Model (= 0.0.17)
    - DKPhotoGallery/Preview (= 0.0.17)
    - DKPhotoGallery/Resource (= 0.0.17)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.17):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.17):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - location (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SDWebImage (5.18.2):
    - SDWebImage/Core (= 5.18.2)
  - SDWebImage/Core (5.18.2)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.4)

DEPENDENCIES:
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - location (from `.symlinks/plugins/location/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - SDWebImage
    - SwiftyGif

EXTERNAL SOURCES:
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  location:
    :path: ".symlinks/plugins/location/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"

SPEC CHECKSUMS:
  DKImagePickerController: b512c28220a2b8ac7419f21c491fc8534b7601ac
  DKPhotoGallery: fdfad5125a9fdda9cc57df834d49df790dbb4179
  file_picker: 09aa5ec1ab24135ccd7a1621c46c84134bfd6655
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  location: d5cf8598915965547c3f36761ae9cc4f4e87d22e
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  SDWebImage: c0de394d7cf7f9838aed1fd6bb6037654a4572e4
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  SwiftyGif: 93a1cc87bf3a51916001cf8f3d63835fb64c819f

PODFILE CHECKSUM: 819463e6a0290f5a72f145ba7cde16e8b6ef0796

COCOAPODS: 1.15.2
