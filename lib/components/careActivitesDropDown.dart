import 'package:flutter/material.dart';

import '../models/staff.dart';

class DropdownSelector extends StatelessWidget {
  final String label;
  final List<String> options;
  final ValueChanged<String?> onChanged;

  DropdownSelector(
      {required this.label, required this.options, required this.onChanged});

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      decoration:
          InputDecoration(labelText: label, contentPadding: EdgeInsets.all(10)),
      items: options
          .map((option) => DropdownMenuItem(value: option, child: Text(option)))
          .toList(),
      onChanged: onChanged,
    );
  }
}

class HandOffDropDown extends StatelessWidget {
  final String label;
  final List<Staff> options;
  final ValueChanged<String?> onChanged;

  HandOffDropDown(
      {required this.label, required this.options, required this.onChanged});

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      decoration:
      InputDecoration(labelText: label, contentPadding: EdgeInsets.all(10)),
      items: options
          .map((staff) => DropdownMenuItem(
                value: staff.id,
                child: Text('${staff.firstName} (${staff.role})'),
              ))
          .toList(),
      onChanged: onChanged,
    );
  }
}

