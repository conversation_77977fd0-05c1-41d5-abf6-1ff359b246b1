import 'package:carerez/routes/router_constants.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class FloatingBottomNavBar extends StatefulWidget {
  final int selectedIndex; // Accept selectedIndex as a parameter

  const FloatingBottomNavBar({super.key, this.selectedIndex = 0});

  @override
  State<FloatingBottomNavBar> createState() => _FloatingBottomNavBarState();
}

class _FloatingBottomNavBarState extends State<FloatingBottomNavBar> {
  late int _selectedIndex;

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.selectedIndex;
  }

  @override
  Widget build(BuildContext context) {
    return _buildFloatingNavBar();
  }

  Widget _buildFloatingNavBar() {
    return SafeArea(
      bottom: true, // Ensures it's above system UI
      child: Padding(
        padding: const EdgeInsets.only(left: 8, right: 8, bottom: 8),
        child: Material(
          elevation: 8,
          borderRadius: BorderRadius.circular(40),
          color: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 4), // Adds internal spacing
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(240),
              borderRadius: BorderRadius.circular(40),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(40),
              child: BottomNavigationBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                currentIndex: _selectedIndex,
                type: BottomNavigationBarType.fixed,
                selectedItemColor: const Color.fromRGBO(90, 38, 101, 1),
                unselectedItemColor: Colors.grey[400],
                showSelectedLabels: false,
                showUnselectedLabels: false,
                onTap: (int index) {
                  setState(() {
                    _selectedIndex = index;
                  });
                  _onNavItemTapped(index);
                },
                items: const [
                  BottomNavigationBarItem(
                    icon: Icon(Icons.home, size: 28),
                    label: 'Home',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.calendar_month, size: 28),
                    label: 'Calendar',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.chat, size: 28),
                    label: 'Chats',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.settings, size: 28),
                    label: 'Settings',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.notifications, size: 28),
                    label: 'Notifications',
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _onNavItemTapped(int index) {
    switch (index) {
      case 0:
        GoRouter.of(context).goNamed(RouteConstants.homescreenRouteName,
            extra: {'selectedIndex': 1});
        break;
      case 1:
        GoRouter.of(context).goNamed(RouteConstants.calenderRouteName,
            extra: {'selectedIndex': 2});

        break;
      case 2:
        GoRouter.of(context).goNamed(RouteConstants.chatsRouteName,
            extra: {'selectedIndex': 3});

        break;
      case 3:
        GoRouter.of(context).goNamed(RouteConstants.settingsRouteName,
            extra: {'selectedIndex': 4});
        break;
      case 4:
        GoRouter.of(context).goNamed(RouteConstants.notificationsRouteName,
            extra: {'selectedIndex': 5});
        break;
      default:
        GoRouter.of(context).goNamed(RouteConstants.homescreenRouteName,
            extra: {'selectedIndex': 0});
        break;
    }
  }
}
