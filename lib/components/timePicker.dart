import 'package:flutter/material.dart';

class TimePicker extends StatefulWidget {
  final void Function(int hour, int minute) onTimeChanged;

  const TimePicker({Key? key, required this.onTimeChanged}) : super(key: key);

  @override
  _TimePickerState createState() => _TimePickerState();
}

class _TimePickerState extends State<TimePicker> {
  int selectedHour = 0;
  int selectedMinute = 0;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text('Time Taken',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500)),
        ),
        Expanded(
          child: SizedBox(
            height: 100,
            child: ListWheelScrollView.useDelegate(
              itemExtent: 50,
              physics: FixedExtentScrollPhysics(),
              onSelectedItemChanged: (index) {
                setState(() {
                  selectedHour = index;
                  widget.onTimeChanged(selectedHour, selectedMinute);
                });
              },
              childDelegate: ListWheelChildBuilderDelegate(
                builder: (context, index) => Center(
                  child: Text(
                    index.toString().padLeft(2, '0'),
                    style: TextStyle(fontSize: 24),
                  ),
                ),
                childCount: 24,
              ),
            ),
          ),
        ),
        Text(
          ':',
          style: TextStyle(fontSize: 24),
        ),
        Expanded(
          child: SizedBox(
            height: 100,
            child: ListWheelScrollView.useDelegate(
              itemExtent: 50,
              physics: FixedExtentScrollPhysics(),
              onSelectedItemChanged: (index) {
                setState(() {
                  selectedMinute = index;
                  widget.onTimeChanged(selectedHour, selectedMinute);
                });
              },
              childDelegate: ListWheelChildBuilderDelegate(
                builder: (context, index) => Center(
                  child: Text(
                    index.toString().padLeft(2, '0'),
                    style: TextStyle(fontSize: 24),
                  ),
                ),
                childCount: 60,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
