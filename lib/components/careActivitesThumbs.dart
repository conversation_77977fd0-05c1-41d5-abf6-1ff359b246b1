import 'package:flutter/material.dart';

class ResponseType extends StatefulWidget {
  final ValueChanged<String> onResponseSelected;
  const ResponseType({super.key, required this.onResponseSelected});

  @override
  State<ResponseType> createState() => _ResponseTypeState();
}

class _ResponseTypeState extends State<ResponseType> {
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    List<Map<String, dynamic>> responses = [
      {
        'icon': Icons.thumb_up_alt_outlined,
        'label': 'Positive',
      },
      {
        'icon': Icons.sentiment_neutral,
        'label': 'Neutral',
      },
      {
        'icon': Icons.thumb_down_alt_outlined,
        'label': 'Negative',
      },
    ];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: responses.map((response) {
        return GestureDetector(
          onTap: () {
            setState(() {
              selectedResponse = response['label'];
            });
            widget.onResponseSelected(response['label']);
          },
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: selectedResponse == response['label']
                      ? Colors.purple.withOpacity(0.2)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.all(8),
                child: Icon(response['icon'], size: 40, color: Colors.purple),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
