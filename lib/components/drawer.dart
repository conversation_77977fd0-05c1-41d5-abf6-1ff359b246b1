import 'package:carerez/dialogs/logout.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';
import '../blocs/user/user_bloc.dart';
import '../blocs/user/user_state.dart';
import '../routes/router_constants.dart';
import '../blocs/auth/auth_bloc.dart';
import '../blocs/auth/auth_event.dart';
import '../blocs/auth/auth_state.dart';

class buildDrawer extends StatelessWidget {
  final GlobalKey<ScaffoldState> scaffoldKey;

  buildDrawer({
    super.key,
    required this.scaffoldKey,
  });

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: Colors.white,
      child: BlocBuilder<UserBloc, UserState>(builder: (context, state) {
        if (state is UserLoaded || state is UserAndTasksLoaded) {
          final user = state is UserLoaded
              ? state.user
              : (state as UserAndTasksLoaded).user;
          return Column(
            children: <Widget>[
              SizedBox(
                height: MediaQuery.of(context).size.height * 0.3,
                child: DrawerHeader(
                  decoration: const BoxDecoration(
                    color: Color.fromRGBO(90, 38, 101, 1),
                  ),
                  child: Stack(children: [
                    IconButton(
                        onPressed: () {
                          scaffoldKey.currentState!.closeDrawer();
                        },
                        icon: const Icon(
                          HugeIcons.strokeRoundedMenu01,
                          color: Colors.white,
                          size: 30,
                        )),
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircleAvatar(
                            radius: 50,
                            backgroundImage: NetworkImage(
                              user.profileUrl.isNotEmpty
                                  ? user.profileUrl
                                  : 'https://www.gravatar.com/avatar/${user.emailAddress.hashCode}?d=identicon',
                            ),
                          ),
                          SizedBox(height: 10),
                          Text(
                            user.fullName.isNotEmpty
                                ? user.fullName
                                : (user.tenantName != null &&
                                            user.tenantName!.isNotEmpty
                                        ? user.tenantName
                                        : 'User')
                                    .toString(),
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                            ),
                          ),
                          Text(
                            user.emailAddress,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                            ),
                          ),
                        ],
                      ),
                    )
                  ]),
                ),
              ),
              Expanded(
                // This pushes the Log Out button to the bottom
                child: ListView(
                  padding: EdgeInsets.zero,
                  children: <Widget>[
                    CustomDrawerTile(
                      text: 'Home',
                      icon: Icons.home,
                      onTap: () {
                        GoRouter.of(context)
                            .goNamed(RouteConstants.homescreenRouteName);
                      },
                    ),
                    CustomDrawerTile(
                      text: 'Residents',
                      icon: Icons.people,
                      onTap: () {
                        GoRouter.of(context)
                            .goNamed(RouteConstants.residentsRouteName);
                      },
                    ),
                    CustomDrawerTile(
                      text: 'Units',
                      icon: Icons.store,
                      onTap: () {
                        GoRouter.of(context)
                            .goNamed(RouteConstants.unitsRouteName);
                      },
                    ),
                  ],
                ),
              ),
              // Log Out button is placed at the bottom now
              LogOutButton(),
            ],
          );
        }
        return Center(
          child: CircularProgressIndicator(),
        );
      }),
    );
  }
}

Widget LogOutButton() {
  return BlocBuilder<AuthBloc, AuthState>(
    builder: (context, state) {
      return CustomDrawerTile(
        text: 'Log Out',
        icon: Icons.logout,
        iconColor: Colors.red,
        textColor: Colors.red,
        onTap: () {
          showLogoutDialog(context);
        },
      );
    },
  );
}

class CustomDrawerTile extends StatelessWidget {
  final String text;
  final IconData icon;
  final textColor;
  final iconColor;
  final VoidCallback? onTap;

  const CustomDrawerTile({
    Key? key,
    required this.text,
    required this.icon,
    this.textColor,
    this.iconColor,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: iconColor ?? Color(0xFF757575),
                  size: 26,
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Text(
                    text,
                    style: TextStyle(
                      fontSize: 16,
                      color: textColor ?? Color(0xFF757575),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: iconColor ?? Color(0xFF757575),
                  size: 18,
                ),
              ],
            ),
            if (text != 'Log Out')
              Divider(
                color: Colors.grey,
              )
          ],
        ),
      ),
    );
  }
}
