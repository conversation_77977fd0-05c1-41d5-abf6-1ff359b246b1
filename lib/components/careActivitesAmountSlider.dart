import 'package:flutter/material.dart';

class AmountSlider extends StatefulWidget {
  final String label;
  final double initialValue;
  final ValueChanged<double> onChanged;

  const AmountSlider({
    required this.label,
    required this.initialValue,
    required this.onChanged,
    super.key,
  });

  @override
  State<AmountSlider> createState() => _AmountSliderState();
}

class _AmountSliderState extends State<AmountSlider> {
  late double _currentValue;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.initialValue;
  }

  @override
  Widget build(BuildContext context) {
    const List<String> fractionLabels = ['<1/4', '1/4', '1/2', '3/4', 'All'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Slider(
          value: _currentValue,
          min: 0,
          max: 4,
          divisions: 4,
          label: fractionLabels[_currentValue.round()],
          onChanged: (value) {
            setState(() {
              _currentValue = value;
            });
            widget.onChanged(value); // Notify parent widget of the change
          },
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: fractionLabels.map((fraction) {
            return Text(
              fraction,
              style: TextStyle(
                fontSize: 12,
                color: Colors.black54,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
