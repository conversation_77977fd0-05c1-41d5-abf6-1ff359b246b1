import 'package:flutter/material.dart';

class DropdownSelectorToilet extends StatefulWidget {
  final String label;
  final List<String> options;
  final Function(String) onChanged;

  const DropdownSelectorToilet({
    super.key,
    required this.label,
    required this.options,
    required this.onChanged,
  });

  @override
  State<DropdownSelectorToilet> createState() => _DropdownSelectorToiletState();
}

class _DropdownSelectorToiletState extends State<DropdownSelectorToilet> {
  String? selectedOption;
  double catheterQuantity = 0;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.label,
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 5),
          DropdownButton<String>(
            dropdownColor: Colors.white,
            value: selectedOption,
            hint: Text("Select an option"),
            isExpanded: true,
            items: widget.options.map((option) {
              return DropdownMenuItem<String>(
                value: option,
                child: Text(option),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                selectedOption = value;
                widget.onChanged(value!);
              });

              if (value == 'Bowels Opened') {
                _showBowelsOpenedSubMenu(context);
              } else if (value == 'Catether Empty') {
                _showCatheterQuantitySlider(context);
              } else if (value == 'Staff Supported') {
                _showCatheterQuantitySlider(context);
              }
            },
          ),
          SizedBox(height: 10),
          if (selectedOption != null) ...[
            Text(
              'Selected Activity: $selectedOption',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            SizedBox(height: 10),
          ],
          if (selectedOption == 'Catether Empty') ...[
            Text(
              'Catheter Quantity Emptied: ${catheterQuantity.toInt()} ml',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
          ],
          if (selectedOption == 'Staff Supported') ...[
            Text(
              'Urine Quantity: ${catheterQuantity.toInt()} ml',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
          ],
        ],
      ),
    );
  }

  void _showBowelsOpenedSubMenu(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        title: Text("Bowels Opened Options"),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: Text("Option 1"),
              onTap: () {
                setState(() {
                  selectedOption = "Bowels Opened - Option 1";
                });
                Navigator.of(context).pop();
              },
            ),
            ListTile(
              title: Text("Option 2"),
              onTap: () {
                setState(() {
                  selectedOption = "Bowels Opened - Option 2";
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showCatheterQuantitySlider(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        title: Text("Catheter Quantity"),
        content: StatefulBuilder(
          builder: (BuildContext context, StateSetter setDialogState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '${catheterQuantity.toInt()} ml',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                Slider(
                  value: catheterQuantity,
                  min: 0,
                  max: 1000,
                  divisions: 20,
                  label: '${catheterQuantity.toInt()} ml',
                  onChanged: (value) {
                    // Update both dialog state and main widget state
                    setDialogState(() {
                      catheterQuantity = value;
                    });
                    setState(() {
                      catheterQuantity = value;
                    });
                  },
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text("Done"),
          ),
        ],
      ),
    );
  }
}
