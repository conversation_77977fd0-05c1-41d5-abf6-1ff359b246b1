import 'package:flutter/material.dart';

class DrinkDetails extends StatelessWidget {
  final ValueChanged<TimeOfDay> onTimeChanged;
  final ValueChanged<String> onLocationChanged;
  final TimeOfDay initialTime;
  final String initialLocation;

  const DrinkDetails({
    required this.onTimeChanged,
    required this.onLocationChanged,
    this.initialTime = const TimeOfDay(hour: 0, minute: 0),
    this.initialLocation = '',
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    TextEditingController timeController = TextEditingController(
      text: '${initialTime.format(context)}',
    );
    TextEditingController locationController = TextEditingController(
      text: initialLocation,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'When was the drink taken?',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        GestureDetector(
          onTap: () async {
            TimeOfDay? pickedTime = await showTimePicker(
              context: context,
              initialTime: initialTime,
            );
            if (pickedTime != null) {
              onTimeChanged(pickedTime);
              timeController.text = pickedTime.format(context);
            }
          },
          child: AbsorbPointer(
            child: TextField(
              controller: timeController,
              decoration: InputDecoration(
                hintText: 'Select time',
                prefixIcon: Icon(Icons.access_time),
              ),
            ),
          ),
        ),
        SizedBox(height: 10),
        Text(
          'Where was the drink taken?',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        TextField(
          controller: locationController,
          decoration: InputDecoration(
            hintText: 'Enter location',
            hintStyle: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            prefixIcon: Icon(Icons.location_on),
          ),
          onChanged: (value) => onLocationChanged(value),
        ),
      ],
    );
  }
}
