import 'package:flutter/material.dart';

class MoodSelection extends StatefulWidget {
  final ValueChanged<String> onMoodSelected;

  MoodSelection({required this.onMoodSelected});

  @override
  _MoodSelectionState createState() => _MoodSelectionState();
}

class _MoodSelectionState extends State<MoodSelection> {
  String? selectedMood;

  @override
  Widget build(BuildContext context) {
    List<Map<String, dynamic>> moods = [
      {'icon': Icons.sentiment_very_satisfied, 'label': 'Very Happy','value':'delighted'},
      {'icon': Icons.sentiment_satisfied, 'label': 'Happy','value':'happy'},
      {'icon': Icons.sentiment_neutral, 'label': 'OK','value':'neutral'},
      {'icon': Icons.sentiment_dissatisfied, 'label': 'Sad','value':'sad'},
      {'icon': Icons.sentiment_very_dissatisfied, 'label': 'Very Sad','value':'extremely sad'},
    ];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: moods.map((mood) {
        return GestureDetector(
          onTap: () {
            setState(() {
              selectedMood = mood['value'];
            });
            widget.onMoodSelected(mood['value']);
          },
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(
                    color: selectedMood == mood['value']
                        ? Colors.purple
                        : Colors.transparent,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(5),
                ),
                padding: const EdgeInsets.all(5),
                child: Icon(mood['icon'], size: 40, color: Colors.amber),
              ),
              Text(mood['label']),
            ],
          ),
        );
      }).toList(),
    );
  }
}
