import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';

class AttachmentButton extends StatelessWidget {
  final List<String> attachments;
  final Function(List<String>) onAttachmentsSelected;

  AttachmentButton(
      {required this.attachments, required this.onAttachmentsSelected});

  @override
  Widget build(BuildContext context) {
    return TextButton.icon(
      onPressed: () async {
        FilePickerResult? result =
            await FilePicker.platform.pickFiles(allowMultiple: true);
        if (result != null) {
          onAttachmentsSelected(result.paths.whereType<String>().toList());
        }
      },
      icon: Icon(Icons.attach_file),
      label: Text('ADD AN ATTACHMENT'),
    );
  }
}
