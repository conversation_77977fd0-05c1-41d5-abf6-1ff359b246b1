import 'package:carerez/blocs/complaints/complaints_bloc.dart';
import 'package:carerez/blocs/incident/incident_bloc.dart';
import 'package:carerez/blocs/staff/staff_bloc.dart';
import 'package:carerez/blocs/user/user_bloc.dart';
import 'package:carerez/routes/router_config.dart';
import 'package:carerez/services/complaints_service.dart';
import 'package:carerez/services/incident_service.dart';
import 'package:carerez/services/resident_finance_service.dart';
import 'package:carerez/services/resident_service.dart';
import 'package:carerez/services/staff_service.dart';
import 'package:carerez/services/unit_service.dart';
import 'package:carerez/services/user_service.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'blocs/auth/auth_bloc.dart';
import 'blocs/auth/auth_event.dart';
import 'blocs/finance/resident_budget_transaction_bloc.dart';
import 'blocs/finance/resident_finance_basic_bloc.dart';
import 'blocs/mar_sheet/mar_sheet_bloc.dart';
import 'blocs/notification/notification_bloc.dart';
import 'blocs/resident/resident_bloc.dart';
import 'blocs/unit/unit_bloc.dart';
import 'services/notification_service.dart';
import 'blocs/ticket/ticket_bloc.dart';
import 'services/ticket_service.dart';
import 'package:carerez/blocs/medication/medication_bloc.dart';
import 'package:carerez/services/medication_service.dart';
import 'package:carerez/repositories/user_repository.dart';

final RouteObserver<ModalRoute<void>> routeObserver = RouteObserver<ModalRoute<void>>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Load .env file first
  await dotenv.load(fileName: "assets/.env");

  final userService = UserService();
  final userRepository = UserRepository(userService);
  final complaintsService = ComplaintsService();
  final incidentService = IncidentService();
  final staffService = StaffService();

  final notificationService = NotificationService();
  await notificationService.initialize();

  final ticketService = TicketService(
    'your_auth_token_here',
  );

  runApp(MultiBlocProvider(
    providers: [
      BlocProvider(
        create: (context) => AuthBloc()..add(CheckAuthStatus()),
      ),
      BlocProvider(
        create: (context) => UserBloc(userRepository),
      ),
      BlocProvider(
        create: (context) => NotificationBloc(notificationService),
      ),
      BlocProvider(
        create: (context) => TicketBloc(ticketService),
      ),
      BlocProvider(
        create: (context) => StaffBloc(
          staffService,
        ),
      ),
      BlocProvider(
        create: (context) => ComplaintsBloc(
          complaintsService
        ),
      ),
      BlocProvider(
        create: (context) => IncidentBloc(
          incidentService
        )
      ),

      BlocProvider<ResidentBloc>(
        create: (context) => ResidentBloc(ResidentService()),
      ),
      BlocProvider<UnitBloc>(
        create: (context) => UnitBloc(UnitService()),
      ),
      BlocProvider<MedicationBloc>(
        create: (context) => MedicationBloc(MedicationService()),
      ),
      BlocProvider<ResidentBudgetTransactionBloc>(
        create: (context) => ResidentBudgetTransactionBloc(ResidentFinanceService()),
      ),
      BlocProvider<ResidentFinanceBasicBloc>(
        create: (context) => ResidentFinanceBasicBloc(ResidentFinanceService()),
      ),
      BlocProvider<MarSheetBloc>(
        create: (context) => MarSheetBloc(),
      ),
    ],
    child: MyApp(),
  ));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      routerConfig: router,
      title: 'Carerez',
      debugShowCheckedModeBanner: false,
    );
  }
}
