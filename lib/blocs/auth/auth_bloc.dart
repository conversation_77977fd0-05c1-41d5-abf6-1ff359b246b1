import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../services/http_client.dart';
import 'auth_event.dart';
import 'auth_state.dart';
import 'package:carerez/config/env_config.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final String _baseUrl = EnvConfig.apiBaseUrl;

  AuthBloc() : super(AuthInitial()) {
    on<SignInRequested>(_onSignInRequested);
    on<SignOutRequested>(_onSignOutRequested);
    on<CheckAuthStatus>(_onCheckAuthStatus);
  }

  // Add this method to check token
  Future<String?> getStoredToken() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString('access-token');
  }

  // 1. Sign In
  void _onSignInRequested(SignInRequested event, Emitter<AuthState> emit) async {

    emit(AuthLoading());
    try {
      final Map<String, String> requestBody = {
        'emailAddress': event.emailAddress.trim(),
        'password': event.password.trim()
      };
      
      
      final response = await HttpClientService.post(
        '$_baseUrl/tenant/login',
        body: jsonEncode(requestBody),
      );
      

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        
        if (data['success'] == true) {
          // Extract token from cookies
          final token = _extractTokenFromCookies(response.headers['set-cookie']);
          
          if (token != null) {
            await _setToken(token);
            
            // Verify token was stored
            final storedToken = await getStoredToken();

            final roles = data['roles'];
            String userType = 'user';
            if (roles != null && roles.isNotEmpty) {
              userType = roles[0].split(':')[0];
            }
            
            emit(AuthAuthenticated(token,userType));
          } else {
            emit(AuthError('Login failed: No token received'));
          }
        } else {
          emit(AuthError('Login failed: ${data['message']}'));
        }
      } else {
        final data = jsonDecode(response.body);
      
        emit(AuthError(data['message'] ?? 'Login failed'));
      }
    } catch (e) {
      emit(AuthError('An error occurred: ${e.toString()}'));
    }
  }

  // Helper method to extract token from cookies
  String? _extractTokenFromCookies(String? cookieHeader) {
    if (cookieHeader == null) return null;
    
    final cookieParts = cookieHeader.split(',');
    for (var part in cookieParts) {
      part = part.trim();
      if (part.startsWith('access-token=Bearer%20')) {
        // Extract the token and decode the URL encoding
        final token = part.split(';')[0].substring('access-token=Bearer%20'.length);
        return Uri.decodeComponent(token);
      }
    }
    return null;
  }

  // 3. Sign Out
  void _onSignOutRequested(
      SignOutRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      await _clearToken();
      emit(AuthInitial());
    } catch (e) {
      emit(AuthError('An error occurred during sign out: ${e.toString()}'));
    }
  }

  // 4. Check Auth Status
  void _onCheckAuthStatus(
      CheckAuthStatus event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('access-token');

      if (token != null && token.isNotEmpty) {
        emit(AuthAuthenticated(token));
      } else {
        emit(AuthInitial());
      }
    } catch (e) {
      emit(AuthError(
          'An error occurred while checking auth status: ${e.toString()}'));
    }
  }

  // Utility: Store token in SharedPreferences
  Future<void> _setToken(String token) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('access-token', token);
  }

  // Utility: Clear token from SharedPreferences
  Future<void> _clearToken() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('access-token');
  }
}
