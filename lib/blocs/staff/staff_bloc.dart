import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../models/staff.dart';
import '../../screens/utils/snackbar_utils.dart';
import '../../services/staff_service.dart';
import 'staff_event.dart';
import 'staff_state.dart';

class StaffBloc extends Bloc<StaffEvent, StaffState> {
  final StaffService _staffService;

  StaffBloc(this._staffService) : super(StaffInitial()) {
    on<FetchStaff>(_onFetchStaff);
    on<FetchStaffById>(_onFetchStaffById);
    on<CreateStaff>(_onCreateStaff);
    on<UpdateStaff>(_onUpdateStaff);
    on<DeleteStaff>(_onDeleteStaff);
    on<SearchStaff>(_onSearchStaff);
    on<FilterStaffByRole>(_onFilterStaffByRole);
    on<FilterStaffByStatus>(_onFilterStaffByStatus);
    on<FilterStaffByLocation>(_onFilterStaffByLocation);
    on<ResetStaffFilters>(_onResetStaffFilters);
  }

  Future<void> _onFetchStaff(
    FetchStaff event,
    Emitter<StaffState> emit,
  ) async {
    try {
      emit(StaffLoading());
      final staff = await _staffService.getStaff();

      if (staff.isEmpty) {
        emit(StaffEmpty());
      } else {
        emit(StaffLoaded(
          staff: staff,
          filteredStaff: staff,
        ));
      }
    } on AccessDeniedException {
      emit(StaffAccessDenied());
    } catch (e) {
      debugPrint('Error in StaffBloc._onFetchStaff: $e');
      emit(StaffError(e.toString()));
    }
  }

  Future<void> _onFetchStaffById(
    FetchStaffById event,
    Emitter<StaffState> emit,
  ) async {
    try {
      emit(StaffLoading());
      final staff = await _staffService.getStaffById(event.staffId);
      emit(StaffDetailLoaded(staff));
    } on AccessDeniedException {
      emit(StaffAccessDenied());
    } catch (e) {
      emit(StaffError(e.toString()));
    }
  }

  Future<void> _onCreateStaff(
    CreateStaff event,
    Emitter<StaffState> emit,
  ) async {
    try {
      final newStaff = await _staffService.createStaff(event.staff);

      final currentState = state;
      if (currentState is StaffLoaded) {
        final updatedStaff = [newStaff, ...currentState.staff];
        emit(StaffLoaded(
          staff: updatedStaff,
          filteredStaff: _applyFilters(
            updatedStaff,
            currentState.searchQuery,
            currentState.currentRole,
            currentState.currentActiveStatus,
            currentState.currentLocation,
          ),
          searchQuery: currentState.searchQuery,
          currentRole: currentState.currentRole,
          currentActiveStatus: currentState.currentActiveStatus,
          currentLocation: currentState.currentLocation,
        ));
      }

      emit(StaffCreated(newStaff));
    } on AccessDeniedException {
      emit(StaffAccessDenied());
    } catch (e) {
      emit(StaffError(e.toString()));
    }
  }

  Future<void> _onUpdateStaff(
    UpdateStaff event,
    Emitter<StaffState> emit,
  ) async {
    try {
      final updatedStaff = await _staffService.updateStaff(
        event.staffId,
        event.updates,
      );

      final currentState = state;
      if (currentState is StaffLoaded) {
        final updatedStaffList = currentState.staff.map((staff) {
          return staff.id == updatedStaff.id ? updatedStaff : staff;
        }).toList();

        emit(StaffLoaded(
          staff: updatedStaffList,
          filteredStaff: _applyFilters(
            updatedStaffList,
            currentState.searchQuery,
            currentState.currentRole,
            currentState.currentActiveStatus,
            currentState.currentLocation,
          ),
          searchQuery: currentState.searchQuery,
          currentRole: currentState.currentRole,
          currentActiveStatus: currentState.currentActiveStatus,
          currentLocation: currentState.currentLocation,
        ));
      }

      emit(StaffUpdated(updatedStaff));
    } on AccessDeniedException {
      emit(StaffAccessDenied());
    } catch (e) {
      emit(StaffError(e.toString()));
    }
  }

  Future<void> _onDeleteStaff(
    DeleteStaff event,
    Emitter<StaffState> emit,
  ) async {
    try {
      final success = await _staffService.deleteStaff(event.staffId);

      if (success) {
        final currentState = state;
        if (currentState is StaffLoaded) {
          final updatedStaffList = currentState.staff
              .where((staff) => staff.id != event.staffId)
              .toList();

          if (updatedStaffList.isEmpty) {
            emit(StaffEmpty());
          } else {
            emit(StaffLoaded(
              staff: updatedStaffList,
              filteredStaff: _applyFilters(
                updatedStaffList,
                currentState.searchQuery,
                currentState.currentRole,
                currentState.currentActiveStatus,
                currentState.currentLocation,
              ),
              searchQuery: currentState.searchQuery,
              currentRole: currentState.currentRole,
              currentActiveStatus: currentState.currentActiveStatus,
              currentLocation: currentState.currentLocation,
            ));
          }
        }

        emit(StaffDeleted(event.staffId));
      } else {
        emit(StaffError('Failed to delete staff'));
      }
    } on AccessDeniedException {
      emit(StaffAccessDenied());
    } catch (e) {
      emit(StaffError(e.toString()));
    }
  }

  void _onSearchStaff(
    SearchStaff event,
    Emitter<StaffState> emit,
  ) {
    final currentState = state;
    if (currentState is StaffLoaded) {
      final filteredStaff = _applyFilters(
        currentState.staff,
        event.query,
        currentState.currentRole,
        currentState.currentActiveStatus,
        currentState.currentLocation,
      );

      emit(StaffLoaded(
        staff: currentState.staff,
        filteredStaff: filteredStaff,
        searchQuery: event.query,
        currentRole: currentState.currentRole,
        currentActiveStatus: currentState.currentActiveStatus,
        currentLocation: currentState.currentLocation,
      ));
    }
  }

  void _onFilterStaffByRole(
    FilterStaffByRole event,
    Emitter<StaffState> emit,
  ) {
    final currentState = state;
    if (currentState is StaffLoaded) {
      final filteredStaff = _applyFilters(
        currentState.staff,
        currentState.searchQuery,
        event.role,
        currentState.currentActiveStatus,
        currentState.currentLocation,
      );

      emit(StaffLoaded(
        staff: currentState.staff,
        filteredStaff: filteredStaff,
        searchQuery: currentState.searchQuery,
        currentRole: event.role,
        currentActiveStatus: currentState.currentActiveStatus,
        currentLocation: currentState.currentLocation,
      ));
    }
  }

  void _onFilterStaffByStatus(
    FilterStaffByStatus event,
    Emitter<StaffState> emit,
  ) {
    final currentState = state;
    if (currentState is StaffLoaded) {
      final filteredStaff = _applyFilters(
        currentState.staff,
        currentState.searchQuery,
        currentState.currentRole,
        event.isActive,
        currentState.currentLocation,
      );

      emit(StaffLoaded(
        staff: currentState.staff,
        filteredStaff: filteredStaff,
        searchQuery: currentState.searchQuery,
        currentRole: currentState.currentRole,
        currentActiveStatus: event.isActive,
        currentLocation: currentState.currentLocation,
      ));
    }
  }

  void _onFilterStaffByLocation(
    FilterStaffByLocation event,
    Emitter<StaffState> emit,
  ) {
    final currentState = state;
    if (currentState is StaffLoaded) {
      final filteredStaff = _applyFilters(
        currentState.staff,
        currentState.searchQuery,
        currentState.currentRole,
        currentState.currentActiveStatus,
        event.location,
      );

      emit(StaffLoaded(
        staff: currentState.staff,
        filteredStaff: filteredStaff,
        searchQuery: currentState.searchQuery,
        currentRole: currentState.currentRole,
        currentActiveStatus: currentState.currentActiveStatus,
        currentLocation: event.location,
      ));
    }
  }

  void _onResetStaffFilters(
    ResetStaffFilters event,
    Emitter<StaffState> emit,
  ) {
    final currentState = state;
    if (currentState is StaffLoaded) {
      emit(StaffLoaded(
        staff: currentState.staff,
        filteredStaff: currentState.staff,
      ));
    }
  }

  List<Staff> _applyFilters(
    List<Staff> staff,
    String? searchQuery,
    String? role,
    bool? isActive,
    String? location,
  ) {
    List<Staff> filteredStaff = List.from(staff);

    // Apply search query filter
    if (searchQuery != null && searchQuery.isNotEmpty) {
      filteredStaff = filteredStaff.where((staff) {
        return staff.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
            staff.email.toLowerCase().contains(searchQuery.toLowerCase()) ||
            staff.phone.toLowerCase().contains(searchQuery.toLowerCase());
      }).toList();
    }

    // Apply role filter
    if (role != null && role.isNotEmpty) {
      filteredStaff = filteredStaff.where((staff) {
        return staff.role.toLowerCase() == role.toLowerCase();
      }).toList();
    }

    // Apply active status filter
    if (isActive != null) {
      filteredStaff = filteredStaff.where((staff) {
        return staff.active == isActive;
      }).toList();
    }

    // Apply location filter
    if (location != null && location.isNotEmpty) {
      filteredStaff = filteredStaff.where((staff) {
        return staff.location.toLowerCase() == location.toLowerCase();
      }).toList();
    }

    return filteredStaff;
  }
}
