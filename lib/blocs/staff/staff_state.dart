import 'package:equatable/equatable.dart';
import '../../models/staff.dart';

abstract class StaffState extends Equatable {
  const StaffState();

  @override
  List<Object?> get props => [];
}

class StaffInitial extends StaffState {}

class StaffLoading extends StaffState {}

class StaffLoaded extends StaffState {
  final List<Staff> staff;
  final List<Staff> filteredStaff;
  final String? searchQuery;
  final String? currentRole;
  final bool? currentActiveStatus;
  final String? currentLocation;

  const StaffLoaded({
    required this.staff,
    required this.filteredStaff,
    this.searchQuery,
    this.currentRole,
    this.currentActiveStatus,
    this.currentLocation,
  });

  @override
  List<Object?> get props => [
        staff,
        filteredStaff,
        searchQuery,
        currentRole,
        currentActiveStatus,
        currentLocation,
      ];
}

class StaffDetailLoaded extends StaffState {
  final Staff staff;

  const StaffDetailLoaded(this.staff);

  @override
  List<Object?> get props => [staff];
}

class StaffEmpty extends StaffState {}

class StaffError extends StaffState {
  final String message;

  const StaffError(this.message);

  @override
  List<Object?> get props => [message];
}

class StaffCreated extends StaffState {
  final Staff staff;

  const StaffCreated(this.staff);

  @override
  List<Object?> get props => [staff];
}

class StaffUpdated extends StaffState {
  final Staff staff;

  const StaffUpdated(this.staff);

  @override
  List<Object?> get props => [staff];
}

class StaffDeleted extends StaffState {
  final String staffId;

  const StaffDeleted(this.staffId);

  @override
  List<Object?> get props => [staffId];
}

class StaffAccessDenied extends StaffState {
  const StaffAccessDenied();

  @override
  List<Object?> get props => [];
}