import 'package:equatable/equatable.dart';
import '../../models/staff.dart';

abstract class StaffEvent extends Equatable {
  const StaffEvent();

  @override
  List<Object?> get props => [];
}

class FetchStaff extends StaffEvent {}

class FetchStaffById extends StaffEvent {
  final String staffId;

  const FetchStaffById(this.staffId);

  @override
  List<Object?> get props => [staffId];
}

class CreateStaff extends StaffEvent {
  final Staff staff;

  const CreateStaff(this.staff);

  @override
  List<Object?> get props => [staff];
}

class UpdateStaff extends StaffEvent {
  final String staffId;
  final Map<String, dynamic> updates;

  const UpdateStaff(this.staffId, this.updates);

  @override
  List<Object?> get props => [staffId, updates];
}

class DeleteStaff extends StaffEvent {
  final String staffId;

  const DeleteStaff(this.staffId);

  @override
  List<Object?> get props => [staffId];
}

class SearchStaff extends StaffEvent {
  final String query;

  const SearchStaff(this.query);

  @override
  List<Object?> get props => [query];
}

class FilterStaffByRole extends StaffEvent {
  final String role;

  const FilterStaffByRole(this.role);

  @override
  List<Object?> get props => [role];
}

class FilterStaffByStatus extends StaffEvent {
  final bool? isActive;

  const FilterStaffByStatus(this.isActive);

  @override
  List<Object?> get props => [isActive];
}

class FilterStaffByLocation extends StaffEvent {
  final String location;

  const FilterStaffByLocation(this.location);

  @override
  List<Object?> get props => [location];
}

class ResetStaffFilters extends StaffEvent {}
