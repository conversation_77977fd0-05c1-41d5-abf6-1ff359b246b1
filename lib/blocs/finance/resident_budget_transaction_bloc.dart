import 'package:bloc/bloc.dart';
import 'package:carerez/blocs/finance/resident_finance_event.dart';
import 'package:carerez/blocs/finance/resident_finance_state.dart';
import 'package:equatable/equatable.dart';
import 'package:carerez/models/resident_budget_transaction.dart';
import 'package:carerez/services/resident_finance_service.dart';

// Events

// States


// Bloc
class ResidentBudgetTransactionBloc extends Bloc<ResidentBudgetTransactionEvent, ResidentBudgetTransactionState> {
  final ResidentFinanceService _service;
  ResidentBudgetTransactionBloc(this._service) : super(ResidentBudgetTransactionInitial()) {
    on<FetchResidentBudgetTransactions>((event, emit) async {
      emit(ResidentBudgetTransactionLoading());
      try {
        final txs = await _service.getResidentBudgetTransactions(event.residentId);
        emit(ResidentBudgetTransactionLoaded(txs));
      } catch (e) {
        emit(ResidentBudgetTransactionError(e.toString()));
      }
    });
  }
}

