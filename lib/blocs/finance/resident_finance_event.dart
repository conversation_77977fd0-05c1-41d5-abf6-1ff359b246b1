import 'package:equatable/equatable.dart';

abstract class ResidentFinanceBasicEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class FetchResidentFinanceBasic extends ResidentFinanceBasicEvent {
  final String residentId;
  FetchResidentFinanceBasic(this.residentId);
  @override
  List<Object?> get props => [residentId];
}


abstract class ResidentBudgetTransactionEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class FetchResidentBudgetTransactions extends ResidentBudgetTransactionEvent {
  final String residentId;
  FetchResidentBudgetTransactions(this.residentId);
  @override
  List<Object?> get props => [residentId];
}
