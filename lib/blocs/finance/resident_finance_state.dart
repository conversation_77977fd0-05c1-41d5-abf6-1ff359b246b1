import 'package:equatable/equatable.dart';
import 'package:carerez/models/resident_finance_basic.dart';

import '../../models/resident_budget_transaction.dart';

abstract class ResidentFinanceBasicState extends Equatable {
  @override
  List<Object?> get props => [];
}

class ResidentFinanceBasicInitial extends ResidentFinanceBasicState {}
class ResidentFinanceBasicLoading extends ResidentFinanceBasicState {}
class ResidentFinanceBasicLoaded extends ResidentFinanceBasicState {
  final ResidentFinanceBasic financeBasic;
  ResidentFinanceBasicLoaded(this.financeBasic);
  @override
  List<Object?> get props => [financeBasic];
}
class ResidentFinanceBasicError extends ResidentFinanceBasicState {
  final String message;
  ResidentFinanceBasicError(this.message);
  @override
  List<Object?> get props => [message];
}

abstract class ResidentBudgetTransactionState extends Equatable {
  @override
  List<Object?> get props => [];
}

class ResidentBudgetTransactionInitial extends ResidentBudgetTransactionState {}
class ResidentBudgetTransactionLoading extends ResidentBudgetTransactionState {}
class ResidentBudgetTransactionLoaded extends ResidentBudgetTransactionState {
  final List<ResidentBudgetTransaction> transactions;
  ResidentBudgetTransactionLoaded(this.transactions);
  @override
  List<Object?> get props => [transactions];
}
class ResidentBudgetTransactionError extends ResidentBudgetTransactionState {
  final String message;
  ResidentBudgetTransactionError(this.message);
  @override
  List<Object?> get props => [message];
}