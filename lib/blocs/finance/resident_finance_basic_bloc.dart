import 'package:bloc/bloc.dart';
import 'package:carerez/blocs/finance/resident_finance_event.dart';
import 'package:carerez/blocs/finance/resident_finance_state.dart';
import 'package:equatable/equatable.dart';
import 'package:carerez/models/resident_finance_basic.dart';
import 'package:carerez/services/resident_finance_service.dart';

// Bloc
class ResidentFinanceBasicBloc extends Bloc<ResidentFinanceBasicEvent, ResidentFinanceBasicState> {
  final ResidentFinanceService _service;
  ResidentFinanceBasicBloc(this._service) : super(ResidentFinanceBasicInitial()) {
    on<FetchResidentFinanceBasic>((event, emit) async {
      emit(ResidentFinanceBasicLoading());
      try {
        final data = await _service.getResidentFinanceBasic(event.residentId);
        emit(ResidentFinanceBasicLoaded(data));
      } catch (e) {
        emit(ResidentFinanceBasicError(e.toString()));
      }
    });
  }
}

