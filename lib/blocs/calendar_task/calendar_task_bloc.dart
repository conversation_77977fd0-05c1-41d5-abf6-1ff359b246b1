import 'package:flutter_bloc/flutter_bloc.dart';
import '../../models/assign_task.dart';
import '../../services/calendar_service.dart';
import 'calendar_task_event.dart';
import 'calendar_task_state.dart';

class CalendarTaskBloc extends Bloc<CalendarTaskEvent, CalendarTaskState> {

  CalendarTaskBloc() : super(CalendarTaskInitial()) {
    on<FetchCalendarTasks>(_onFetchCalendarTasks);
    on<CreateCalendarTask>(_onCreateCalendarTask);
    on<UpdateCalendarTask>(_onUpdateCalendarTask);
    on<DeleteCalendarTask>(_onDeleteCalendarTask);
    on<ToggleTaskCompletion>(_onToggleTaskCompletion);
    // on<FilterCalendarTasks>(_onFilterCalendarTasks);
  }

  Future<void> _onFetchCalendarTasks(
    FetchCalendarTasks event,
    Emitter<CalendarTaskState> emit,
  ) async {
    try {
      emit(CalendarTaskLoading());
      final tasks = await CalendarService().getCalendarTasks(event.residentId);
      emit(CalendarTaskLoaded(
        tasks: tasks,
        filteredTasks: tasks,
      ));
    } catch (e) {
      emit(CalendarTaskError(e.toString()));
    }
  }

  Future<void> _onCreateCalendarTask(
    CreateCalendarTask event,
    Emitter<CalendarTaskState> emit,
  ) async {
    try {
      await CalendarService.addCalendarTask(event.task);

      final currentState = state;
      if (currentState is CalendarTaskLoaded) {
        final updatedTasks = [event.task, ...currentState.tasks];
        emit(CalendarTaskLoaded(
          tasks: updatedTasks,
          filteredTasks: _applyFilters(
            updatedTasks,
            currentState.selectedDate,
            currentState.selectedType,
            currentState.showCompleted,
          ),
          selectedDate: currentState.selectedDate,
          selectedType: currentState.selectedType,
          showCompleted: currentState.showCompleted,
        ));
      }

      emit(CalendarTaskCreated(event.task));
    } catch (e) {
      emit(CalendarTaskError(e.toString()));
    }
  }

  Future<void> _onUpdateCalendarTask(
    UpdateCalendarTask event,
    Emitter<CalendarTaskState> emit,
  ) async {
    try {
      await CalendarService.updateCalendarTask(event.task);

      final currentState = state;
      if (currentState is CalendarTaskLoaded) {
        final updatedTasks = currentState.tasks.map((task) {
          return task.assignTaskId == event.task.assignTaskId ? event.task : task;
        }).toList();

        emit(CalendarTaskLoaded(
          tasks: updatedTasks,
          filteredTasks: _applyFilters(
            updatedTasks,
            currentState.selectedDate,
            currentState.selectedType,
            currentState.showCompleted,
          ),
          selectedDate: currentState.selectedDate,
          selectedType: currentState.selectedType,
          showCompleted: currentState.showCompleted,
        ));
      }

      emit(CalendarTaskUpdated(event.task));
    } catch (e) {
      emit(CalendarTaskError(e.toString()));
    }
  }

  Future<void> _onDeleteCalendarTask(
    DeleteCalendarTask event,
    Emitter<CalendarTaskState> emit,
  ) async {
    try {
      await CalendarService.deleteCalendarTask(event.taskId);

      final currentState = state;
      if (currentState is CalendarTaskLoaded) {
        final updatedTasks = currentState.tasks
            .where((task) => task.assignTaskId != event.taskId)
            .toList();

        emit(CalendarTaskLoaded(
          tasks: updatedTasks,
          filteredTasks: _applyFilters(
            updatedTasks,
            currentState.selectedDate,
            currentState.selectedType,
            currentState.showCompleted,
          ),
          selectedDate: currentState.selectedDate,
          selectedType: currentState.selectedType,
          showCompleted: currentState.showCompleted,
        ));
      }

      emit(CalendarTaskDeleted(event.taskId));
    } catch (e) {
      emit(CalendarTaskError(e.toString()));
    }
  }

  Future<void> _onToggleTaskCompletion(
    ToggleTaskCompletion event,
    Emitter<CalendarTaskState> emit,
  ) async {
    emit(CalendarTaskError('Toggle completion is not supported for AssignTask.'));
  }

  // void _onFilterCalendarTasks(
  //   FilterCalendarTasks event,
  //   Emitter<CalendarTaskState> emit,
  // ) {
  //   final currentState = state;
  //   if (currentState is CalendarTaskLoaded) {
  //     final filteredTasks = _applyFilters(
  //       currentState.tasks,
  //       event.date,
  //       event.type,
  //       event.showCompleted,
  //     );
  //
  //     emit(CalendarTaskLoaded(
  //       tasks: currentState.tasks,
  //       filteredTasks: filteredTasks,
  //       selectedDate: event.date,
  //       selectedType: event.type,
  //       showCompleted: event.showCompleted ?? currentState.showCompleted,
  //     ));
  //   }
  // }

  List<AssignTask> _applyFilters(
    List<AssignTask> tasks,
    DateTime? date,
    String? type,
    bool? showCompleted,
  ) {
    return tasks.where((assignTask) {
      if (date != null) {
        final taskDate = assignTask.date;
        if (taskDate.year != date.year ||
            taskDate.month != date.month ||
            taskDate.day != date.day) {
          return false;
        }
      }
      if (type != null && type.isNotEmpty && assignTask.taskColor != type) {
        return false;
      }
      // No isCompleted in AssignTask, so skip that filter
      return true;
    }).toList();
  }
}
