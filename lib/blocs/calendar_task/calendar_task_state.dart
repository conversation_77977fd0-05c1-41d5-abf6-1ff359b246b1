import 'package:carerez/models/assign_task.dart';
import 'package:equatable/equatable.dart';
import '../../models/calendar_task.dart';

abstract class CalendarTaskState extends Equatable {
  const CalendarTaskState();
  
  @override
  List<Object> get props => [];
}

class CalendarTaskInitial extends CalendarTaskState {}

class CalendarTaskLoading extends CalendarTaskState {}

class CalendarTaskLoaded extends CalendarTaskState {
  final List<AssignTask> tasks;
  final List<AssignTask> filteredTasks;
  final DateTime? selectedDate;
  final String? selectedType;
  final bool showCompleted;

  const CalendarTaskLoaded({
    required this.tasks,
    required this.filteredTasks,
    this.selectedDate,
    this.selectedType,
    this.showCompleted = true,
  });

  @override
  List<Object> get props => [
    tasks,
    filteredTasks,
    selectedDate ?? DateTime(0),
    selectedType ?? '',
    showCompleted,
  ];
}

class CalendarTaskCreated extends CalendarTaskState {
  final AssignTask task;

  const CalendarTaskCreated(this.task);

  @override
  List<Object> get props => [task];
}

class CalendarTaskUpdated extends CalendarTaskState {
  final AssignTask task;

  const CalendarTaskUpdated(this.task);

  @override
  List<Object> get props => [task];
}

class CalendarTaskDeleted extends CalendarTaskState {
  final String taskId;

  const CalendarTaskDeleted(this.taskId);

  @override
  List<Object> get props => [taskId];
}

class CalendarTaskError extends CalendarTaskState {
  final String message;

  const CalendarTaskError(this.message);

  @override
  List<Object> get props => [message];
}