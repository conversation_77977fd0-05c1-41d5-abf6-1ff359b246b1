import 'package:equatable/equatable.dart';
import '../../models/assign_task.dart';

abstract class CalendarTaskEvent extends Equatable {
  const CalendarTaskEvent();

  @override
  List<Object> get props => [];
}

class FetchCalendarTasks extends CalendarTaskEvent {
  final String residentId;

  const FetchCalendarTasks(this.residentId);

  @override
  List<Object> get props => [residentId];
}

class CreateCalendarTask extends CalendarTaskEvent {
  final AssignTask task;

  const CreateCalendarTask(this.task);

  @override
  List<Object> get props => [task];
}

class UpdateCalendarTask extends CalendarTaskEvent {
  final AssignTask task;

  const UpdateCalendarTask(this.task);

  @override
  List<Object> get props => [task];
}

class DeleteCalendarTask extends CalendarTaskEvent {
  final String taskId;

  const DeleteCalendarTask(this.taskId);

  @override
  List<Object> get props => [taskId];
}

class ToggleTaskCompletion extends CalendarTaskEvent {
  final String taskId;

  const ToggleTaskCompletion(this.taskId);

  @override
  List<Object> get props => [taskId];
}

// class FilterCalendarTasks extends CalendarTaskEvent {
//   final DateTime? date;
//   final String? type;
//   final bool? showCompleted;
//
//   const FilterCalendarTasks({
//     this.date,
//     this.type,
//     this.showCompleted,
//   });
//
//   @override
//   List<Object?> get props => [date, type, showCompleted];
// }