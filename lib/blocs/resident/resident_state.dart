import 'package:carerez/models/diet_plan.dart';
import 'package:carerez/models/medicalTreatment.dart';
import 'package:equatable/equatable.dart';
import '../../models/emergency_contact.dart';
import '../../models/meal.dart';
import '../../models/resident.dart';
import '../../models/user_model.dart';

abstract class ResidentState extends Equatable {
  const ResidentState();

  @override
  List<Object?> get props => [];
}

class ResidentInitial extends ResidentState {}

class ResidentLoading extends ResidentState {}

class ResidentLoaded extends ResidentState {
  final List<Resident> residents;
  final List<Resident> filteredResidents;
  final String? currentStatus;
  final String? currentCareLevel;
  final String? searchQuery;

  const ResidentLoaded({
    required this.residents,
    required this.filteredResidents,
    this.currentStatus,
    this.currentCareLevel,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [
        residents,
        filteredResidents,
        currentStatus,
        currentCareLevel,
        searchQuery,
      ];
}

class ResidentDetailLoaded extends ResidentState {
  final Resident resident;

  const ResidentDetailLoaded(this.resident);

  @override
  List<Object> get props => [resident];
}

class ResidentEmpty extends ResidentState {}

class ResidentError extends ResidentState {
  final String message;

  const ResidentError(this.message);

  @override
  List<Object> get props => [message];
}

class ResidentNotFound extends ResidentState {
  final String message;

  const ResidentNotFound(this.message);

  @override
  List<Object> get props => [message];
}

class ResidentMedicalHistoryLoaded extends ResidentState {
  final String residentId;
  final List<MedicalTreatment> medicalHistory;

  const ResidentMedicalHistoryLoaded({
    required this.residentId,
    required this.medicalHistory,
  });

  @override
  List<Object> get props => [residentId, medicalHistory];
}

class ResidentMedicalHistoryUpdated extends ResidentState {
  final String residentId;
  final List<Map<String, dynamic>> medicalHistory;

  const ResidentMedicalHistoryUpdated({
    required this.residentId,
    required this.medicalHistory,
  });

  @override
  List<Object> get props => [residentId, medicalHistory];
}

class ResidentCarePlanLoaded extends ResidentState {
  final String residentId;
  final List<MedicalTreatment> carePlan;

  const ResidentCarePlanLoaded({
    required this.residentId,
    required this.carePlan,
  });

  @override
  List<Object> get props => [residentId, carePlan];
}

class ResidentCarePlanUpdated extends ResidentState {
  final String residentId;
  final List<Map<String, dynamic>> carePlan;

  const ResidentCarePlanUpdated({
    required this.residentId,
    required this.carePlan,
  });

  @override
  List<Object> get props => [residentId, carePlan];
}

class ResidentContactInfoLoaded extends ResidentState {
  final String residentId;
  final List<EmergencyContact> contactInfo;

  const ResidentContactInfoLoaded({
    required this.residentId,
    required this.contactInfo,
  });

  @override
  List<Object> get props => [residentId, contactInfo];
}

class ResidentSearchResults extends ResidentState {
  final List<Resident> searchResults;

  const ResidentSearchResults(this.searchResults);

  @override
  List<Object> get props => [searchResults];
}

class ResidentFilterStatusUpdated extends ResidentState {
  final String? status;

  const ResidentFilterStatusUpdated(this.status);

  @override
  List<Object?> get props => [status];
}


// on<FetchResidents>(_onFetchResidents);
// on<FetchResidentById>(_onFetchResidentById);
// on<SearchResidents>(_onSearchResidents);
// on<FilterResidentsByStatus>(_onFilterResidentsByStatus);
// on<FilterResidentsByCareLevel>(_onFilterResidentsByCareLevel);
// on<FetchResidentsByUnit>(_onFetchResidentsByUnit);
// on<SearchResidentsByUnit>(_onSearchResidentsByUnit);
// on<FetchResidentMedicalHistory>(_onFetchResidentMedicalHistory);
// on<FetchResidentContactInformation>(_onFetchResidentContactInformation);
// on<FetchResidentFamilyInformation>(_onFetchResidentFamilyInformation);
// on<FetchResidentBudget>(_onFetchResidentBudget);
// on<FetchResidentServiceProvider>(_onFetchResidentServiceProvider);

class ResidentFamilyInformationLoaded extends ResidentState {
  final String residentId;
  final List<FamilyDetail> familyInformation;

  const ResidentFamilyInformationLoaded({
    required this.residentId,
    required this.familyInformation,
  });

  @override
  List<Object> get props => [residentId, familyInformation];
}

class ResidentFamilyInformationUpdated extends ResidentState {
  final String residentId;
  final List<FamilyDetail> familyInformation;

  const ResidentFamilyInformationUpdated({
    required this.residentId,
    required this.familyInformation,
  });

  @override
  List<Object> get props => [residentId, familyInformation];
}

class ResidentBudgetLoaded extends ResidentState {
  final String residentId;
  final ResidentBudget budget;

  const ResidentBudgetLoaded({
    required this.residentId,
    required this.budget,
  });

  @override
  List<Object> get props => [residentId, budget];
}

class ResidentBudgetUpdated extends ResidentState {
  final String residentId;
  final ResidentBudget budget;

  const ResidentBudgetUpdated({
    required this.residentId,
    required this.budget,
  });

  @override
  List<Object> get props => [residentId, budget];
}

class ResidentServiceProviderLoaded extends ResidentState {
  final String residentId;
  final ServiceProviderResident serviceProvider;

  const ResidentServiceProviderLoaded({
    required this.residentId,
    required this.serviceProvider,
  });

  @override
  List<Object> get props => [residentId, serviceProvider];
}

class ResidentServiceProviderUpdated extends ResidentState {
  final String residentId;
  final ServiceProviderResident serviceProvider;

  const ResidentServiceProviderUpdated({
    required this.residentId,
    required this.serviceProvider,
  });

  @override
  List<Object> get props => [residentId, serviceProvider];
}

class ResidentDocumentsLoaded extends ResidentState {
  final String residentId;
  final List<String> documents;

  const ResidentDocumentsLoaded({
    required this.residentId,
    required this.documents,
  });

  @override
  List<Object> get props => [residentId, documents];
}

class ResidentDocumentsUpdated extends ResidentState {
  final String residentId;
  final List<String> documents;

  const ResidentDocumentsUpdated({
    required this.residentId,
    required this.documents,
  });

  @override
  List<Object> get props => [residentId, documents];
}

class ResidentOverviewLoaded extends ResidentState {
  final Resident resident;
  final ResidentBudget? budget;
  final ServiceProviderResident? serviceProvider;
  final List<FamilyDetail>? familyInformation;
  final List<EmergencyContact>? contactInfo;

  const ResidentOverviewLoaded({
    required this.resident,
    this.budget,
    this.serviceProvider,
    this.familyInformation,
    this.contactInfo,
  });

  @override
  List<Object?> get props => [
    resident,
    budget,
    serviceProvider,
    familyInformation,
    contactInfo,
  ];
}

class ResidentDietPlanLoaded extends ResidentState {
  final String residentId;
  final List<DietPlan> dietPlan;
  final Map<String,List<Meal>> mealsByDietPlan;

  const ResidentDietPlanLoaded({
    required this.residentId,
    required this.dietPlan,
    required this.mealsByDietPlan,
  });

  @override
  List<Object> get props => [residentId, dietPlan];
}