import 'package:carerez/models/medicalTreatment.dart';
import 'package:equatable/equatable.dart';
import '../../models/resident.dart';

abstract class ResidentEvent extends Equatable {
  const ResidentEvent();

  @override
  List<Object?> get props => [];
}

class FetchResidents extends ResidentEvent {}

class FetchResidentById extends ResidentEvent {
  final String residentId;

  const FetchResidentById(this.residentId);

  @override
  List<Object> get props => [residentId];
}

class SearchResidents extends ResidentEvent {
  final String query;

  const SearchResidents(this.query);

  @override
  List<Object> get props => [query];
}

class FilterResidentsByStatus extends ResidentEvent {
  final String status;

  const FilterResidentsByStatus(this.status);

  @override
  List<Object> get props => [status];
}

class FilterResidentsByCareLevel extends ResidentEvent {
  final String careLevel;

  const FilterResidentsByCareLevel(this.careLevel);

  @override
  List<Object> get props => [careLevel];
}

class FetchResidentsByUnit extends ResidentEvent {
  final String unitId;

  FetchResidentsByUnit(this.unitId);
}

class SearchResidentsByUnit extends ResidentEvent {
  final String unitId;
  final String query;

  SearchResidentsByUnit(this.unitId, this.query);
}

class FetchResidentMedicalHistory extends ResidentEvent {
  final String residentId;

  const FetchResidentMedicalHistory(this.residentId);

  @override
  List<Object> get props => [residentId];
}

class UpdateResidentMedicalHistory extends ResidentEvent {
  final String residentId;
  final List<MedicalTreatment> medicalHistory;

  const UpdateResidentMedicalHistory(this.residentId, this.medicalHistory);

  @override
  List<Object> get props => [residentId, medicalHistory];
}


class FetchResidentCarePlan extends ResidentEvent {
  final String residentId;

  const FetchResidentCarePlan(this.residentId);

  @override
  List<Object> get props => [residentId];
}

class UpdateResidentCarePlan extends ResidentEvent {
  final String residentId;
  final List<MedicalTreatment> carePlan;

  const UpdateResidentCarePlan(this.residentId, this.carePlan);

  @override
  List<Object> get props => [residentId, carePlan];
}

class FetchResidentContactInfo extends ResidentEvent {
  final String residentId;

  const FetchResidentContactInfo(this.residentId);

  @override
  List<Object> get props => [residentId];
}
class UpdateResidentContactInfo extends ResidentEvent {
  final String residentId;
  final String phone;
  final String email;

  const UpdateResidentContactInfo(this.residentId, this.phone, this.email);

  @override
  List<Object> get props => [residentId, phone, email];
}

class FetchResidentFamilyInformation extends ResidentEvent {
  final String residentId;

  const FetchResidentFamilyInformation(this.residentId);

  @override
  List<Object> get props => [residentId];
}

class UpdateResidentFamilyInfo extends ResidentEvent {
  final String residentId;
  final String familyMemberName;
  final String relation;
  final String phone;

  const UpdateResidentFamilyInfo(this.residentId, this.familyMemberName, this.relation, this.phone);

  @override
  List<Object> get props => [residentId, familyMemberName, relation, phone];
}

class FetchResidentEmergencyContact extends ResidentEvent {
  final String residentId;

  const FetchResidentEmergencyContact(this.residentId);

  @override
  List<Object> get props => [residentId];
}

class UpdateResidentEmergencyContact extends ResidentEvent {
  final String residentId;
  final String emergencyContactName;
  final String relation;
  final String phone;

  const UpdateResidentEmergencyContact(this.residentId, this.emergencyContactName, this.relation, this.phone);

  @override
  List<Object> get props => [residentId, emergencyContactName, relation, phone];
}

class FetchResidentDocuments extends ResidentEvent {
  final String residentId;

  const FetchResidentDocuments(this.residentId);

  @override
  List<Object> get props => [residentId];
}

class UpdateResidentDocuments extends ResidentEvent {
  final String residentId;
  final List<String> documents;

  const UpdateResidentDocuments(this.residentId, this.documents);

  @override
  List<Object> get props => [residentId, documents];
}

class FetchResidentBudget extends ResidentEvent {
  final String residentId;

  const FetchResidentBudget(this.residentId);

  @override
  List<Object> get props => [residentId];
}

class UpdateResidentBudget extends ResidentEvent {
  final String residentId;
  final double budget;

  const UpdateResidentBudget(this.residentId, this.budget);

  @override
  List<Object> get props => [residentId, budget];
}

class FetchResidentServiceProvider extends ResidentEvent {
  final String residentId;

  const FetchResidentServiceProvider(this.residentId);

  @override
  List<Object> get props => [residentId];
}

class UpdateResidentServiceProvider extends ResidentEvent {
  final String residentId;
  final String serviceProvider;

  const UpdateResidentServiceProvider(this.residentId, this.serviceProvider);

  @override
  List<Object> get props => [residentId, serviceProvider];
}


class FetchResidentContactInformation extends ResidentEvent {
  final String residentId;

  const FetchResidentContactInformation(this.residentId);

  @override
  List<Object> get props => [residentId];
}

class FetchResidentOverview extends ResidentEvent {
  final String residentUUID;
  final String residentId;

  const FetchResidentOverview(this.residentId, this.residentUUID);

  @override
  List<Object> get props => [residentId, residentUUID];
}

class FetchResidentDietPlan extends ResidentEvent {
  final String residentId;

  const FetchResidentDietPlan(this.residentId);

  @override
  List<Object> get props => [residentId];
}
