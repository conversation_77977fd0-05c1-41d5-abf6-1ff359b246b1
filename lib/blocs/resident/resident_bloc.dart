import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../models/emergency_contact.dart';
import '../../models/meal.dart';
import '../../models/resident.dart';
import '../../services/resident_service.dart';
import '../../models/user_model.dart';
import 'resident_event.dart';
import 'resident_state.dart';

class ResidentBloc extends Bloc<ResidentEvent, ResidentState> {
  final ResidentService _residentService;

  ResidentBloc(this._residentService) : super(ResidentInitial()) {
    on<FetchResidents>(_onFetchResidents);
    on<FetchResidentById>(_onFetchResidentById);
    on<SearchResidents>(_onSearchResidents);
    on<FilterResidentsByStatus>(_onFilterResidentsByStatus);
    on<FilterResidentsByCareLevel>(_onFilterResidentsByCareLevel);
    on<FetchResidentsByUnit>(_onFetchResidentsByUnit);
    on<SearchResidentsByUnit>(_onSearchResidentsByUnit);
    on<FetchResidentMedicalHistory>(_onFetchResidentMedicalHistory);
    on<FetchResidentContactInformation>(_onFetchResidentContactInformation);
    on<FetchResidentFamilyInformation>(_onFetchResidentFamilyInformation);
    on<FetchResidentBudget>(_onFetchResidentBudget);
    on<FetchResidentServiceProvider>(_onFetchResidentServiceProvider);
    on<FetchResidentOverview>(_onFetchResidentOverview);
    on<FetchResidentDietPlan>(_onFetchResidentDietPlan);
  }

  Future<void> _onFetchResidents(
    FetchResidents event,
    Emitter<ResidentState> emit,
  ) async {
    try {
      emit(ResidentLoading());
      final residents = await _residentService.getResidents();
      debugPrint('Fetched residents: $residents');
      if (residents.isEmpty) {
        emit(ResidentEmpty());
      } else {
        emit(ResidentLoaded(
          residents: residents,
          filteredResidents: residents,
        ));
      }
    } catch (e) {
      emit(ResidentError(e.toString()));
    }
  }

  Future<void> _onFetchResidentById(
    FetchResidentById event,
    Emitter<ResidentState> emit,
  ) async {
    try {
      emit(ResidentLoading());
      final resident = await _residentService.getResidentById(event.residentId);
      emit(ResidentDetailLoaded(resident));
    } catch (e) {
      emit(ResidentError(e.toString()));
    }
  }

  void _onSearchResidents(
    SearchResidents event,
    Emitter<ResidentState> emit,
  ) {
    final currentState = state;
    if (currentState is ResidentLoaded) {
      final filteredResidents = _applyFilters(
        currentState.residents,
        currentState.currentStatus,
        currentState.currentCareLevel,
        event.query,
      );

      emit(ResidentLoaded(
        residents: currentState.residents,
        filteredResidents: filteredResidents,
        currentStatus: currentState.currentStatus,
        currentCareLevel: currentState.currentCareLevel,
        searchQuery: event.query,
      ));
    }
  }

  void _onFilterResidentsByStatus(
    FilterResidentsByStatus event,
    Emitter<ResidentState> emit,
  ) {
    final currentState = state;
    if (currentState is ResidentLoaded) {
      final filteredResidents = _applyFilters(
        currentState.residents,
        event.status,
        currentState.currentCareLevel,
        currentState.searchQuery,
      );

      emit(ResidentLoaded(
        residents: currentState.residents,
        filteredResidents: filteredResidents,
        currentStatus: event.status,
        currentCareLevel: currentState.currentCareLevel,
        searchQuery: currentState.searchQuery,
      ));
    }
  }

  void _onFilterResidentsByCareLevel(
    FilterResidentsByCareLevel event,
    Emitter<ResidentState> emit,
  ) {
    final currentState = state;
    if (currentState is ResidentLoaded) {
      final filteredResidents = _applyFilters(
        currentState.residents,
        currentState.currentStatus,
        event.careLevel,
        currentState.searchQuery,
      );

      emit(ResidentLoaded(
        residents: currentState.residents,
        filteredResidents: filteredResidents,
        currentStatus: currentState.currentStatus,
        currentCareLevel: event.careLevel,
        searchQuery: currentState.searchQuery,
      ));
    }
  }

  List<Resident> _applyFilters(
    List<Resident> residents,
    String? status,
    String? careLevel,
    String? searchQuery,
  ) {
    return residents.where((resident) {
      bool matchesStatus = status == null || resident.status == status;
      bool matchesCareLevel =
          careLevel == null || resident.careLevel == careLevel;
      bool matchesSearch = searchQuery == null ||
          searchQuery.isEmpty ||
          resident.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
          resident.id.toLowerCase().contains(searchQuery.toLowerCase());

      return matchesStatus && matchesCareLevel && matchesSearch;
    }).toList();
  }

  Future<void> _onFetchResidentsByUnit(
    FetchResidentsByUnit event,
    Emitter<ResidentState> emit,
  ) async {
    try {
      emit(ResidentLoading());
      final residents = await _residentService.getResidentsByUnit(event.unitId);

      if (residents.isEmpty) {
        emit(ResidentEmpty());
      } else {
        emit(ResidentLoaded(
          residents: residents,
          filteredResidents: residents,
        ));
      }
    } catch (e) {
      emit(ResidentError(e.toString()));
    }
  }

  Future<void> _onSearchResidentsByUnit(
    SearchResidentsByUnit event,
    Emitter<ResidentState> emit,
  ) async {
    if (state is ResidentLoaded) {
      final currentState = state as ResidentLoaded;

      if (event.query.isEmpty) {
        // If search query is empty, reload all residents for the unit
        add(FetchResidentsByUnit(event.unitId));
      } else {
        // Filter residents by search query
        final filteredResidents = currentState.residents.where((resident) {
          return resident.name
                  .toLowerCase()
                  .contains(event.query.toLowerCase()) ||
              resident.userCode
                  .toLowerCase()
                  .contains(event.query.toLowerCase()) ||
              resident.phone.toLowerCase().contains(event.query.toLowerCase());
        }).toList();

        if (filteredResidents.isEmpty) {
          emit(ResidentEmpty());
        } else {
          emit(ResidentLoaded(
            residents: currentState.residents,
            filteredResidents: filteredResidents,
          ));
        }
      }
    }
  }

  Future<void> _onFetchResidentMedicalHistory(
    FetchResidentMedicalHistory event,
    Emitter<ResidentState> emit,
  ) async {
    try {
      emit(ResidentLoading());
      final medicalHistory = await _residentService.getResidentMedicalHistory(event.residentId);
      emit(ResidentMedicalHistoryLoaded(residentId:event.residentId,medicalHistory: medicalHistory));
    } catch (e) {
      emit(ResidentError(e.toString()));
    }
  }

  Future<void> _onFetchResidentContactInformation(
      FetchResidentContactInformation event,
    Emitter<ResidentState> emit,
  ) async {
    try {
      emit(ResidentLoading());
      final contactInfo = await _residentService.getResidentContactInformation(event.residentId);
      emit(ResidentContactInfoLoaded(residentId:event.residentId,contactInfo: contactInfo));
    } catch (e) {
      emit(ResidentError(e.toString()));
    }
  }

  Future<void> _onFetchResidentFamilyInformation(
    FetchResidentFamilyInformation event,
    Emitter<ResidentState> emit,
  ) async {
    try {
      emit(ResidentLoading());
      final familyInfo = await _residentService.getResidentFamilyInformation(event.residentId);
      emit(ResidentFamilyInformationLoaded(residentId:event.residentId, familyInformation: familyInfo));
    } catch (e) {
      emit(ResidentError(e.toString()));
    }
  }

  Future<void> _onFetchResidentBudget(
    FetchResidentBudget event,
    Emitter<ResidentState> emit,
  ) async {
    try {
      emit(ResidentLoading());
      final budget = await _residentService.getResidentBudget(event.residentId);
      emit(ResidentBudgetLoaded(residentId:event.residentId,budget: budget));
    } catch (e) {
      emit(ResidentError(e.toString()));
    }
  }

  Future<void> _onFetchResidentServiceProvider(
    FetchResidentServiceProvider event,
    Emitter<ResidentState> emit,
  ) async {
    try {
      emit(ResidentLoading());
      final serviceProvider = await _residentService.getResidentServiceProvider(event.residentId);
      emit(ResidentServiceProviderLoaded(residentId:event.residentId,serviceProvider: serviceProvider));
    } catch (e) {
      emit(ResidentError(e.toString()));
    }
  }

  Future<void> _onFetchResidentOverview(
    FetchResidentOverview event,
    Emitter<ResidentState> emit,
  ) async {
    try {
      emit(ResidentLoading());
      final residentFuture = _residentService.getResidentById(event.residentUUID);
      final budgetFuture = _residentService.getResidentBudget(event.residentId);
      final serviceProviderFuture = _residentService.getResidentServiceProvider(event.residentId);
      final familyInfoFuture = _residentService.getResidentFamilyInformation(event.residentId);
      final contactInfoFuture = _residentService.getResidentContactInformation(event.residentId);

      final results = await Future.wait([
        residentFuture,
        budgetFuture,
        serviceProviderFuture,
        familyInfoFuture,
        contactInfoFuture,
      ]);

      emit(ResidentOverviewLoaded(
        resident: results[0] as Resident,
        budget: results[1] as ResidentBudget?,
        serviceProvider: results[2] as ServiceProviderResident?,
        familyInformation: results[3] as List<FamilyDetail>?,
        contactInfo: results[4] as List<EmergencyContact>?,
      ));
    } catch (e) {
      emit(ResidentError(e.toString()));
    }
  }

  Future<void> _onFetchResidentDietPlan(
    FetchResidentDietPlan event,
    Emitter<ResidentState> emit,
  ) async {
    try {
      emit(ResidentLoading());
      final dietPlan = await _residentService.getResidentDietPlans(event.residentId);
      final mealsMap = <String, List<Meal>>{};
      await Future.wait(dietPlan.map((plan) async {
        final meals = await _residentService.getDietPlanMeals(plan.dietPlanId);
        mealsMap[plan.dietPlanId] = meals;
      }));
      emit(ResidentDietPlanLoaded(residentId: event.residentId, dietPlan: dietPlan, mealsByDietPlan: mealsMap));
    } catch (e) {
      emit(ResidentError(e.toString()));
    }
  }


}
