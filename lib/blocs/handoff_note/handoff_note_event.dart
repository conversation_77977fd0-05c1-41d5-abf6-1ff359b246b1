import 'package:equatable/equatable.dart';
import '../../models/handoff_note.dart';

abstract class HandoffNoteEvent extends Equatable {
  const HandoffNoteEvent();

  @override
  List<Object?> get props => [];
}

class FetchHandoffNotes extends HandoffNoteEvent {
  final String? residentId;

  const FetchHandoffNotes({this.residentId});

  @override
  List<Object?> get props => [residentId];
}

class FetchHandoffNoteById extends HandoffNoteEvent {
  final String noteId;

  const FetchHandoffNoteById(this.noteId);

  @override
  List<Object?> get props => [noteId];
}

class CreateHandoffNote extends HandoffNoteEvent {
  final HandoffNote note;

  const CreateHandoffNote(this.note);

  @override
  List<Object?> get props => [note];
}

class UpdateHandoffNote extends HandoffNoteEvent {
  final String noteId;
  final Map<String, dynamic> updates;

  const UpdateHandoffNote(this.noteId, this.updates);

  @override
  List<Object?> get props => [noteId, updates];
}

class DeleteHandoffNote extends HandoffNoteEvent {
  final String noteId;

  const DeleteHandoffNote(this.noteId);

  @override
  List<Object?> get props => [noteId];
}

class MarkHandoffNoteAsCompleted extends HandoffNoteEvent {
  final String noteId;
  final String completedBy;

  const MarkHandoffNoteAsCompleted(this.noteId, this.completedBy);

  @override
  List<Object?> get props => [noteId, completedBy];
}

class AddHandoffNoteAttachment extends HandoffNoteEvent {
  final String noteId;
  final String attachmentPath;

  const AddHandoffNoteAttachment(this.noteId, this.attachmentPath);

  @override
  List<Object?> get props => [noteId, attachmentPath];
}

class FilterHandoffNotes extends HandoffNoteEvent {
  final bool? showCompleted;
  final String? assignedTo;
  final String? searchQuery;

  const FilterHandoffNotes({
    this.showCompleted,
    this.assignedTo,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [showCompleted, assignedTo, searchQuery];
}