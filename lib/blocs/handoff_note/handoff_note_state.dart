import 'package:equatable/equatable.dart';
import '../../models/handoff_note.dart';

abstract class HandoffNoteState extends Equatable {
  const HandoffNoteState();

  @override
  List<Object?> get props => [];
}

class HandoffNoteInitial extends HandoffNoteState {}

class HandoffNoteLoading extends HandoffNoteState {}

class HandoffNoteLoaded extends HandoffNoteState {
  final List<HandoffNote> notes;
  final List<HandoffNote> filteredNotes;
  final bool? showCompleted;
  final String? assignedTo;
  final String? searchQuery;

  const HandoffNoteLoaded({
    required this.notes,
    required this.filteredNotes,
    this.showCompleted,
    this.assignedTo,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [notes, filteredNotes, showCompleted, assignedTo, searchQuery];
}

class HandoffNoteLoadedById extends HandoffNoteState {
  final HandoffNote note;

  const HandoffNoteLoadedById(this.note);

  @override
  List<Object?> get props => [note];
}

class HandoffNoteCreated extends HandoffNoteState {
  final HandoffNote note;

  const HandoffNoteCreated(this.note);

  @override
  List<Object?> get props => [note];
}

class HandoffNoteUpdated extends HandoffNoteState {
  final HandoffNote note;

  const HandoffNoteUpdated(this.note);

  @override
  List<Object?> get props => [note];
}

class HandoffNoteDeleted extends HandoffNoteState {
  final String noteId;

  const HandoffNoteDeleted(this.noteId);

  @override
  List<Object?> get props => [noteId];
}

class HandoffNoteError extends HandoffNoteState {
  final String message;

  const HandoffNoteError(this.message);

  @override
  List<Object?> get props => [message];
}