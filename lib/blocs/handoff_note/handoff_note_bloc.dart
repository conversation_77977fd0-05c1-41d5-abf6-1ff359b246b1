import 'package:flutter_bloc/flutter_bloc.dart';
import '../../models/handoff_note.dart';
import '../../services/handoff_note_service.dart';
import 'handoff_note_event.dart';
import 'handoff_note_state.dart';

class HandoffNoteBloc extends Bloc<HandoffNoteEvent, HandoffNoteState> {
  final HandoffNoteService _handoffNoteService;

  HandoffNoteBloc(this._handoffNoteService) : super(HandoffNoteInitial()) {
    on<FetchHandoffNotes>(_onFetchHandoffNotes);
    on<FetchHandoffNoteById>(_onFetchHandoffNoteById);
    on<CreateHandoffNote>(_onCreateHandoffNote);
    on<UpdateHandoffNote>(_onUpdateHandoffNote);
    on<DeleteHandoffNote>(_onDeleteHandoffNote);
    on<MarkHandoffNoteAsCompleted>(_onMarkHandoffNoteAsCompleted);
    on<AddHandoffNoteAttachment>(_onAddHandoffNoteAttachment);
    on<FilterHandoffNotes>(_onFilterHandoffNotes);
  }

  Future<void> _onFetchHandoffNotes(
    FetchHandoffNotes event,
    Emitter<HandoffNoteState> emit,
  ) async {
    try {
      emit(HandoffNoteLoading());
      final notes = await _handoffNoteService.getHandoffNotes(
        residentId: event.residentId,
      );
      emit(HandoffNoteLoaded(
        notes: notes,
        filteredNotes: notes,
      ));
    } catch (e) {
      emit(HandoffNoteError(e.toString()));
    }
  }

  Future<void> _onFetchHandoffNoteById(
    FetchHandoffNoteById event,
    Emitter<HandoffNoteState> emit,
  ) async {
    try {
      emit(HandoffNoteLoading());
      final note = await _handoffNoteService.getHandoffNoteById(event.noteId);
      emit(HandoffNoteLoadedById(note));
    } catch (e) {
      emit(HandoffNoteError(e.toString()));
    }
  }

  Future<void> _onCreateHandoffNote(
    CreateHandoffNote event,
    Emitter<HandoffNoteState> emit,
  ) async {
    try {
      final newNote = await _handoffNoteService.createHandoffNote(event.note);

      final currentState = state;
      if (currentState is HandoffNoteLoaded) {
        final updatedNotes = [newNote, ...currentState.notes];
        emit(HandoffNoteLoaded(
          notes: updatedNotes,
          filteredNotes: _applyFilters(
            updatedNotes,
            currentState.showCompleted,
            currentState.assignedTo,
            currentState.searchQuery,
          ),
          showCompleted: currentState.showCompleted,
          assignedTo: currentState.assignedTo,
          searchQuery: currentState.searchQuery,
        ));
      }

      emit(HandoffNoteCreated(newNote));
    } catch (e) {
      emit(HandoffNoteError(e.toString()));
    }
  }

  Future<void> _onUpdateHandoffNote(
    UpdateHandoffNote event,
    Emitter<HandoffNoteState> emit,
  ) async {
    try {
      final updatedNote = await _handoffNoteService.updateHandoffNote(
        event.noteId,
        event.updates,
      );

      final currentState = state;
      if (currentState is HandoffNoteLoaded) {
        final updatedNotes = currentState.notes.map((note) {
          return note.id == event.noteId ? updatedNote : note;
        }).toList();

        emit(HandoffNoteLoaded(
          notes: updatedNotes,
          filteredNotes: _applyFilters(
            updatedNotes,
            currentState.showCompleted,
            currentState.assignedTo,
            currentState.searchQuery,
          ),
          showCompleted: currentState.showCompleted,
          assignedTo: currentState.assignedTo,
          searchQuery: currentState.searchQuery,
        ));
      }

      emit(HandoffNoteUpdated(updatedNote));
    } catch (e) {
      emit(HandoffNoteError(e.toString()));
    }
  }

  Future<void> _onDeleteHandoffNote(
    DeleteHandoffNote event,
    Emitter<HandoffNoteState> emit,
  ) async {
    try {
      await _handoffNoteService.deleteHandoffNote(event.noteId);

      final currentState = state;
      if (currentState is HandoffNoteLoaded) {
        final updatedNotes = currentState.notes
            .where((note) => note.id != event.noteId)
            .toList();

        emit(HandoffNoteLoaded(
          notes: updatedNotes,
          filteredNotes: _applyFilters(
            updatedNotes,
            currentState.showCompleted,
            currentState.assignedTo,
            currentState.searchQuery,
          ),
          showCompleted: currentState.showCompleted,
          assignedTo: currentState.assignedTo,
          searchQuery: currentState.searchQuery,
        ));
      }

      emit(HandoffNoteDeleted(event.noteId));
    } catch (e) {
      emit(HandoffNoteError(e.toString()));
    }
  }

  Future<void> _onMarkHandoffNoteAsCompleted(
    MarkHandoffNoteAsCompleted event,
    Emitter<HandoffNoteState> emit,
  ) async {
    try {
      final updatedNote = await _handoffNoteService.markAsCompleted(
        event.noteId,
        event.completedBy,
      );

      final currentState = state;
      if (currentState is HandoffNoteLoaded) {
        final updatedNotes = currentState.notes.map((note) {
          return note.id == event.noteId ? updatedNote : note;
        }).toList();

        emit(HandoffNoteLoaded(
          notes: updatedNotes,
          filteredNotes: _applyFilters(
            updatedNotes,
            currentState.showCompleted,
            currentState.assignedTo,
            currentState.searchQuery,
          ),
          showCompleted: currentState.showCompleted,
          assignedTo: currentState.assignedTo,
          searchQuery: currentState.searchQuery,
        ));
      }

      emit(HandoffNoteUpdated(updatedNote));
    } catch (e) {
      emit(HandoffNoteError(e.toString()));
    }
  }

  Future<void> _onAddHandoffNoteAttachment(
    AddHandoffNoteAttachment event,
    Emitter<HandoffNoteState> emit,
  ) async {
    try {
      final updatedNote = await _handoffNoteService.addAttachment(
        event.noteId,
        event.attachmentPath,
      );

      final currentState = state;
      if (currentState is HandoffNoteLoaded) {
        final updatedNotes = currentState.notes.map((note) {
          return note.id == event.noteId ? updatedNote : note;
        }).toList();

        emit(HandoffNoteLoaded(
          notes: updatedNotes,
          filteredNotes: _applyFilters(
            updatedNotes,
            currentState.showCompleted,
            currentState.assignedTo,
            currentState.searchQuery,
          ),
          showCompleted: currentState.showCompleted,
          assignedTo: currentState.assignedTo,
          searchQuery: currentState.searchQuery,
        ));
      }

      emit(HandoffNoteUpdated(updatedNote));
    } catch (e) {
      emit(HandoffNoteError(e.toString()));
    }
  }

  void _onFilterHandoffNotes(
    FilterHandoffNotes event,
    Emitter<HandoffNoteState> emit,
  ) {
    final currentState = state;
    if (currentState is HandoffNoteLoaded) {
      final filteredNotes = _applyFilters(
        currentState.notes,
        event.showCompleted,
        event.assignedTo,
        event.searchQuery,
      );

      emit(HandoffNoteLoaded(
        notes: currentState.notes,
        filteredNotes: filteredNotes,
        showCompleted: event.showCompleted,
        assignedTo: event.assignedTo,
        searchQuery: event.searchQuery,
      ));
    }
  }

  List<HandoffNote> _applyFilters(
    List<HandoffNote> notes,
    bool? showCompleted,
    String? assignedTo,
    String? searchQuery,
  ) {
    var filteredNotes = List<HandoffNote>.from(notes);

    // Filter by completion status
    if (showCompleted != null) {
      filteredNotes = filteredNotes
          .where((note) => note.isCompleted == showCompleted)
          .toList();
    }

    // Filter by assigned nurse
    if (assignedTo != null && assignedTo.isNotEmpty) {
      filteredNotes =
          filteredNotes.where((note) => note.assignedTo == assignedTo).toList();
    }

    // Filter by search query
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase();
      filteredNotes = filteredNotes.where((note) {
        return note.content.toLowerCase().contains(query) ||
            note.createdBy.toLowerCase().contains(query) ||
            (note.completedBy != null &&
                note.completedBy!.toLowerCase().contains(query));
      }).toList();
    }

    // Sort by creation date (newest first)
    filteredNotes.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    return filteredNotes;
  }
}
