import 'package:equatable/equatable.dart';
import '../../models/incident.dart';

abstract class IncidentsEvent extends Equatable {
  const IncidentsEvent();

  @override
  List<Object?> get props => [];
}

class FetchIncidents extends IncidentsEvent {}

class CreateIncident extends IncidentsEvent {
  final Incident incident;

  const CreateIncident(this.incident);

  @override
  List<Object> get props => [incident];
}

class UpdateIncidentStatus extends IncidentsEvent {
  final String incidentId;
  final String newStatus;

  const UpdateIncidentStatus(this.incidentId, this.newStatus);

  @override
  List<Object> get props => [incidentId, newStatus];
}

class DeleteIncident extends IncidentsEvent {
  final String incidentId;

  const DeleteIncident(this.incidentId);

  @override
  List<Object> get props => [incidentId];
}

class FilterIncidents extends IncidentsEvent {
  final String? level;
  final String? status;
  final String? searchQuery;

  const FilterIncidents({
    this.level,
    this.status,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [level, status, searchQuery];
}

class AddIncidentComment extends IncidentsEvent {
  final String incidentId;
  final String comment;

  const AddIncidentComment(this.incidentId, this.comment);

  @override
  List<Object> get props => [incidentId, comment];
}