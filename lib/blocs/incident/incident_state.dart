import 'package:equatable/equatable.dart';
import '../../models/incident.dart';

abstract class IncidentState extends Equatable {
  const IncidentState();

  @override
  List<Object?> get props => [];
}

class IncidentInitial extends IncidentState {}

class IncidentLoading extends IncidentState {}

class IncidentLoaded extends IncidentState {
  final List<Incident> incidents;
  final List<Incident> filteredIncidents;
  final String? currentLevel;
  final String? currentStatus;
  final String? searchQuery;

  const IncidentLoaded({
    required this.incidents,
    required this.filteredIncidents,
    this.currentLevel,
    this.currentStatus,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [
        incidents,
        filteredIncidents,
        currentLevel,
        currentStatus,
        searchQuery,
      ];
}

class IncidentError extends IncidentState {
  final String message;

  const IncidentError(this.message);

  @override
  List<Object> get props => [message];
}

class IncidentCreated extends IncidentState {
  final Incident incident;

  const IncidentCreated(this.incident);

  @override
  List<Object> get props => [incident];
}

class IncidentUpdated extends IncidentState {
  final Incident incident;

  const IncidentUpdated(this.incident);

  @override
  List<Object> get props => [incident];
}

class IncidentEmpty extends IncidentState {}
