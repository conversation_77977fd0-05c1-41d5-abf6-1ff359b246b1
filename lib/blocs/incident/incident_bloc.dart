import 'package:flutter_bloc/flutter_bloc.dart';
import '../../models/incident.dart';
import '../../services/incident_service.dart';
import 'incident_event.dart';
import 'incident_state.dart';


class IncidentBloc extends Bloc<IncidentsEvent, IncidentState> {
  final IncidentService _incidentService;

  IncidentBloc(this._incidentService) : super(IncidentInitial()) {
    on<FetchIncidents>(_onFetchIncidents);
    on<CreateIncident>(_onCreateIncident);
    on<UpdateIncidentStatus>(_onUpdateIncidentStatus);
    on<DeleteIncident>(_onDeleteIncident);
    on<FilterIncidents>(_onFilterIncidents);
    on<AddIncidentComment>(_onAddIncidentComment);
  }

  Future<void> _onFetchIncidents(
    FetchIncidents event,
    Emitter<IncidentState> emit,
  ) async {
    try {
      emit(IncidentLoading());
      final incidents = await _incidentService.getIncidents();

      
      if (incidents.isEmpty) {
        emit(IncidentEmpty());
      } else {
        emit(IncidentLoaded(
          incidents: incidents,
          filteredIncidents: incidents,
        ));
      }
    } catch (e) {
      print('Error in IncidentBloc._onFetchIncidents: $e');
      emit(IncidentError(e.toString()));
    }
  }

  Future<void> _onCreateIncident(
    CreateIncident event,
    Emitter<IncidentState> emit,
  ) async {
    try {
      final newIncident = await _incidentService.createIncident(event.incident);
      
      final currentState = state;
      if (currentState is IncidentLoaded) {
        final updatedIncidents = [newIncident, ...currentState.incidents];
        emit(IncidentLoaded(
          incidents: updatedIncidents,
          filteredIncidents: _applyFilters(
            updatedIncidents,
            currentState.currentLevel,
            currentState.currentStatus,
            currentState.searchQuery,
          ),
          currentLevel: currentState.currentLevel,
          currentStatus: currentState.currentStatus,
          searchQuery: currentState.searchQuery,
        ));
      }
      
      emit(IncidentCreated(newIncident));
    } catch (e) {
      emit(IncidentError(e.toString()));
    }
  }

  Future<void> _onUpdateIncidentStatus(
    UpdateIncidentStatus event,
    Emitter<IncidentState> emit,
  ) async {
    try {
      final updatedIncident = await _incidentService.updateIncident(
        event.incidentId,
        {'status': event.newStatus},
      );

      final currentState = state;
      if (currentState is IncidentLoaded) {
        final updatedIncidents = currentState.incidents.map((incident) {
          return incident.id == event.incidentId ? updatedIncident : incident;
        }).toList();

        emit(IncidentLoaded(
          incidents: updatedIncidents,
          filteredIncidents: _applyFilters(
            updatedIncidents,
            currentState.currentLevel,
            currentState.currentStatus,
            currentState.searchQuery,
          ),
          currentLevel: currentState.currentLevel,
          currentStatus: currentState.currentStatus,
          searchQuery: currentState.searchQuery,
        ));
      }
    } catch (e) {
      emit(IncidentError(e.toString()));
    }
  }

  Future<void> _onDeleteIncident(
    DeleteIncident event,
    Emitter<IncidentState> emit,
  ) async {
    try {
      await _incidentService.deleteIncident(event.incidentId);

      final currentState = state;
      if (currentState is IncidentLoaded) {
        final updatedIncidents = currentState.incidents
            .where((incident) => incident.id != event.incidentId)
            .toList();

        emit(IncidentLoaded(
          incidents: updatedIncidents,
          filteredIncidents: _applyFilters(
            updatedIncidents,
            currentState.currentLevel,
            currentState.currentStatus,
            currentState.searchQuery,
          ),
          currentLevel: currentState.currentLevel,
          currentStatus: currentState.currentStatus,
          searchQuery: currentState.searchQuery,
        ));
      }
    } catch (e) {
      emit(IncidentError(e.toString()));
    }
  }

  void _onFilterIncidents(
    FilterIncidents event,
    Emitter<IncidentState> emit,
  ) {
    final currentState = state;
    if (currentState is IncidentLoaded) {
      final filteredIncidents = _applyFilters(
        currentState.incidents,
        event.level,
        event.status,
        event.searchQuery,
      );

      emit(IncidentLoaded(
        incidents: currentState.incidents,
        filteredIncidents: filteredIncidents,
        currentLevel: event.level,
        currentStatus: event.status,
        searchQuery: event.searchQuery,
      ));
    }
  }

  Future<void> _onAddIncidentComment(
    AddIncidentComment event,
    Emitter<IncidentState> emit,
  ) async {
    try {
      final updatedIncident = await _incidentService.addComment(
        event.incidentId,
        event.comment,
      );

      final currentState = state;
      if (currentState is IncidentLoaded) {
        final updatedIncidents = currentState.incidents.map((incident) {
          return incident.id == event.incidentId ? updatedIncident : incident;
        }).toList();

        emit(IncidentLoaded(
          incidents: updatedIncidents,
          filteredIncidents: _applyFilters(
            updatedIncidents,
            currentState.currentLevel,
            currentState.currentStatus,
            currentState.searchQuery,
          ),
          currentLevel: currentState.currentLevel,
          currentStatus: currentState.currentStatus,
          searchQuery: currentState.searchQuery,
        ));
      }
    } catch (e) {
      emit(IncidentError(e.toString()));
    }
  }

  List<Incident> _applyFilters(
    List<Incident> incidents,
    String? level,
    String? status,
    String? searchQuery,
  ) {
    return incidents.where((incident) {
      bool matchesLevel = level == null || incident.level == level;
      bool matchesStatus = status == null || incident.status == status;
      bool matchesSearch = searchQuery == null ||
          searchQuery.isEmpty ||
          incident.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
          incident.description.toLowerCase().contains(searchQuery.toLowerCase());

      return matchesLevel && matchesStatus && matchesSearch;
    }).toList();
  }
}
