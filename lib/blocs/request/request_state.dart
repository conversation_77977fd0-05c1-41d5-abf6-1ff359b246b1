import 'package:equatable/equatable.dart';
import '../../models/request.dart';

abstract class RequestState extends Equatable {
  const RequestState();

  @override
  List<Object?> get props => [];
}

class RequestInitial extends RequestState {}

class RequestLoading extends RequestState {}

class RequestLoaded extends RequestState {
  final List<Request> requests;
  final List<Request> filteredRequests;
  final RequestType? currentType;
  final RequestStatus? currentStatus;
  final RequestPriority? currentPriority;
  final String? searchQuery;

  const RequestLoaded({
    required this.requests,
    required this.filteredRequests,
    this.currentType,
    this.currentStatus,
    this.currentPriority,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [
        requests,
        filteredRequests,
        currentType,
        currentStatus,
        currentPriority,
        searchQuery,
      ];
}

class RequestDetailLoaded extends RequestState {
  final Request request;

  const RequestDetailLoaded(this.request);

  @override
  List<Object?> get props => [request];
}

class RequestCreated extends RequestState {
  final Request request;

  const RequestCreated(this.request);

  @override
  List<Object?> get props => [request];
}

class RequestUpdated extends RequestState {
  final Request request;

  const RequestUpdated(this.request);

  @override
  List<Object?> get props => [request];
}

class RequestDeleted extends RequestState {
  final String requestId;

  const RequestDeleted(this.requestId);

  @override
  List<Object?> get props => [requestId];
}

class RequestCommentAdded extends RequestState {
  final Request request;
  final RequestComment comment;

  const RequestCommentAdded(this.request, this.comment);

  @override
  List<Object?> get props => [request, comment];
}

class RequestEmpty extends RequestState {}

class RequestError extends RequestState {
  final String message;

  const RequestError(this.message);

  @override
  List<Object?> get props => [message];
}