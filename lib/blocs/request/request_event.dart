import 'package:equatable/equatable.dart';
import '../../models/request.dart';

abstract class RequestEvent extends Equatable {
  const RequestEvent();

  @override
  List<Object?> get props => [];
}

class FetchRequests extends RequestEvent {
  final String? residentId;
  final String? assignedTo;

  const FetchRequests({this.residentId, this.assignedTo});

  @override
  List<Object?> get props => [residentId, assignedTo];
}

class FetchRequestById extends RequestEvent {
  final String requestId;

  const FetchRequestById(this.requestId);

  @override
  List<Object?> get props => [requestId];
}

class CreateRequest extends RequestEvent {
  final Request request;

  const CreateRequest(this.request);

  @override
  List<Object?> get props => [request];
}

class UpdateRequest extends RequestEvent {
  final String requestId;
  final Map<String, dynamic> updates;

  const UpdateRequest(this.requestId, this.updates);

  @override
  List<Object?> get props => [requestId, updates];
}

class DeleteRequest extends RequestEvent {
  final String requestId;

  const DeleteRequest(this.requestId);

  @override
  List<Object?> get props => [requestId];
}

class UpdateRequestStatus extends RequestEvent {
  final String requestId;
  final RequestStatus newStatus;
  final String? completedBy;

  const UpdateRequestStatus(
    this.requestId,
    this.newStatus, {
    this.completedBy,
  });

  @override
  List<Object?> get props => [requestId, newStatus, completedBy];
}

class AssignRequest extends RequestEvent {
  final String requestId;
  final String assignedTo;

  const AssignRequest(this.requestId, this.assignedTo);

  @override
  List<Object?> get props => [requestId, assignedTo];
}

class AddRequestComment extends RequestEvent {
  final String requestId;
  final RequestComment comment;

  const AddRequestComment(this.requestId, this.comment);

  @override
  List<Object?> get props => [requestId, comment];
}

class FilterRequests extends RequestEvent {
  final RequestType? type;
  final RequestStatus? status;
  final RequestPriority? priority;
  final String? searchQuery;

  const FilterRequests({
    this.type,
    this.status,
    this.priority,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [type, status, priority, searchQuery];
}