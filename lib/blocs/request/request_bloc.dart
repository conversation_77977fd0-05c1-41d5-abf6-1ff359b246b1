import 'package:flutter_bloc/flutter_bloc.dart';
import '../../models/request.dart';
import '../../services/request_service.dart';
import 'request_event.dart';
import 'request_state.dart';

class RequestBloc extends Bloc<RequestEvent, RequestState> {
  final RequestService _requestService;

  RequestBloc(this._requestService) : super(RequestInitial()) {
    on<FetchRequests>(_onFetchRequests);
    on<FetchRequestById>(_onFetchRequestById);
    on<CreateRequest>(_onCreateRequest);
    on<UpdateRequest>(_onUpdateRequest);
    on<DeleteRequest>(_onDeleteRequest);
    on<UpdateRequestStatus>(_onUpdateRequestStatus);
    on<AssignRequest>(_onAssignRequest);
    on<AddRequestComment>(_onAddRequestComment);
    on<FilterRequests>(_onFilterRequests);
  }

  Future<void> _onFetchRequests(
    FetchRequests event,
    Emitter<RequestState> emit,
  ) async {
    try {
      emit(RequestLoading());
      final requests = await _requestService.getRequests(
        residentId: event.residentId,
        assignedTo: event.assignedTo,
      );

      if (requests.isEmpty) {
        emit(RequestEmpty());
      } else {
        emit(RequestLoaded(
          requests: requests,
          filteredRequests: requests,
        ));
      }
    } catch (e) {
      emit(RequestError(e.toString()));
    }
  }

  Future<void> _onFetchRequestById(
    FetchRequestById event,
    Emitter<RequestState> emit,
  ) async {
    try {
      emit(RequestLoading());
      final request = await _requestService.getRequestById(event.requestId);
      emit(RequestDetailLoaded(request));
    } catch (e) {
      emit(RequestError(e.toString()));
    }
  }

  Future<void> _onCreateRequest(
    CreateRequest event,
    Emitter<RequestState> emit,
  ) async {
    try {
      final newRequest = await _requestService.createRequest(event.request);

      final currentState = state;
      if (currentState is RequestLoaded) {
        final updatedRequests = [newRequest, ...currentState.requests];
        emit(RequestLoaded(
          requests: updatedRequests,
          filteredRequests: _applyFilters(
            updatedRequests,
            currentState.currentType,
            currentState.currentStatus,
            currentState.currentPriority,
            currentState.searchQuery,
          ),
          currentType: currentState.currentType,
          currentStatus: currentState.currentStatus,
          currentPriority: currentState.currentPriority,
          searchQuery: currentState.searchQuery,
        ));
      }

      emit(RequestCreated(newRequest));
    } catch (e) {
      emit(RequestError(e.toString()));
    }
  }

  Future<void> _onUpdateRequest(
    UpdateRequest event,
    Emitter<RequestState> emit,
  ) async {
    try {
      final updatedRequest = await _requestService.updateRequest(
        event.requestId,
        event.updates,
      );

      final currentState = state;
      if (currentState is RequestLoaded) {
        final updatedRequests = currentState.requests.map((request) {
          return request.id == event.requestId ? updatedRequest : request;
        }).toList();

        emit(RequestLoaded(
          requests: updatedRequests,
          filteredRequests: _applyFilters(
            updatedRequests,
            currentState.currentType,
            currentState.currentStatus,
            currentState.currentPriority,
            currentState.searchQuery,
          ),
          currentType: currentState.currentType,
          currentStatus: currentState.currentStatus,
          currentPriority: currentState.currentPriority,
          searchQuery: currentState.searchQuery,
        ));
      }

      emit(RequestUpdated(updatedRequest));
    } catch (e) {
      emit(RequestError(e.toString()));
    }
  }

  Future<void> _onDeleteRequest(
    DeleteRequest event,
    Emitter<RequestState> emit,
  ) async {
    try {
      await _requestService.deleteRequest(event.requestId);

      final currentState = state;
      if (currentState is RequestLoaded) {
        final updatedRequests = currentState.requests
            .where((request) => request.id != event.requestId)
            .toList();

        emit(RequestLoaded(
          requests: updatedRequests,
          filteredRequests: _applyFilters(
            updatedRequests,
            currentState.currentType,
            currentState.currentStatus,
            currentState.currentPriority,
            currentState.searchQuery,
          ),
          currentType: currentState.currentType,
          currentStatus: currentState.currentStatus,
          currentPriority: currentState.currentPriority,
          searchQuery: currentState.searchQuery,
        ));
      }

      emit(RequestDeleted(event.requestId));
    } catch (e) {
      emit(RequestError(e.toString()));
    }
  }

  Future<void> _onUpdateRequestStatus(
    UpdateRequestStatus event,
    Emitter<RequestState> emit,
  ) async {
    try {
      final updatedRequest = await _requestService.updateRequestStatus(
        event.requestId,
        event.newStatus,
        completedBy: event.completedBy,
      );

      final currentState = state;
      if (currentState is RequestLoaded) {
        final updatedRequests = currentState.requests.map((request) {
          return request.id == event.requestId ? updatedRequest : request;
        }).toList();

        emit(RequestLoaded(
          requests: updatedRequests,
          filteredRequests: _applyFilters(
            updatedRequests,
            currentState.currentType,
            currentState.currentStatus,
            currentState.currentPriority,
            currentState.searchQuery,
          ),
          currentType: currentState.currentType,
          currentStatus: currentState.currentStatus,
          currentPriority: currentState.currentPriority,
          searchQuery: currentState.searchQuery,
        ));
      }

      emit(RequestUpdated(updatedRequest));
    } catch (e) {
      emit(RequestError(e.toString()));
    }
  }

  Future<void> _onAssignRequest(
    AssignRequest event,
    Emitter<RequestState> emit,
  ) async {
    try {
      final updatedRequest = await _requestService.assignRequest(
        event.requestId,
        event.assignedTo,
      );

      final currentState = state;
      if (currentState is RequestLoaded) {
        final updatedRequests = currentState.requests.map((request) {
          return request.id == event.requestId ? updatedRequest : request;
        }).toList();

        emit(RequestLoaded(
          requests: updatedRequests,
          filteredRequests: _applyFilters(
            updatedRequests,
            currentState.currentType,
            currentState.currentStatus,
            currentState.currentPriority,
            currentState.searchQuery,
          ),
          currentType: currentState.currentType,
          currentStatus: currentState.currentStatus,
          currentPriority: currentState.currentPriority,
          searchQuery: currentState.searchQuery,
        ));
      }

      emit(RequestUpdated(updatedRequest));
    } catch (e) {
      emit(RequestError(e.toString()));
    }
  }

  Future<void> _onAddRequestComment(
    AddRequestComment event,
    Emitter<RequestState> emit,
  ) async {
    try {
      final updatedRequest = await _requestService.addRequestComment(
        event.requestId,
        event.comment,
      );

      final currentState = state;
      if (currentState is RequestLoaded) {
        final updatedRequests = currentState.requests.map((request) {
          return request.id == event.requestId ? updatedRequest : request;
        }).toList();

        emit(RequestLoaded(
          requests: updatedRequests,
          filteredRequests: _applyFilters(
            updatedRequests,
            currentState.currentType,
            currentState.currentStatus,
            currentState.currentPriority,
            currentState.searchQuery,
          ),
          currentType: currentState.currentType,
          currentStatus: currentState.currentStatus,
          currentPriority: currentState.currentPriority,
          searchQuery: currentState.searchQuery,
        ));
      }

      emit(RequestCommentAdded(updatedRequest, event.comment));
    } catch (e) {
      emit(RequestError(e.toString()));
    }
  }

  void _onFilterRequests(
    FilterRequests event,
    Emitter<RequestState> emit,
  ) {
    final currentState = state;
    if (currentState is RequestLoaded) {
      final filteredRequests = _applyFilters(
        currentState.requests,
        event.type,
        event.status,
        event.priority,
        event.searchQuery,
      );

      emit(RequestLoaded(
        requests: currentState.requests,
        filteredRequests: filteredRequests,
        currentType: event.type,
        currentStatus: event.status,
        currentPriority: event.priority,
        searchQuery: event.searchQuery,
      ));
    }
  }

  List<Request> _applyFilters(
    List<Request> requests,
    RequestType? type,
    RequestStatus? status,
    RequestPriority? priority,
    String? searchQuery,
  ) {
    var filteredRequests = List<Request>.from(requests);

    // Filter by type
    if (type != null) {
      filteredRequests =
          filteredRequests.where((request) => request.type == type).toList();
    }

    // Filter by status
    if (status != null) {
      filteredRequests = filteredRequests
          .where((request) => request.status == status)
          .toList();
    }

    // Filter by priority
    if (priority != null) {
      filteredRequests = filteredRequests
          .where((request) => request.priority == priority)
          .toList();
    }

    // Filter by search query
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase();
      filteredRequests = filteredRequests.where((request) {
        return request.title.toLowerCase().contains(query) ||
            request.description.toLowerCase().contains(query) ||
            (request.createdBy.toLowerCase().contains(query)) ||
            (request.assignedTo != null &&
                request.assignedTo!.toLowerCase().contains(query));
      }).toList();
    }

    // Sort by priority (highest first) and then by creation date (newest first)
    filteredRequests.sort((a, b) {
      // First sort by priority (urgent > high > medium > low)
      final priorityComparison = b.priority.index.compareTo(a.priority.index);
      if (priorityComparison != 0) {
        return priorityComparison;
      }

      // Then sort by creation date (newest first)
      return b.createdAt.compareTo(a.createdAt);
    });

    return filteredRequests;
  }
}
