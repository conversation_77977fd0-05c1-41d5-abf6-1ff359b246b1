import 'package:flutter_bloc/flutter_bloc.dart';
import '../../screens/utils/snackbar_utils.dart';
import '../../services/ticket_service.dart';
import 'ticket_event.dart';
import 'ticket_state.dart';
import '../../models/ticket.dart';

class TicketBloc extends Bloc<TicketEvent, TicketState> {
  final TicketService _ticketService;

  TicketBloc(this._ticketService) : super(TicketInitial()) {
    on<FetchTickets>(_onFetchTickets);
    on<CreateTicket>(_onCreateTicket);
    on<UpdateTicketStatus>(_onUpdateTicketStatus);
    on<AddTicketComment>(_onAddTicketComment);
    on<DeleteTicket>(_onDeleteTicket);
    on<FilterTickets>(_onFilterTickets);
  }

  Future<void> _onFetchTickets(
    FetchTickets event,
    Emitter<TicketState> emit,
  ) async {
    try {
      emit(TicketLoading());
      final tickets = await _ticketService.getTickets();
      emit(TicketLoaded(
        tickets: tickets,
        filteredTickets: tickets,
      ));
    } on AccessDeniedException {
      emit(TicketAccessDenied());
    } catch (e) {
      emit(TicketError(e.toString()));
    }
  }

  Future<void> _onCreateTicket(
    CreateTicket event,
    Emitter<TicketState> emit,
  ) async {
    try {
      final newTicket = await _ticketService.createTicket(event.ticket, event.unit??'', event.home??'');
      final currentState = state;
      if (currentState is TicketLoaded) {
        final updatedTickets = [newTicket, ...currentState.tickets];
        emit(TicketLoaded(
          tickets: updatedTickets,
          filteredTickets: _applyFilters(
            updatedTickets,
            currentState.currentFilter,
            currentState.isAssignedToMeFilter,
          ),
          currentFilter: currentState.currentFilter,
          isAssignedToMeFilter: currentState.isAssignedToMeFilter,
        ));
      }
      emit(TicketCreated(newTicket));
    } on AccessDeniedException {
      emit(TicketAccessDenied());
    } catch (e) {
      emit(TicketError(e.toString()));
    }
  }

  Future<void> _onUpdateTicketStatus(
    UpdateTicketStatus event,
    Emitter<TicketState> emit,
  ) async {
    try {
      final updatedTicket = await _ticketService.updateTicket(
        event.ticketId,
        {'status': event.newStatus.toString().split('.').last},
      );

      final currentState = state;
      if (currentState is TicketLoaded) {
        final updatedTickets = currentState.tickets.map((ticket) {
          return ticket.id == event.ticketId ? updatedTicket : ticket;
        }).toList();

        emit(TicketLoaded(
          tickets: updatedTickets,
          filteredTickets: _applyFilters(
            updatedTickets,
            currentState.currentFilter,
            currentState.isAssignedToMeFilter,
          ),
          currentFilter: currentState.currentFilter,
          isAssignedToMeFilter: currentState.isAssignedToMeFilter,
        ));
      }
    } catch (e) {
      emit(TicketError(e.toString()));
    }
  }

  Future<void> _onAddTicketComment(
    AddTicketComment event,
    Emitter<TicketState> emit,
  ) async {
    try {
      final updatedTicket = await _ticketService.addComment(
        event.ticketId,
        event.entry,
      );

      final currentState = state;
      if (currentState is TicketLoaded) {
        final updatedTickets = currentState.tickets.map((ticket) {
          return ticket.id == event.ticketId ? updatedTicket : ticket;
        }).toList();

        emit(TicketLoaded(
          tickets: updatedTickets,
          filteredTickets: _applyFilters(
            updatedTickets,
            currentState.currentFilter,
            currentState.isAssignedToMeFilter,
          ),
          currentFilter: currentState.currentFilter,
          isAssignedToMeFilter: currentState.isAssignedToMeFilter,
        ));
      }
    } catch (e) {
      emit(TicketError(e.toString()));
    }
  }

  Future<void> _onDeleteTicket(
    DeleteTicket event,
    Emitter<TicketState> emit,
  ) async {
    try {
      await _ticketService.deleteTicket(event.ticketId);

      final currentState = state;
      if (currentState is TicketLoaded) {
        final updatedTickets = currentState.tickets
            .where((ticket) => ticket.id != event.ticketId)
            .toList();

        emit(TicketLoaded(
          tickets: updatedTickets,
          filteredTickets: _applyFilters(
            updatedTickets,
            currentState.currentFilter,
            currentState.isAssignedToMeFilter,
          ),
          currentFilter: currentState.currentFilter,
          isAssignedToMeFilter: currentState.isAssignedToMeFilter,
        ));
      }
    } catch (e) {
      emit(TicketError(e.toString()));
    }
  }

  void _onFilterTickets(
    FilterTickets event,
    Emitter<TicketState> emit,
  ) {
    final currentState = state;
    if (currentState is TicketLoaded) {
      final filteredTickets = _applyFilters(
        currentState.tickets,
        event.status,
        event.assignedToMe,
      );

      emit(TicketLoaded(
        tickets: currentState.tickets,
        filteredTickets: filteredTickets,
        currentFilter: event.status,
        isAssignedToMeFilter: event.assignedToMe,
      ));
    }
  }

  List<TicketModel> _applyFilters(
    List<TicketModel> tickets,
    TicketStatus? status,
    bool assignedToMe,
  ) {
    var filteredTickets = List<TicketModel>.from(tickets);

    if (status != null) {
      filteredTickets = filteredTickets
          .where((ticket) => ticket.status == status)
          .toList();
    }

    if (assignedToMe) {
      // Implement assigned to me logic based on your user authentication
      // This is just an example:
      // filteredTickets = filteredTickets
      //     .where((ticket) => ticket.assignedTo == currentUserId)
      //     .toList();
    }

    return filteredTickets;
  }
}