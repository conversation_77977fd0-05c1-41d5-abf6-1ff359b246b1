import 'package:equatable/equatable.dart';
import '../../models/ticket.dart';

abstract class TicketEvent extends Equatable {
  const TicketEvent();

  @override
  List<Object> get props => [];
}

class FetchTickets extends TicketEvent {}

class CreateTicket extends TicketEvent {
  final TicketModel ticket;
  final String? unit;
  final String? home;

  const CreateTicket(this.ticket, {this.unit, this.home});

  @override
  List<Object> get props => [ticket, unit ?? '', home ?? ''];
}

class UpdateTicketStatus extends TicketEvent {
  final String ticketId;
  final TicketStatus newStatus;

  const UpdateTicketStatus(this.ticketId, this.newStatus);

  @override
  List<Object> get props => [ticketId, newStatus];
}

class AddTicketComment extends TicketEvent {
  final String ticketId;
  final TimelineEntry entry;

  const AddTicketComment(this.ticketId, this.entry);

  @override
  List<Object> get props => [ticketId, entry];
}

class DeleteTicket extends TicketEvent {
  final String ticketId;

  const DeleteTicket(this.ticketId);

  @override
  List<Object> get props => [ticketId];
}

class FilterTickets extends TicketEvent {
  final TicketStatus? status;
  final String? category;
  final bool assignedToMe;

  const FilterTickets({
    this.status,
    this.category,
    this.assignedToMe = false,
  });

  @override
  List<Object> get props => [status ?? '', category ?? '', assignedToMe];
}