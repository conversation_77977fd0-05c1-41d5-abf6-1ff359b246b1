import 'package:equatable/equatable.dart';
import '../../models/ticket.dart';

abstract class TicketState extends Equatable {
  const TicketState();

  @override
  List<Object> get props => [];
}

class TicketInitial extends TicketState {}

class TicketLoading extends TicketState {}

class TicketLoaded extends TicketState {
  final List<TicketModel> tickets;
  final List<TicketModel> filteredTickets;
  final TicketStatus? currentFilter;
  final bool isAssignedToMeFilter;

  const TicketLoaded({
    required this.tickets,
    required this.filteredTickets,
    this.currentFilter,
    this.isAssignedToMeFilter = false,
  });

  @override
  List<Object> get props => [
    tickets,
    filteredTickets,
    currentFilter ?? '',
    isAssignedToMeFilter,
  ];
}

class TicketError extends TicketState {
  final String message;

  const TicketError(this.message);

  @override
  List<Object> get props => [message];
}

class TicketCreated extends TicketState {
  final TicketModel ticket;

  const TicketCreated(this.ticket);

  @override
  List<Object> get props => [ticket];
}

class TicketUpdated extends TicketState {
  final TicketModel ticket;

  const TicketUpdated(this.ticket);

  @override
  List<Object> get props => [ticket];
}

class TicketAccessDenied extends TicketState {}
