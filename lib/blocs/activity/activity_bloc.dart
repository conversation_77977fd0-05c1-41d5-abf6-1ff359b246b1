import 'package:flutter_bloc/flutter_bloc.dart';
import '../../models/activity.dart';
import '../../services/activity_service.dart';
import 'activity_event.dart';
import 'activity_state.dart';

class ActivityBloc extends Bloc<ActivityEvent, ActivityState> {
  final ActivityService _activityService;

  ActivityBloc(this._activityService) : super(ActivityInitial()) {
    on<FetchActivities>(_onFetchActivities);
    on<CreateActivity>(_onCreateActivity);
    on<UpdateActivity>(_onUpdateActivity);
    on<DeleteActivity>(_onDeleteActivity);
    on<FilterActivities>(_onFilterActivities);
  }

  Future<void> _onFetchActivities(
    FetchActivities event,
    Emitter<ActivityState> emit,
  ) async {
    try {
      emit(ActivityLoading());
      final activities = await _activityService.getActivities(
        residentId: event.residentId,
        staffId: event.staffId,
        category: event.category,
        startDate: event.startDate,
        endDate: event.endDate,
      );

      if (activities.isEmpty) {
        emit(ActivityEmpty());
      } else {
        emit(ActivityLoaded(
          activities: activities,
          filteredActivities: activities,
        ));
      }
    } catch (e) {
      emit(ActivityError(e.toString()));
    }
  }

  Future<void> _onCreateActivity(
    CreateActivity event,
    Emitter<ActivityState> emit,
  ) async {
    try {
      final newActivity = await _activityService.createActivity(event.activity);

      final currentState = state;
      if (currentState is ActivityLoaded) {
        final updatedActivities = [newActivity, ...currentState.activities];
        emit(ActivityLoaded(
          activities: updatedActivities,
          filteredActivities: _applyFilters(
            updatedActivities,
            currentState.currentCategory,
            currentState.currentResponse,
            currentState.currentDate,
            currentState.searchQuery,
          ),
          currentCategory: currentState.currentCategory,
          currentResponse: currentState.currentResponse,
          currentDate: currentState.currentDate,
          searchQuery: currentState.searchQuery,
        ));
      }

      emit(ActivityCreated(newActivity));
    } catch (e) {
      emit(ActivityError(e.toString()));
    }
  }

  Future<void> _onUpdateActivity(
    UpdateActivity event,
    Emitter<ActivityState> emit,
  ) async {
    try {
      final updatedActivity = await _activityService.updateActivity(
        event.activityId,
        event.updates,
      );

      final currentState = state;
      if (currentState is ActivityLoaded) {
        final updatedActivities = currentState.activities.map((activity) {
          return activity.id == event.activityId ? updatedActivity : activity;
        }).toList();

        emit(ActivityLoaded(
          activities: updatedActivities,
          filteredActivities: _applyFilters(
            updatedActivities,
            currentState.currentCategory,
            currentState.currentResponse,
            currentState.currentDate,
            currentState.searchQuery,
          ),
          currentCategory: currentState.currentCategory,
          currentResponse: currentState.currentResponse,
          currentDate: currentState.currentDate,
          searchQuery: currentState.searchQuery,
        ));
      }

      emit(ActivityUpdated(updatedActivity));
    } catch (e) {
      emit(ActivityError(e.toString()));
    }
  }

  Future<void> _onDeleteActivity(
    DeleteActivity event,
    Emitter<ActivityState> emit,
  ) async {
    try {
      await _activityService.deleteActivity(event.activityId);

      final currentState = state;
      if (currentState is ActivityLoaded) {
        final updatedActivities = currentState.activities
            .where((activity) => activity.id != event.activityId)
            .toList();

        emit(ActivityLoaded(
          activities: updatedActivities,
          filteredActivities: _applyFilters(
            updatedActivities,
            currentState.currentCategory,
            currentState.currentResponse,
            currentState.currentDate,
            currentState.searchQuery,
          ),
          currentCategory: currentState.currentCategory,
          currentResponse: currentState.currentResponse,
          currentDate: currentState.currentDate,
          searchQuery: currentState.searchQuery,
        ));
      }

      emit(ActivityDeleted(event.activityId));
    } catch (e) {
      emit(ActivityError(e.toString()));
    }
  }

  void _onFilterActivities(
    FilterActivities event,
    Emitter<ActivityState> emit,
  ) {
    final currentState = state;
    if (currentState is ActivityLoaded) {
      final filteredActivities = _applyFilters(
        currentState.activities,
        event.category,
        event.response,
        event.date,
        event.searchQuery,
      );

      emit(ActivityLoaded(
        activities: currentState.activities,
        filteredActivities: filteredActivities,
        currentCategory: event.category,
        currentResponse: event.response,
        currentDate: event.date,
        searchQuery: event.searchQuery,
      ));
    }
  }

  List<Activity> _applyFilters(
    List<Activity> activities,
    String? category,
    ActivityResponse? response,
    DateTime? date,
    String? searchQuery,
  ) {
    return activities.where((activity) {
      // Filter by category
      if (category != null && category.isNotEmpty && activity.category != category) {
        return false;
      }

      // Filter by response
      if (response != null && activity.response != response) {
        return false;
      }

      // Filter by date
      if (date != null) {
        final activityDate = activity.timestamp;
        if (activityDate.year != date.year ||
            activityDate.month != date.month ||
            activityDate.day != date.day) {
          return false;
        }
      }

      // Filter by search query
      if (searchQuery != null && searchQuery.isNotEmpty) {
        final query = searchQuery.toLowerCase();
        return activity.activityName.toLowerCase().contains(query) ||
            activity.category.toLowerCase().contains(query) ||
            (activity.notes?.toLowerCase().contains(query) ?? false);
      }

      return true;
    }).toList();
  }
}