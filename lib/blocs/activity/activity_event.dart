import 'package:equatable/equatable.dart';
import '../../models/activity.dart';

abstract class ActivityEvent extends Equatable {
  const ActivityEvent();

  @override
  List<Object?> get props => [];
}

class FetchActivities extends ActivityEvent {
  final String? residentId;
  final String? staffId;
  final String? category;
  final DateTime? startDate;
  final DateTime? endDate;

  const FetchActivities({
    this.residentId,
    this.staffId,
    this.category,
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [residentId, staffId, category, startDate, endDate];
}

class CreateActivity extends ActivityEvent {
  final Activity activity;

  const CreateActivity(this.activity);

  @override
  List<Object> get props => [activity];
}

class UpdateActivity extends ActivityEvent {
  final String activityId;
  final Map<String, dynamic> updates;

  const UpdateActivity(this.activityId, this.updates);

  @override
  List<Object> get props => [activityId, updates];
}

class DeleteActivity extends ActivityEvent {
  final String activityId;

  const DeleteActivity(this.activityId);

  @override
  List<Object> get props => [activityId];
}

class FilterActivities extends ActivityEvent {
  final String? category;
  final ActivityResponse? response;
  final DateTime? date;
  final String? searchQuery;

  const FilterActivities({
    this.category,
    this.response,
    this.date,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [category, response, date, searchQuery];
}