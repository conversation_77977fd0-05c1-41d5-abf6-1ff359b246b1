import 'package:equatable/equatable.dart';
import '../../models/activity.dart';

abstract class ActivityState extends Equatable {
  const ActivityState();
  
  @override
  List<Object?> get props => [];
}

class ActivityInitial extends ActivityState {}

class ActivityLoading extends ActivityState {}

class ActivityLoaded extends ActivityState {
  final List<Activity> activities;
  final List<Activity> filteredActivities;
  final String? currentCategory;
  final ActivityResponse? currentResponse;
  final DateTime? currentDate;
  final String? searchQuery;

  const ActivityLoaded({
    required this.activities,
    required this.filteredActivities,
    this.currentCategory,
    this.currentResponse,
    this.currentDate,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [
    activities, 
    filteredActivities, 
    currentCategory, 
    currentResponse, 
    currentDate, 
    searchQuery
  ];
}

class ActivityEmpty extends ActivityState {}

class ActivityCreated extends ActivityState {
  final Activity activity;

  const ActivityCreated(this.activity);

  @override
  List<Object> get props => [activity];
}

class ActivityUpdated extends ActivityState {
  final Activity activity;

  const ActivityUpdated(this.activity);

  @override
  List<Object> get props => [activity];
}

class ActivityDeleted extends ActivityState {
  final String activityId;

  const ActivityDeleted(this.activityId);

  @override
  List<Object> get props => [activityId];
}

class ActivityError extends ActivityState {
  final String message;

  const ActivityError(this.message);

  @override
  List<Object> get props => [message];
}