import 'package:flutter_bloc/flutter_bloc.dart';
import '../../models/unit.dart';
import '../../screens/utils/snackbar_utils.dart';
import '../../services/unit_service.dart';
import 'unit_event.dart';
import 'unit_state.dart';

class UnitBloc extends Bloc<UnitEvent, UnitState> {
  final UnitService _unitService;

  UnitBloc(this._unitService) : super(UnitInitial()) {
    on<FetchUnits>(_onFetchUnits);
    on<FetchUnitById>(_onFetchUnitById);
    on<SearchUnits>(_onSearchUnits);
    on<FilterUnits>(_onFilterUnits);
  }

  Future<void> _onFetchUnits(
    FetchUnits event,
    Emitter<UnitState> emit,
  ) async {
    try {
      emit(UnitLoading());
      final units = await _unitService.getUnits();

      if (units.isEmpty) {
        emit(UnitEmpty());
      } else {
        emit(UnitLoaded(
          units: units,
          filteredUnits: units,
        ));
      }
    } on AccessDeniedException {
      emit(UnitAccessDenied());
    } catch (e) {
      emit(UnitError(e.toString()));
    }
  }

  Future<void> _onFetchUnitById(
    FetchUnitById event,
    Emitter<UnitState> emit,
  ) async {
    try {
      emit(UnitLoading());
      final unit = await _unitService.getUnitById(event.unitId);
      emit(UnitDetailLoaded(unit));
    } on AccessDeniedException {
      emit(UnitAccessDenied());
    } catch (e) {
      emit(UnitError(e.toString()));
    }
  }

  void _onSearchUnits(
    SearchUnits event,
    Emitter<UnitState> emit,
  ) {
    final currentState = state;
    if (currentState is UnitLoaded) {
      final filteredUnits = _applyFilters(
        currentState.units,
        currentState.currentStatus,
        currentState.currentType,
        event.query,
      );

      emit(UnitLoaded(
        units: currentState.units,
        filteredUnits: filteredUnits,
        currentStatus: currentState.currentStatus,
        currentType: currentState.currentType,
        searchQuery: event.query,
      ));
    }
  }

  Future<void> _onFilterUnits(
    FilterUnits event,
    Emitter<UnitState> emit,
  ) async {
    if (state is UnitLoaded) {
      final currentState = state as UnitLoaded;

      if (event.status == null) {
        // If no filter, show all units but maintain search
        final searchQuery = currentState.searchQuery ?? '';
        if (searchQuery.isEmpty) {
          emit(UnitLoaded(
            units: currentState.units,
            filteredUnits: currentState.units,
          ));
        } else {
          final filtered = currentState.units.where((unit) {
            return unit.name
                    .toLowerCase()
                    .contains(searchQuery.toLowerCase()) ||
                unit.id
                    .toLowerCase()
                    .contains(searchQuery.toLowerCase());
          }).toList();
          emit(UnitLoaded(
            units: currentState.units,
            filteredUnits: filtered,
          ));
        }
      } else {
        // Apply status filter
        final filtered = currentState.units.where((unit) {
          // Example: filter by isActive property
          return unit.isActive == event.status;
        }).toList();

        if (filtered.isEmpty) {
          emit(UnitEmpty());
        } else {
          emit(UnitLoaded(
            units: currentState.units,
            filteredUnits: filtered,
          ));
        }
      }
    }
  }

  List<Unit> _applyFilters(
    List<Unit> units,
    String? status,
    String? type,
    String? searchQuery,
  ) {
    return units.where((unit) {
      bool matchesStatus = status == null || unit.isActive.toString() == status;
      bool matchesSearch = searchQuery == null ||
          searchQuery.isEmpty ||
          unit.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
          unit.id.toLowerCase().contains(searchQuery.toLowerCase());

      return matchesStatus && matchesSearch;
    }).toList();
  }
}
