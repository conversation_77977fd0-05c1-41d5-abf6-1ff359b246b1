import 'package:equatable/equatable.dart';

abstract class UnitEvent extends Equatable {
  const UnitEvent();

  @override
  List<Object?> get props => [];
}

class FetchUnits extends UnitEvent {}

class FetchUnitById extends UnitEvent {
  final String unitId;

  FetchUnitById(this.unitId);
}

class SearchUnits extends UnitEvent {
  final String query;

  const SearchUnits(this.query);

  @override
  List<Object> get props => [query];
}

class FilterUnits extends UnitEvent {
  final String? status;

  FilterUnits(this.status);
}
