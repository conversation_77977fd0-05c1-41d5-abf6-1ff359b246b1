import 'package:equatable/equatable.dart';
import '../../models/unit.dart';

abstract class UnitState extends Equatable {
  const UnitState();

  @override
  List<Object?> get props => [];
}

class UnitInitial extends UnitState {}

class UnitLoading extends UnitState {}

class UnitLoaded extends UnitState {
  final List<Unit> units;
  final List<Unit> filteredUnits;
  final String? currentStatus;
  final String? currentType;
  final String? searchQuery;

  const UnitLoaded({
    required this.units,
    required this.filteredUnits,
    this.currentStatus,
    this.currentType,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [
        units,
        filteredUnits,
        currentStatus,
        currentType,
        searchQuery,
      ];
}

class UnitDetailLoaded extends UnitState {
  final Unit unit;

  UnitDetailLoaded(this.unit);
}

class UnitEmpty extends UnitState {}

class UnitError extends UnitState {
  final String message;

  const UnitError(this.message);

  @override
  List<Object> get props => [message];
}

class UnitAccessDenied extends UnitState {}
