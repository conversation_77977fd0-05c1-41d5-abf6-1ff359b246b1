import 'package:flutter_bloc/flutter_bloc.dart';
import '../../services/medication_service.dart';
import '../../models/assigned_medicine.dart';
import 'medication_event.dart';
import 'medication_state.dart';

class MedicationBloc extends Bloc<MedicationEvent, MedicationState> {
  final MedicationService _medicationService;

  MedicationBloc(this._medicationService) : super(MedicationInitial()) {
    // Medication events
    on<FetchMedications>(_onFetchMedications);
    on<FetchMedicationById>(_onFetchMedicationById);
    on<CreateMedication>(_onCreateMedication);
    on<UpdateMedication>(_onUpdateMedication);
    on<DeleteMedication>(_onDeleteMedication);
    on<FilterMedications>(_onFilterMedications);

    // Medication intake events
    on<FetchMedicationIntakes>(_onFetchMedicationIntakes);
    on<RecordMedicationIntake>(_onRecordMedicationIntake);

    // Medication feedback events
    on<FetchMedicationFeedback>(_onFetchMedicationFeedback);
    on<AddMedicationFeedback>(_onAddMedicationFeedback);

    // Doctor notes events
    on<FetchDoctorNotes>(_onFetchDoctorNotes);
    on<AddDoctorNote>(_onAddDoctorNote);

    //medication History
    on<FetchMedicationHistory>(_onFetchMedicationHistory);
  }

  // Medication handlers
  Future<void> _onFetchMedications(
    FetchMedications event,
    Emitter<MedicationState> emit,
  ) async {
    try {
      emit(MedicationLoading());
      final medications = await _medicationService.getMedications(
        residentId: event.residentId,
      );

      emit(MedicationsLoaded(
        medications: medications,
        filteredMedications: medications,
      ));
    } catch (e) {
      emit(MedicationError(e.toString()));
    }
  }

  Future<void> _onFetchMedicationById(
    FetchMedicationById event,
    Emitter<MedicationState> emit,
  ) async {
    try {
      emit(MedicationLoading());
      final medication = await _medicationService.getMedicationById(
        event.medicationId,
      );

      emit(MedicationDetailsLoaded(medication));
    } catch (e) {
      emit(MedicationError(e.toString()));
    }
  }

  Future<void> _onCreateMedication(
    CreateMedication event,
    Emitter<MedicationState> emit,
  ) async {
    try {
      emit(MedicationLoading());
      final newMedication = await _medicationService.createAssignedMedicine(
        event.medication,
      );
      final currentState = state;
      if (currentState is MedicationsLoaded) {
        final updatedMedications = [newMedication, ...currentState.medications];
        emit(MedicationsLoaded(
          medications: updatedMedications,
          filteredMedications: _applyFilters(
            updatedMedications,
            currentState.currentStatus,
            currentState.searchQuery,
          ),
          currentStatus: currentState.currentStatus,
          searchQuery: currentState.searchQuery,
        ));
      }
      emit(MedicationCreated(newMedication));
    } catch (e) {
      emit(MedicationError(e.toString()));
    }
  }

  Future<void> _onUpdateMedication(
    UpdateMedication event,
    Emitter<MedicationState> emit,
  ) async {
    try {
      emit(MedicationLoading());
      final updatedMedication = await _medicationService.updateAssignedMedicine(
        event.medication,
      );
      final currentState = state;
      if (currentState is MedicationsLoaded) {
        final updatedMedications = currentState.medications.map((medication) {
          return medication.assignMedicineId == updatedMedication.assignMedicineId
              ? updatedMedication
              : medication;
        }).toList();
        emit(MedicationsLoaded(
          medications: updatedMedications,
          filteredMedications: _applyFilters(
            updatedMedications,
            currentState.currentStatus,
            currentState.searchQuery,
          ),
          currentStatus: currentState.currentStatus,
          searchQuery: currentState.searchQuery,
        ));
      }
      emit(MedicationUpdated(updatedMedication));
    } catch (e) {
      emit(MedicationError(e.toString()));
    }
  }

  Future<void> _onDeleteMedication(
    DeleteMedication event,
    Emitter<MedicationState> emit,
  ) async {
    try {
      emit(MedicationLoading());
      await _medicationService.deleteAssignedMedicine(event.medicationId);
      final currentState = state;
      if (currentState is MedicationsLoaded) {
        final updatedMedications = currentState.medications
            .where((medication) => medication.assignMedicineId != event.medicationId)
            .toList();
        emit(MedicationsLoaded(
          medications: updatedMedications,
          filteredMedications: _applyFilters(
            updatedMedications,
            currentState.currentStatus,
            currentState.searchQuery,
          ),
          currentStatus: currentState.currentStatus,
          searchQuery: currentState.searchQuery,
        ));
      }
      emit(MedicationDeleted(event.medicationId));
    } catch (e) {
      emit(MedicationError(e.toString()));
    }
  }

  void _onFilterMedications(
    FilterMedications event,
    Emitter<MedicationState> emit,
  ) {
    final currentState = state;
    if (currentState is MedicationsLoaded) {
      final filteredMedications = _applyFilters(
        currentState.medications,
        event.status,
        event.searchQuery,
      );

      emit(MedicationsLoaded(
        medications: currentState.medications,
        filteredMedications: filteredMedications,
        currentStatus: event.status,
        searchQuery: event.searchQuery,
      ));
    }
  }

  // Medication intake handlers
  Future<void> _onFetchMedicationIntakes(
    FetchMedicationIntakes event,
    Emitter<MedicationState> emit,
  ) async {
    try {
      emit(MedicationLoading());
      final intakes = await _medicationService.getMedicationIntakes(
        residentId: event.residentId,
        medicationId: event.medicationId,
        startDate: event.startDate,
        endDate: event.endDate,
      );

      emit(MedicationIntakesLoaded(intakes));
    } catch (e) {
      emit(MedicationError(e.toString()));
    }
  }

  Future<void> _onRecordMedicationIntake(
    RecordMedicationIntake event,
    Emitter<MedicationState> emit,
  ) async {
    try {
      emit(MedicationLoading());
      final intake = await _medicationService.recordMedicationIntake(
        event.intake,
      );

      emit(MedicationIntakeRecorded(intake));
    } catch (e) {
      emit(MedicationError(e.toString()));
    }
  }

  // Medication feedback handlers
  Future<void> _onFetchMedicationFeedback(
    FetchMedicationFeedback event,
    Emitter<MedicationState> emit,
  ) async {
    try {
      emit(MedicationLoading());
      final feedback = await _medicationService.getMedicationFeedback(
        residentId: event.residentId,
        medicationId: event.medicationId,
      );

      emit(MedicationFeedbackLoaded(feedback));
    } catch (e) {
      emit(MedicationError(e.toString()));
    }
  }

  Future<void> _onAddMedicationFeedback(
    AddMedicationFeedback event,
    Emitter<MedicationState> emit,
  ) async {
    try {
      emit(MedicationLoading());
      final feedback = await _medicationService.addMedicationFeedback(
        event.feedback,
      );

      emit(MedicationFeedbackAdded(feedback));
    } catch (e) {
      emit(MedicationError(e.toString()));
    }
  }

  // Doctor notes handlers
  Future<void> _onFetchDoctorNotes(
    FetchDoctorNotes event,
    Emitter<MedicationState> emit,
  ) async {
    try {
      emit(MedicationLoading());
      final notes = await _medicationService.getDoctorNotes(
        residentId: event.residentId,
      );

      emit(DoctorNotesLoaded(notes));
    } catch (e) {
      emit(MedicationError(e.toString()));
    }
  }

  Future<void> _onAddDoctorNote(
    AddDoctorNote event,
    Emitter<MedicationState> emit,
  ) async {
    try {
      emit(MedicationLoading());
      final note = await _medicationService.addDoctorNote(
        event.note,
      );

      emit(DoctorNoteAdded(note));
    } catch (e) {
      emit(MedicationError(e.toString()));
    }
  }

  // Medication history handlers

  Future<void> _onFetchMedicationHistory(
    FetchMedicationHistory event,
    Emitter<MedicationState> emit,
  ) async {
    try {
      emit(MedicationLoading());
      final history = await _medicationService.getMedicationHistory(
        residentId: event.residentId,
      );

      emit(MedicationHistoryLoaded(history));
    } catch (e) {
      emit(MedicationError(e.toString()));
    }
  }



  // Helper method to apply filters
  List<ResidentAssignedMedicine> _applyFilters(
    List<ResidentAssignedMedicine> medications,
    String? status,
    String? searchQuery,
  ) {
    var filteredList = medications;

    // Filter by status (isActive)
    if (status != null && status.isNotEmpty) {
      final isActive = status.toLowerCase() == 'active';
      filteredList = filteredList.where((m) => m.isActive == isActive).toList();
    }

    // Filter by search query (medicine name, dosage, notes)
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase();
      filteredList = filteredList.where((m) {
        return m.medicine.medicineName.toLowerCase().contains(query) ||
            m.medicine.dosage.toLowerCase().contains(query) ||
            (m.notes != null && m.notes!.toLowerCase().contains(query));
      }).toList();
    }

    return filteredList;
  }
}
