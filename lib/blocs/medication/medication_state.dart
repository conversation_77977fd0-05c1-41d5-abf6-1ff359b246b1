import 'package:equatable/equatable.dart';
import '../../models/assigned_medicine.dart';
import '../../models/doctor_note.dart';
import '../../models/medication_feedback.dart';
import '../../models/medication_intake.dart';

abstract class MedicationState extends Equatable {
  const MedicationState();
  
  @override
  List<Object?> get props => [];
}

// Base States
class MedicationInitial extends MedicationState {}

class MedicationLoading extends MedicationState {}

class MedicationError extends MedicationState {
  final String message;

  const MedicationError(this.message);

  @override
  List<Object> get props => [message];
}

// Medication States
class MedicationsLoaded extends MedicationState {
  final List<ResidentAssignedMedicine> medications;
  final List<ResidentAssignedMedicine> filteredMedications;
  final String? currentStatus;
  final String? searchQuery;

  const MedicationsLoaded({
    required this.medications,
    required this.filteredMedications,
    this.currentStatus,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [
    medications, 
    filteredMedications, 
    currentStatus, 
    searchQuery
  ];
}

class MedicationDetailsLoaded extends MedicationState {
  final ResidentAssignedMedicine medication;

  const MedicationDetailsLoaded(this.medication);

  @override
  List<Object> get props => [medication];
}

class MedicationCreated extends MedicationState {
  final ResidentAssignedMedicine medication;

  const MedicationCreated(this.medication);

  @override
  List<Object> get props => [medication];
}

class MedicationUpdated extends MedicationState {
  final ResidentAssignedMedicine medication;

  const MedicationUpdated(this.medication);

  @override
  List<Object> get props => [medication];
}

class MedicationDeleted extends MedicationState {
  final String medicationId;

  const MedicationDeleted(this.medicationId);

  @override
  List<Object> get props => [medicationId];
}

// Medication Intake States
class MedicationIntakesLoaded extends MedicationState {
  final List<MedicationIntake> intakes;

  const MedicationIntakesLoaded(this.intakes);

  @override
  List<Object> get props => [intakes];
}

class MedicationIntakeRecorded extends MedicationState {
  final MedicationIntake intake;

  const MedicationIntakeRecorded(this.intake);

  @override
  List<Object> get props => [intake];
}

// Medication Feedback States
class MedicationFeedbackLoaded extends MedicationState {
  final List<MedicationFeedback> feedback;

  const MedicationFeedbackLoaded(this.feedback);

  @override
  List<Object> get props => [feedback];
}

class MedicationFeedbackAdded extends MedicationState {
  final MedicationFeedback feedback;

  const MedicationFeedbackAdded(this.feedback);

  @override
  List<Object> get props => [feedback];
}

// Doctor Notes States
class DoctorNotesLoaded extends MedicationState {
  final List<DoctorNote> notes;

  const DoctorNotesLoaded(this.notes);

  @override
  List<Object> get props => [notes];
}

class DoctorNoteAdded extends MedicationState {
  final DoctorNote note;

  const DoctorNoteAdded(this.note);

  @override
  List<Object> get props => [note];
}

class MedicationHistoryLoaded extends MedicationState {
  final List<MedicationIntake> history;

  const MedicationHistoryLoaded(this.history);

  @override
  List<Object> get props => [history];
}