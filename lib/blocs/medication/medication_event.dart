import 'package:equatable/equatable.dart';
import '../../models/assigned_medicine.dart';
import '../../models/doctor_note.dart';
import '../../models/medication_feedback.dart';
import '../../models/medication_intake.dart';

abstract class MedicationEvent extends Equatable {
  const MedicationEvent();

  @override
  List<Object?> get props => [];
}

// Medication Events
class FetchMedications extends MedicationEvent {
  final String? residentId;

  const FetchMedications({this.residentId});

  @override
  List<Object?> get props => [residentId];
}

class FetchMedicationById extends MedicationEvent {
  final String medicationId;

  const FetchMedicationById(this.medicationId);

  @override
  List<Object> get props => [medicationId];
}

class CreateMedication extends MedicationEvent {
  final ResidentAssignedMedicine medication;

  const CreateMedication(this.medication);

  @override
  List<Object> get props => [medication];
}

class UpdateMedication extends MedicationEvent {
  final ResidentAssignedMedicine medication;

  const UpdateMedication(this.medication);

  @override
  List<Object> get props => [medication];
}

class DeleteMedication extends MedicationEvent {
  final String medicationId;

  const DeleteMedication(this.medicationId);

  @override
  List<Object> get props => [medicationId];
}

class FilterMedications extends MedicationEvent {
  final String? status;
  final String? searchQuery;

  const FilterMedications({
    this.status,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [status, searchQuery];
}

// Medication Intake Events
class FetchMedicationIntakes extends MedicationEvent {
  final String? residentId;
  final String? medicationId;
  final DateTime? startDate;
  final DateTime? endDate;

  const FetchMedicationIntakes({
    this.residentId,
    this.medicationId,
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [residentId, medicationId, startDate, endDate];
}

class RecordMedicationIntake extends MedicationEvent {
  final MedicationIntake intake;

  const RecordMedicationIntake(this.intake);

  @override
  List<Object> get props => [intake];
}

// Medication Feedback Events
class FetchMedicationFeedback extends MedicationEvent {
  final String? residentId;
  final String? medicationId;

  const FetchMedicationFeedback({
    this.residentId,
    this.medicationId,
  });

  @override
  List<Object?> get props => [residentId, medicationId];
}

class AddMedicationFeedback extends MedicationEvent {
  final MedicationFeedback feedback;

  const AddMedicationFeedback(this.feedback);

  @override
  List<Object> get props => [feedback];
}

// Doctor Notes Events
class FetchDoctorNotes extends MedicationEvent {
  final String? residentId;

  const FetchDoctorNotes({this.residentId});

  @override
  List<Object?> get props => [residentId];
}

class AddDoctorNote extends MedicationEvent {
  final DoctorNote note;

  const AddDoctorNote(this.note);

  @override
  List<Object> get props => [note];
}

class FetchMedicationHistory extends MedicationEvent {
  final String residentId;
  final String residentUUID;

  const FetchMedicationHistory({
    required this.residentId,
    required this.residentUUID,
  });

  @override
  List<Object> get props => [residentId, residentUUID];
}