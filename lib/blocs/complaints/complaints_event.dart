import 'package:equatable/equatable.dart';
import '../../models/complaints.dart';

abstract class ComplaintsEvent extends Equatable {
  const ComplaintsEvent();

  @override
  List<Object?> get props => [];
}

class FetchComplaints extends ComplaintsEvent {}

class FetchComplaintById extends ComplaintsEvent {
  final String complaintId;

  const FetchComplaintById(this.complaintId);

  @override
  List<Object?> get props => [complaintId];
}

class CreateComplaint extends ComplaintsEvent {
  final Complaints complaint;

  const CreateComplaint(this.complaint);

  @override
  List<Object?> get props => [complaint];
}

class UpdateComplaintStatus extends ComplaintsEvent {
  final String complaintId;
  final String newStatus;

  const UpdateComplaintStatus(this.complaintId, this.newStatus);

  @override
  List<Object?> get props => [complaintId, newStatus];
}

class DeleteComplaint extends ComplaintsEvent {
  final String complaintId;

  const DeleteComplaint(this.complaintId);

  @override
  List<Object?> get props => [complaintId];
}

class FilterComplaints extends ComplaintsEvent {
  final String? level;
  final String? status;
  final String? searchQuery;

  const FilterComplaints({
    this.level,
    this.status,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [level, status, searchQuery];
}

class AddComplaintTimelineEvent extends ComplaintsEvent {
  final String complaintId;
  final ComplaintTimelineEvent timelineEvent;

  const AddComplaintTimelineEvent(this.complaintId, this.timelineEvent);

  @override
  List<Object?> get props => [complaintId, timelineEvent];
}

class AddComplaintAttachment extends ComplaintsEvent {
  final String complaintId;
  final String attachmentPath;

  const AddComplaintAttachment(this.complaintId, this.attachmentPath);

  @override
  List<Object?> get props => [complaintId, attachmentPath];
}
