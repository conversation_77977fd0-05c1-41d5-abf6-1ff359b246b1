import 'package:equatable/equatable.dart';
import '../../models/complaints.dart';

abstract class ComplaintsState extends Equatable {
  const ComplaintsState();

  @override
  List<Object?> get props => [];
}

class ComplaintsInitial extends ComplaintsState {}

class ComplaintsLoading extends ComplaintsState {}

class ComplaintsLoaded extends ComplaintsState {
  final List<Complaints> complaints;
  final List<Complaints> filteredComplaints;
  final String? currentLevel;
  final String? currentStatus;
  final String? searchQuery;

  const ComplaintsLoaded({
    required this.complaints,
    required this.filteredComplaints,
    this.currentLevel,
    this.currentStatus,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [
        complaints,
        filteredComplaints,
        currentLevel,
        currentStatus,
        searchQuery,
      ];
}

class ComplaintDetailLoaded extends ComplaintsState {
  final Complaints complaint;

  const ComplaintDetailLoaded(this.complaint);

  @override
  List<Object?> get props => [complaint];
}

class ComplaintsError extends ComplaintsState {
  final String message;

  const ComplaintsError(this.message);

  @override
  List<Object?> get props => [message];
}

class ComplaintCreated extends ComplaintsState {
  final Complaints complaint;

  const ComplaintCreated(this.complaint);

  @override
  List<Object?> get props => [complaint];
}

class ComplaintUpdated extends ComplaintsState {
  final Complaints complaint;

  const ComplaintUpdated(this.complaint);

  @override
  List<Object?> get props => [complaint];
}

class ComplaintDeleted extends ComplaintsState {
  final String complaintId;

  const ComplaintDeleted(this.complaintId);

  @override
  List<Object?> get props => [complaintId];
}

class ComplaintsEmpty extends ComplaintsState {}