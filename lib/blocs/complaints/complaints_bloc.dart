import 'package:flutter_bloc/flutter_bloc.dart';
import '../../models/complaints.dart';
import '../../services/complaints_service.dart';
import 'complaints_event.dart';
import 'complaints_state.dart';

class ComplaintsBloc extends Bloc<ComplaintsEvent, ComplaintsState> {
  final ComplaintsService _complaintsService;

  ComplaintsBloc(this._complaintsService) : super(ComplaintsInitial()) {
    on<FetchComplaints>(_onFetchComplaints);
    on<FetchComplaintById>(_onFetchComplaintById);
    on<CreateComplaint>(_onCreateComplaint);
    on<UpdateComplaintStatus>(_onUpdateComplaintStatus);
    on<DeleteComplaint>(_onDeleteComplaint);
    on<FilterComplaints>(_onFilterComplaints);
    on<AddComplaintTimelineEvent>(_onAddComplaintTimelineEvent);
    on<AddComplaintAttachment>(_onAddComplaintAttachment);
  }

  Future<void> _onFetchComplaints(
    FetchComplaints event,
    Emitter<ComplaintsState> emit,
  ) async {
    try {
      emit(ComplaintsLoading());
      print('Fetching complaints...');
      final complaints = await _complaintsService.getComplaints();
      print('Fetched ${complaints.length} complaints');

      if (complaints.isEmpty) {
        print('No complaints found, emitting ComplaintsEmpty');
        emit(ComplaintsEmpty());
      } else {
        print('Emitting ComplaintsLoaded with ${complaints.length} complaints');
        emit(ComplaintsLoaded(
          complaints: complaints,
          filteredComplaints: complaints,
        ));
      }
    } catch (e) {
      print('Error in ComplaintsBloc._onFetchComplaints: $e');
      emit(ComplaintsError(e.toString()));
    }
  }

  Future<void> _onFetchComplaintById(
    FetchComplaintById event,
    Emitter<ComplaintsState> emit,
  ) async {
    try {
      emit(ComplaintsLoading());
      final complaint =
          await _complaintsService.getComplaintById(event.complaintId);
      emit(ComplaintDetailLoaded(complaint));
    } catch (e) {
      emit(ComplaintsError(e.toString()));
    }
  }

  Future<void> _onCreateComplaint(
    CreateComplaint event,
    Emitter<ComplaintsState> emit,
  ) async {
    try {
      final newComplaint =
          await _complaintsService.createComplaint(event.complaint);

      final currentState = state;
      if (currentState is ComplaintsLoaded) {
        final updatedComplaints = [newComplaint, ...currentState.complaints];
        emit(ComplaintsLoaded(
          complaints: updatedComplaints,
          filteredComplaints: _applyFilters(
            updatedComplaints,
            currentState.currentLevel,
            currentState.currentStatus,
            currentState.searchQuery,
          ),
          currentLevel: currentState.currentLevel,
          currentStatus: currentState.currentStatus,
          searchQuery: currentState.searchQuery,
        ));
      }

      emit(ComplaintCreated(newComplaint));
    } catch (e) {
      emit(ComplaintsError(e.toString()));
    }
  }

  Future<void> _onUpdateComplaintStatus(
    UpdateComplaintStatus event,
    Emitter<ComplaintsState> emit,
  ) async {
    try {
      final updatedComplaint = await _complaintsService.updateComplaint(
        event.complaintId,
        {'status': event.newStatus},
      );

      final currentState = state;
      if (currentState is ComplaintsLoaded) {
        final updatedComplaints = currentState.complaints.map((complaint) {
          return complaint.complaintId == event.complaintId
              ? updatedComplaint
              : complaint;
        }).toList();

        emit(ComplaintsLoaded(
          complaints: updatedComplaints,
          filteredComplaints: _applyFilters(
            updatedComplaints,
            currentState.currentLevel,
            currentState.currentStatus,
            currentState.searchQuery,
          ),
          currentLevel: currentState.currentLevel,
          currentStatus: currentState.currentStatus,
          searchQuery: currentState.searchQuery,
        ));
      }

      emit(ComplaintUpdated(updatedComplaint));
    } catch (e) {
      emit(ComplaintsError(e.toString()));
    }
  }

  Future<void> _onDeleteComplaint(
    DeleteComplaint event,
    Emitter<ComplaintsState> emit,
  ) async {
    try {
      final success =
          await _complaintsService.deleteComplaint(event.complaintId);

      if (success) {
        final currentState = state;
        if (currentState is ComplaintsLoaded) {
          final updatedComplaints = currentState.complaints
              .where((complaint) => complaint.complaintId != event.complaintId)
              .toList();

          if (updatedComplaints.isEmpty) {
            emit(ComplaintsEmpty());
          } else {
            emit(ComplaintsLoaded(
              complaints: updatedComplaints,
              filteredComplaints: _applyFilters(
                updatedComplaints,
                currentState.currentLevel,
                currentState.currentStatus,
                currentState.searchQuery,
              ),
              currentLevel: currentState.currentLevel,
              currentStatus: currentState.currentStatus,
              searchQuery: currentState.searchQuery,
            ));
          }
        }

        emit(ComplaintDeleted(event.complaintId));
      } else {
        emit(ComplaintsError('Failed to delete complaint'));
      }
    } catch (e) {
      emit(ComplaintsError(e.toString()));
    }
  }

  void _onFilterComplaints(
    FilterComplaints event,
    Emitter<ComplaintsState> emit,
  ) {
    final currentState = state;
    if (currentState is ComplaintsLoaded) {
      final filteredComplaints = _applyFilters(
        currentState.complaints,
        event.level,
        event.status,
        event.searchQuery,
      );

      emit(ComplaintsLoaded(
        complaints: currentState.complaints,
        filteredComplaints: filteredComplaints,
        currentLevel: event.level,
        currentStatus: event.status,
        searchQuery: event.searchQuery,
      ));
    }
  }

  Future<void> _onAddComplaintTimelineEvent(
    AddComplaintTimelineEvent event,
    Emitter<ComplaintsState> emit,
  ) async {
    try {
      final updatedComplaint = await _complaintsService.addEvent(
        event.complaintId,
        event.timelineEvent,
      );

      final currentState = state;
      if (currentState is ComplaintsLoaded) {
        final updatedComplaints = currentState.complaints.map((complaint) {
          return complaint.complaintId == event.complaintId
              ? updatedComplaint
              : complaint;
        }).toList();

        emit(ComplaintsLoaded(
          complaints: updatedComplaints,
          filteredComplaints: _applyFilters(
            updatedComplaints,
            currentState.currentLevel,
            currentState.currentStatus,
            currentState.searchQuery,
          ),
          currentLevel: currentState.currentLevel,
          currentStatus: currentState.currentStatus,
          searchQuery: currentState.searchQuery,
        ));
      }

      emit(ComplaintUpdated(updatedComplaint));
    } catch (e) {
      emit(ComplaintsError(e.toString()));
    }
  }

  Future<void> _onAddComplaintAttachment(
    AddComplaintAttachment event,
    Emitter<ComplaintsState> emit,
  ) async {
    try {
      final updatedComplaint = await _complaintsService.addAttachment(
        event.complaintId,
        ComplaintAttachment(
          attachmentId: DateTime.now().millisecondsSinceEpoch.toString(),
          attachmentURL: event.attachmentPath,
          attachmentName: event.attachmentPath.split('/').last,
        ),
      );

      final currentState = state;
      if (currentState is ComplaintsLoaded) {
        final updatedComplaints = currentState.complaints.map((complaint) {
          return complaint.complaintId == event.complaintId
              ? updatedComplaint
              : complaint;
        }).toList();

        emit(ComplaintsLoaded(
          complaints: updatedComplaints,
          filteredComplaints: _applyFilters(
            updatedComplaints,
            currentState.currentLevel,
            currentState.currentStatus,
            currentState.searchQuery,
          ),
          currentLevel: currentState.currentLevel,
          currentStatus: currentState.currentStatus,
          searchQuery: currentState.searchQuery,
        ));
      }

      emit(ComplaintUpdated(updatedComplaint));
    } catch (e) {
      emit(ComplaintsError(e.toString()));
    }
  }

  List<Complaints> _applyFilters(
    List<Complaints> complaints,
    String? level,
    String? status,
    String? searchQuery,
  ) {
    List<Complaints> filteredComplaints = List.from(complaints);

    // Apply level filter
    if (level != null && level.isNotEmpty) {
      filteredComplaints = filteredComplaints.where((complaint) {
        return complaint.complaintLevel.toLowerCase() == level.toLowerCase();
      }).toList();
    }

    // Apply status filter
    if (status != null && status.isNotEmpty) {
      filteredComplaints = filteredComplaints.where((complaint) {
        return complaint.status.toLowerCase() == status.toLowerCase();
      }).toList();
    }

    // Apply search query filter
    if (searchQuery != null && searchQuery.isNotEmpty) {
      filteredComplaints = filteredComplaints.where((complaint) {
        return complaint.complaintName
                .toLowerCase()
                .contains(searchQuery.toLowerCase()) ||
            complaint.description
                .toLowerCase()
                .contains(searchQuery.toLowerCase()) ||
            complaint.residentDetails!.userDetails!.firstName
                .toLowerCase()
                .contains(searchQuery.toLowerCase());
      }).toList();
    }

    return filteredComplaints;
  }
}
