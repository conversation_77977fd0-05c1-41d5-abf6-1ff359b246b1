import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:carerez/models/user_model.dart';
import 'package:carerez/repositories/user_repository.dart';
import 'user_event.dart';
import 'user_state.dart';

class UserBloc extends Bloc<UserEvent, UserState> {
  final UserRepository _userRepository;
  UserModel? _currentUser;

  UserBloc(this._userRepository) : super(UserInitial()) {
    on<FetchCurrentUser>(_onFetchCurrentUser);
    on<UpdateUserProfile>(_onUpdateUserProfile);
    on<ClearUserData>(_onClearUserData);
    on<FetchStaffTasks>(_onFetchStaffTasks);
  }

  Future<void> _onFetchCurrentUser(
    FetchCurrentUser event,
    Emitter<UserState> emit,
  ) async {
    try {
      emit(UserLoading());
      final cachedUser = await _userRepository.getCachedUser();
      if (cachedUser != null) {
        _currentUser = cachedUser;
        emit(UserLoaded(cachedUser));
      }
      final user = await _userRepository.getCurrentUser();
      _currentUser = user;
      emit(UserLoaded(user));
    } catch (e) {
      emit(UserError(e.toString()));
    }
  }

  Future<void> _onUpdateUserProfile(
    UpdateUserProfile event,
    Emitter<UserState> emit,
  ) async {
    // Implementation for updating user profile
    // This would typically call an API endpoint
  }

  Future<void> _onClearUserData(
    ClearUserData event,
    Emitter<UserState> emit,
  ) async {
    try {
      await _userRepository.clearUserData();
      emit(UserInitial());
    } catch (e) {
      emit(UserError(e.toString()));
    }
  }

  Future<void> _onFetchStaffTasks(
    FetchStaffTasks event,
    Emitter<UserState> emit,
  ) async {
    try {
      final tasks = await _userRepository.getStaffTasks(event.staffId);
      if (_currentUser != null) {
        emit(UserAndTasksLoaded(_currentUser!, tasks));
      } else {
        emit(UserTasksLoaded(tasks));
      }
    } catch (e) {
      emit(UserError(e.toString()));
    }
  }
}
