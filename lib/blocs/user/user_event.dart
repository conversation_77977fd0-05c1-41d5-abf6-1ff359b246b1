import 'package:equatable/equatable.dart';

abstract class UserEvent extends Equatable {
  const UserEvent();

  @override
  List<Object> get props => [];
}

class FetchCurrentUser extends UserEvent {}

class FetchStaffTasks extends UserEvent {
  final String staffId;

  const FetchStaffTasks(this.staffId);

  @override
  List<Object> get props => [staffId];
}

class UpdateUserProfile extends UserEvent {
  final Map<String, dynamic> userData;

  const UpdateUserProfile(this.userData);

  @override
  List<Object> get props => [userData];
}

class ClearUserData extends UserEvent {}