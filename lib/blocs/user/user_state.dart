import 'package:carerez/models/staff_task.dart';
import 'package:equatable/equatable.dart';
import 'package:carerez/models/user_model.dart';

abstract class UserState extends Equatable {
  const UserState();

  @override
  List<Object?> get props => [];
}

class UserInitial extends UserState {}

class UserLoading extends UserState {}

class UserLoaded extends UserState {
  final UserModel user;

  const UserLoaded(this.user);

  @override
  List<Object> get props => [user];
}

class UserError extends UserState {
  final String message;

  const UserError(this.message);

  @override
  List<Object> get props => [message];
}

class UserTasksLoaded extends UserState {
  final List<StaffTask> tasks;

  const UserTasksLoaded(this.tasks);

  @override
  List<Object> get props => [tasks];
}

class UserAndTasksLoaded extends UserState {
  final UserModel user;
  final List<StaffTask> tasks;

  const UserAndTasksLoaded(this.user, this.tasks);

  @override
  List<Object> get props => [user, tasks];
}
