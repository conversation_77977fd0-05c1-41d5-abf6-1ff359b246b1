import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../models/mar_sheet_model.dart';
import '../../../services/mar_sheet_service.dart';
import 'mar_sheet_event.dart';
import 'mar_sheet_state.dart';
// Bloc
class MarSheetBloc extends Bloc<MarSheetEvent, MarSheetState> {
  final MarSheetService _service;
  MarSheetBloc({MarSheetService? service})
      : _service = service ?? MarSheetService(),
        super(MarSheetInitial()) {
    on<FetchMarSheet>(_onFetchMarSheet);
    on<AddMarSheetEntry>(_onAddMarSheetEntry);
    on<UpdateMarSheetEntry>(_onUpdateMarSheetEntry);
  }

  Future<void> _onFetchMarSheet(FetchMarSheet event, Emitter<MarSheetState> emit) async {
    emit(MarSheetLoading());
    try {
      final List<Medication> medications = await _service.fetchMarSheets(
        residentUUID: event.residentUUID,
        startDate: event.startDate,
        endDate: event.endDate,
      );
      emit(MarSheetLoaded(medications));
    } catch (e) {
      emit(MarSheetError(e.toString()));
    }
  }

  Future<void> _onAddMarSheetEntry(AddMarSheetEntry event, Emitter<MarSheetState> emit) async {
    print('Bloc: Received AddMarSheetEntry event');
    try {
      emit(MarSheetSubmitting('Adding MAR Sheet entry...'));
      await _service.addMarSheetEntry(event.entry, event.residentUUID);
      add(FetchMarSheet(
        residentUUID: event.residentUUID,
        startDate: event.startDate,
        endDate: event.endDate,
      ));
      emit(MarSheetSubmissionSuccess('MAR Sheet entry added successfully'));
    } catch (e) {
      emit(MarSheetSubmissionError('Failed to add MAR Sheet entry: ${e.toString()}'));
      print('Bloc: Error in addMarSheetEntry: \\n$e');
      emit(MarSheetError(e.toString()));
    }
  }

  Future<void> _onUpdateMarSheetEntry(UpdateMarSheetEntry event, Emitter<MarSheetState> emit) async {
    try {
      await _service.updateMarSheetEntry(event.entry);
      add(FetchMarSheet(
        residentUUID: event.residentUUID,
        startDate: event.startDate,
        endDate: event.endDate,
      ));
    } catch (e) {
      emit(MarSheetError(e.toString()));
    }
  }
}
