import 'package:equatable/equatable.dart';

import '../../models/mar_sheet_model.dart';

abstract class MarSheetState extends Equatable {
  @override
  List<Object?> get props => [];
}

class MarSheetInitial extends MarSheetState {}
class MarSheetLoading extends MarSheetState {}




class MarSheetLoaded extends MarSheetState {
  final List<Medication> medications;
  MarSheetLoaded(this.medications);
  @override
  List<Object?> get props => [medications];
}
class MarSheetError extends MarSheetState {
  final String message;
  MarSheetError(this.message);
  @override
  List<Object?> get props => [message];
}

class MarSheetSubmitting extends MarSheetState {
  final String message;
  MarSheetSubmitting(this.message);
  @override
  List<Object?> get props => [message];
}

class MarSheetSubmissionSuccess extends MarSheetState {
  final String message;
  MarSheetSubmissionSuccess(this.message);
  @override
  List<Object?> get props => [message];
}

class MarSheetSubmissionError extends MarSheetState {
  final String message;
  MarSheetSubmissionError(this.message);
  @override
  List<Object?> get props => [message];
}
