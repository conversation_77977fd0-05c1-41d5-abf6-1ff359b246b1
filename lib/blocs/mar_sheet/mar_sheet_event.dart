import 'package:equatable/equatable.dart';

import '../../models/mar_sheet_model.dart';

abstract class MarSheetEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class FetchMarSheet extends MarSheetEvent {
  final String residentUUID;
  final DateTime startDate;
  final DateTime endDate;

  FetchMarSheet({
    required this.residentUUID,
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object?> get props => [residentUUID, startDate, endDate];
}

class AddMarSheetEntry extends MarSheetEvent {
  final Map<String, dynamic> entry;
  final String residentId;
  final String residentUUID;
  final DateTime startDate;
  final DateTime endDate;

  AddMarSheetEntry({
    required this.entry,
    required this.residentId,
    required this.residentUUID,
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object?> get props => [entry, residentId, residentUUID, startDate, endDate];
}

class UpdateMarSheetEntry extends MarSheetEvent {
  final MarSheet entry;
  final String residentUUID;
  final DateTime startDate;
  final DateTime endDate;

  UpdateMarSheetEntry({
    required this.entry,
    required this.residentUUID,
    required this.startDate,
    required this.endDate,
  });
  @override
  List<Object?> get props => [entry, residentUUID, startDate, endDate];
}