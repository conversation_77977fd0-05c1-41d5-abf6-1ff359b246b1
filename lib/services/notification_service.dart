import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import '../models/notification.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class NotificationService {
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  
  static const String _storageKey = 'notifications';

  Future<void> initialize() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings();

    const InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse details) async {
        // Handle notification tap
      },
    );
  }

  Future<void> showNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'carerez_channel',
      'CareRez Notifications',
      importance: Importance.max,
      priority: Priority.high,
    );

    const NotificationDetails platformChannelSpecifics =
        NotificationDetails(android: androidPlatformChannelSpecifics);

    await _flutterLocalNotificationsPlugin.show(
      0,
      title,
      body,
      platformChannelSpecifics,
      payload: payload,
    );
  }

  Future<List<Notification>> getNotifications() async {
    final prefs = await SharedPreferences.getInstance();
    final String? notificationsJson = prefs.getString(_storageKey);
    
    if (notificationsJson == null) return [];
    
    final List<dynamic> decoded = json.decode(notificationsJson);
    return decoded.map((item) => Notification(
      id: item['id'],
      title: item['title'],
      description: item['description'],
      time: item['time'],
      image: item['image'],
      isRead: item['isRead'],
      type: item['type'],
    )).toList();
  }

  Future<void> saveNotifications(List<Notification> notifications) async {
    final prefs = await SharedPreferences.getInstance();
    final encoded = json.encode(notifications.map((n) => {
      'id': n.id,
      'title': n.title,
      'description': n.description,
      'time': n.time,
      'image': n.image,
      'isRead': n.isRead,
      'type': n.type,
    }).toList());
    
    await prefs.setString(_storageKey, encoded);
  }
}