import 'dart:convert';
import 'package:carerez/config/env_config.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../models/handoff_note.dart';
import 'http_client.dart';

class HandoffNoteService {
  final String? _authToken;
  final String baseUrl;

  HandoffNoteService([this._authToken])
      : baseUrl = '${EnvConfig.apiBaseUrl}/handoff-notes';

  Future<List<HandoffNote>> getHandoffNotes({String? residentId}) async {
    try {
      // For testing purposes, return mock dat
      final response = await HttpClientService.get(
        '$baseUrl',
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => HandoffNote.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load handoff notes: ${response.statusCode}');
      }

    } catch (e) {
      debugPrint('Error fetching handoff notes: $e');
      // Return mock data as fallback
      return _getMockHandoffNotes(residentId);
    }
  }

  Future<HandoffNote> getHandoffNoteById(String id) async {
    try {
      // For testing purposes, return mock data
      final notes = _getMockHandoffNotes(null);
      final note = notes.firstWhere((note) => note.id == id, 
          orElse: () => throw Exception('Handoff note not found'));
      return note;

      // Uncomment for actual API implementation
      /*
      final response = await HttpClient.get(
        '$baseUrl/$id',
        token: _authToken,
      );

      if (response.statusCode == 200) {
        return HandoffNote.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to load handoff note: ${response.statusCode}');
      }
      */
    } catch (e) {
      debugPrint('Error fetching handoff note: $e');
      throw Exception('Failed to load handoff note: $e');
    }
  }

  Future<HandoffNote> createHandoffNote(HandoffNote note) async {
    try {
      // For testing purposes, return the note with a generated ID
      return note.copyWith(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        createdAt: DateTime.now(),
      );

      // Uncomment for actual API implementation
      /*
      final response = await HttpClient.post(
        baseUrl,
        token: _authToken,
        body: note.toJson(),
      );

      if (response.statusCode == 201) {
        return HandoffNote.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to create handoff note: ${response.statusCode}');
      }
      */
    } catch (e) {
      debugPrint('Error creating handoff note: $e');
      throw Exception('Failed to create handoff note: $e');
    }
  }

  Future<HandoffNote> updateHandoffNote(String id, Map<String, dynamic> updates) async {
    try {
      // For testing purposes, return a mock updated note
      final notes = _getMockHandoffNotes(null);
      final noteIndex = notes.indexWhere((note) => note.id == id);
      
      if (noteIndex == -1) {
        throw Exception('Handoff note not found');
      }
      
      final updatedNote = HandoffNote.fromJson({
        ...notes[noteIndex].toJson(),
        ...updates,
      });
      
      return updatedNote;

      // Uncomment for actual API implementation
      /*
      final response = await HttpClient.patch(
        '$baseUrl/$id',
        token: _authToken,
        body: updates,
      );

      if (response.statusCode == 200) {
        return HandoffNote.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to update handoff note: ${response.statusCode}');
      }
      */
    } catch (e) {
      debugPrint('Error updating handoff note: $e');
      throw Exception('Failed to update handoff note: $e');
    }
  }

  Future<void> deleteHandoffNote(String id) async {
    try {
      // For testing purposes, just return
      return;

      // Uncomment for actual API implementation
      /*
      final response = await HttpClient.delete(
        '$baseUrl/$id',
        token: _authToken,
      );

      if (response.statusCode != 204) {
        throw Exception('Failed to delete handoff note: ${response.statusCode}');
      }
      */
    } catch (e) {
      debugPrint('Error deleting handoff note: $e');
      throw Exception('Failed to delete handoff note: $e');
    }
  }

  Future<HandoffNote> markAsCompleted(String id, String completedBy) async {
    try {
      return updateHandoffNote(id, {
        'isCompleted': true,
        'completedAt': DateTime.now().toIso8601String(),
        'completedBy': completedBy,
      });
    } catch (e) {
      debugPrint('Error marking handoff note as completed: $e');
      throw Exception('Failed to mark handoff note as completed: $e');
    }
  }

  Future<HandoffNote> addAttachment(String id, String attachmentPath) async {
    try {
      final note = await getHandoffNoteById(id);
      final List<String> updatedAttachments = [...note.attachments, attachmentPath];
      
      return await updateHandoffNote(id, {'attachments': updatedAttachments});
    } catch (e) {
      debugPrint('Error adding attachment to handoff note: $e');
      throw Exception('Failed to add attachment to handoff note: $e');
    }
  }

  // Mock data for testing
  List<HandoffNote> _getMockHandoffNotes(String? residentId) {
    final List<HandoffNote> notes = [
    ];

    if (residentId != null) {
      return notes.where((note) => note.residentId == residentId).toList();
    }
    
    return notes;
  }
}