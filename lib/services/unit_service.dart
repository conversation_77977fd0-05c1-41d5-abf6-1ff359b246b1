import 'dart:convert';
import 'package:carerez/screens/utils/snackbar_utils.dart';
import 'package:flutter/material.dart';
import '../config/env_config.dart';
import '../models/unit.dart';
import 'http_client.dart';

class UnitService {
  final String baseUrl = '${EnvConfig.apiBaseUrl}/home';

  Future<List<Unit>> getUnits() async {
    try {
      final response = await HttpClientService.get('$baseUrl/view_all_units');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        debugPrint('Response data: $responseData');

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> unitsData = responseData['data'];
          return unitsData.map((json) => Unit.fromJson(json)).toList();
        } else {
          throw Exception('Failed to parse units: ${responseData['message']}');
        }
      }else if(response.statusCode ==401) {
        throw AccessDeniedException('Unauthorized access. Please log in again.');
      }
      else {
        throw Exception(
            'Failed to load units: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      if (e is AccessDeniedException) rethrow;
      debugPrint('Error fetching units: $e');
      return [];
    }
  }

  Future<Unit> getUnitById(String unitId) async {
    try {
      final response = await HttpClientService.get('$baseUrl/$unitId');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);

        if (responseData['success'] == true && responseData['data'] != null) {
          return Unit.fromJson(responseData['data']);
        } else {
          throw Exception('Failed to parse unit: ${responseData['message']}');
        }
      } else {
        throw Exception('Failed to load unit: ${response.statusCode}');
      }
    } catch (e) {
      if (e is AccessDeniedException) rethrow;
      // Return mock data for testing
      return _getMockUnitById(unitId);
    }
  }

  // Mock data for testing

  Unit _getMockUnitById(String unitId) {
    // Generate a mock unit with the specified ID
    return Unit(
      id: unitId,
      name: 'Mock Unit',
      maxResidentCount: '100 beds',
      homeId: 'H001',
      unitColorId: 'C001',
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}
