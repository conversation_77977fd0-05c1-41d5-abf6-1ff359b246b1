import 'dart:convert';
import 'package:carerez/models/resident_finance_basic.dart';
import 'package:carerez/models/resident_budget_transaction.dart';
import 'package:carerez/services/http_client.dart';
import 'package:carerez/config/env_config.dart';

class ResidentFinanceService {
  final String apiBaseUrl = EnvConfig.apiBaseUrl;

  Future<ResidentFinanceBasic> getResidentFinanceBasic(String residentId) async {
    final response = await HttpClientService.get(
      '$apiBaseUrl/resident-finance/view/account/info/$residentId',
    );
    if (response.statusCode == 200) {
      final Map<String, dynamic> responseData = json.decode(response.body);
      if (responseData['success'] == true && responseData['data'] != null) {
        return ResidentFinanceBasic.fromJson(responseData['data']);
      } else {
        throw Exception('Failed to parse finance basic: ${responseData['message']}');
      }
    } else {
      throw Exception('Failed to load finance basic: ${response.statusCode} - ${response.body}');
    }
  }

  Future<List<ResidentBudgetTransaction>> getResidentBudgetTransactions(String residentId) async {
    final response = await HttpClientService.get(
      '$apiBaseUrl/resident-finance/transaction/view_all/$residentId',
    );
    if (response.statusCode == 200) {
      final Map<String, dynamic> responseData = json.decode(response.body);
      if (responseData['success'] == true && responseData['data'] != null) {
        final List<dynamic> txData = responseData['data'];
        return txData.map((json) => ResidentBudgetTransaction.fromJson(json)).toList();
      } else {
        throw Exception('Failed to parse transactions: ${responseData['message']}');
      }
    } else {
      throw Exception('Failed to load transactions: ${response.statusCode} - ${response.body}');
    }
  }
}

