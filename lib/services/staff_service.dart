import 'dart:convert';
import 'package:carerez/config/env_config.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../models/staff.dart';
import 'http_client.dart';

class StaffService {
  final String? _authToken;
  final String baseUrl;

  StaffService([this._authToken])
      : baseUrl = '${EnvConfig.apiBaseUrl}/staff';

  Future<List<Staff>> getStaff() async {
    try {
      final response = _authToken != null
          ? await http.get(
              Uri.parse('$baseUrl/getAllStaff'),
              headers: {
                'Authorization': 'Bearer $_authToken',
              },
            )
          : await HttpClientService.get('$baseUrl/getAllStaff');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        
        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> staffData = responseData['data'];
          return staffData.map((json) => Staff.fromJson(json)).toList();
        } else {
          throw Exception('Failed to parse staff: ${responseData['message']}');
        }
      } else {
        throw Exception('Failed to load staff: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error fetching staff: $e');
      // Return mock data if API fails
      return _getMockStaff();
    }
  }

  Future<Staff> getStaffById(String id) async {
    try {
      final response = _authToken != null
          ? await http.get(
              Uri.parse('$baseUrl/$id'),
              headers: {
                'Authorization': 'Bearer $_authToken',
              },
            )
          : await HttpClientService.get('$baseUrl/$id');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        
        if (responseData['success'] == true && responseData['data'] != null) {
          return Staff.fromJson(responseData['data']);
        } else {
          throw Exception('Failed to parse staff: ${responseData['message']}');
        }
      } else {
        throw Exception('Failed to load staff: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error fetching staff: $e');
      // Return mock data if API fails
      return _getMockStaff().firstWhere(
        (staff) => staff.id == id,
        orElse: () => _getMockStaff().first,
      );
    }
  }

  Future<Staff> createStaff(Staff staff) async {
    try {
      final response = _authToken != null
          ? await http.post(
              Uri.parse(baseUrl),
              headers: {
                'Authorization': 'Bearer $_authToken',
                'Content-Type': 'application/json',
              },
              body: json.encode(staff.toJson()),
            )
          : await HttpClientService.post(
              baseUrl,
              body: json.encode(staff.toJson()),
            );

      if (response.statusCode == 201 || response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        
        if (responseData['success'] == true && responseData['data'] != null) {
          return Staff.fromJson(responseData['data']);
        } else {
          throw Exception('Failed to create staff: ${responseData['message']}');
        }
      } else {
        throw Exception('Failed to create staff: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error creating staff: $e');
      // Return the original staff if API fails
      return staff;
    }
  }

  Future<Staff> updateStaff(String id, Map<String, dynamic> updates) async {
    try {
      final response = _authToken != null
          ? await http.put(
              Uri.parse('$baseUrl/$id'),
              headers: {
                'Authorization': 'Bearer $_authToken',
                'Content-Type': 'application/json',
              },
              body: json.encode(updates),
            )
          : await HttpClientService.put(
              '$baseUrl/$id',
              body: json.encode(updates),
            );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        
        if (responseData['success'] == true && responseData['data'] != null) {
          return Staff.fromJson(responseData['data']);
        } else {
          throw Exception('Failed to update staff: ${responseData['message']}');
        }
      } else {
        throw Exception('Failed to update staff: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error updating staff: $e');
      // Return a staff with the updates applied if API fails
      final staff = await getStaffById(id);
      return staff.copyWith(
        name: updates['name'],
        active: updates['active'],
        location: updates['location'],
        role: updates['role'],
        imageUrl: updates['imageUrl'],
        email: updates['email'],
        phone: updates['phone'],
        department: updates['department'],
        specialization: updates['specialization'],
        qualification: updates['qualification'],
        joinDate: updates['joinDate'],
        shiftType: updates['shiftType'],
        emergencyContact: updates['emergencyContact'],
      );
    }
  }

  Future<bool> deleteStaff(String id) async {
    try {
      final response = _authToken != null
          ? await http.delete(
              Uri.parse('$baseUrl/$id'),
              headers: {
                'Authorization': 'Bearer $_authToken',
              },
            )
          : await HttpClientService.delete('$baseUrl/$id');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        return responseData['success'] == true;
      } else {
        throw Exception('Failed to delete staff: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error deleting staff: $e');
      return false;
    }
  }

  // Mock data for testing
  List<Staff> _getMockStaff() {
    return [
      // Staff(
      //   id: 'S001',
      //   name: 'Dr. Sarah Johnson',
      //   active: true,
      //   location: 'ICU',
      //   role: 'Senior Doctor',
      //   imageUrl: 'https://example.com/sarah.jpg',
      //   email: '<EMAIL>',
      //   phone: '************',
      //   department: 'Medical',
      //   specialization: 'Cardiology',
      //   qualification: 'MD, Cardiology',
      //   joinDate: '2020-05-15',
      //   shiftType: 'Morning',
      //   emergencyContact: '************',
      // ),
      // Staff(
      //   id: 'S002',
      //   name: 'Nurse Emily Davis',
      //   active: false,
      //   location: 'Ward B',
      //   role: 'Head Nurse',
      //   imageUrl: 'https://example.com/emily.jpg',
      //   email: '<EMAIL>',
      //   phone: '************',
      //   department: 'Nursing',
      //   specialization: 'Geriatric Care',
      //   qualification: 'BSN, RN',
      //   joinDate: '2019-08-22',
      //   shiftType: 'Evening',
      //   emergencyContact: '************',
      // ),
      // Staff(
      //   id: 'S003',
      //   name: 'Dr. Michael Smith',
      //   active: true,
      //   location: 'Emergency Room',
      //   role: 'Emergency Physician',
      //   imageUrl: 'https://example.com/michael.jpg',
      //   email: '<EMAIL>',
      //   phone: '************',
      //   department: 'Emergency',
      //   specialization: 'Emergency Medicine',
      //   qualification: 'MD, Emergency Medicine',
      //   joinDate: '2018-11-10',
      //   shiftType: 'Night',
      //   emergencyContact: '************',
      // ),
      // Staff(
      //   id: 'S004',
      //   name: 'Nurse John Williams',
      //   active: true,
      //   location: 'Ward A',
      //   role: 'Staff Nurse',
      //   imageUrl: 'https://example.com/john.jpg',
      //   email: '<EMAIL>',
      //   phone: '************',
      //   department: 'Nursing',
      //   specialization: 'General Care',
      //   qualification: 'BSN',
      //   joinDate: '2021-02-28',
      //   shiftType: 'Rotating',
      //   emergencyContact: '************',
      // ),
    ];
  }
}