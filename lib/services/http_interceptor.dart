import 'package:carerez/config/env_config.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'package:shared_preferences/shared_preferences.dart';

class CustomInterceptor implements InterceptorContract {
  @override
  bool shouldInterceptRequest() => true;

  @override
  bool shouldInterceptResponse() => true;

  @override
  Future<BaseRequest> interceptRequest({required BaseRequest request}) async {
    // Add standard headers
    request.headers["x-host"] = EnvConfig.xHost;
    request.headers["Content-Type"] = "application/json";
    
    // Add auth token if available
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('access-token');
    if (token != null) {
      request.headers["Authorization"] = "Bearer $token";
      request.headers["Cookie"] = "access-token=$token";
    }
    
    return request;
  }

  @override
  Future<BaseResponse> interceptResponse({required BaseResponse response}) async {
    // Check for and save new tokens from response
    if (response.headers.containsKey('set-cookie')) {
      final cookieHeader = response.headers['set-cookie'];
      if (cookieHeader != null) {
        final token = _extractTokenFromCookies(cookieHeader);
        if (token != null) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('access-token', token);
        }
      }
    }
    return response;
  }
  
  // Helper to extract token from cookies
  String? _extractTokenFromCookies(String cookieHeader) {
    final cookieParts = cookieHeader.split(';');
    for (var part in cookieParts) {
      part = part.trim();
      if (part.startsWith('access-token=')) {
        return part.substring('access-token='.length);
      }
    }
    return null;
  }
}
