import 'dart:convert';
import 'package:carerez/config/env_config.dart';
import 'package:carerez/models/meal.dart';
import 'package:carerez/models/medicalTreatment.dart';
import 'package:flutter/material.dart';
import '../models/diet_plan.dart';
import '../models/emergency_contact.dart';
import '../models/resident.dart';
import '../models/user_model.dart';
import 'http_client.dart';

class ResidentService {
  final String apiBaseUrl = EnvConfig.apiBaseUrl;
  final String baseUrl = '${EnvConfig.apiBaseUrl}/resident';

  Future<List<Resident>> getResidents() async {
    try {
      final response = await HttpClientService.get('$baseUrl/getAllResidents');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        debugPrint('Response data: $responseData');

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> residentsData = responseData['data'];
          return residentsData.map((json) => Resident.fromJson(json)).toList();
        } else {
          throw Exception(
              'Failed to parse residents: ${responseData['message']}');
        }
      } else {
        throw Exception(
            'Failed to load residents: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error fetching residents: $e');
      // For now, return mock data if API fails
      return _getMockResidents();
    }
  }

  Future<List<Resident>> getResidentsByUnit(
    String unitId,
  ) async {
    try {
      final response =
          await HttpClientService.get(''
              '${EnvConfig.apiBaseUrl}/unit/resident/$unitId');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        debugPrint('Response data: $responseData');

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> residentsData = responseData['data'];
          return residentsData.map((json) => Resident.fromJson(json)).toList();
        } else {
          throw Exception(
              'Failed to parse residents: ${responseData['message']}');
        }
      } else {
        throw Exception(
            'Failed to load residents: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error fetching residents: $e');
      // For now, return mock data if API fails
      return _getMockResidents();
    }
  }

  Future<Resident> getResidentById(String id) async {
    try {
      final response = await HttpClientService.get('$baseUrl/$id');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);

        if (responseData['success'] == true && responseData['data'] != null) {
          return Resident.fromJson(responseData['data']);
        } else {
          throw Exception(
              'Failed to parse resident: ${responseData['message']}');
        }
      } else {
        throw Exception(
            'Failed to load resident: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error fetching resident: $e');
      throw e;
    }
  }

  // Mock data for testing
  List<Resident> _getMockResidents() {
    return [];
  }

  Future<List<MedicalTreatment>> getResidentMedicalHistory(
      String residentId) async {
    try {
      final response = await HttpClientService.get(
          '$apiBaseUrl/medical-treatment/view_all/$residentId');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        debugPrint('Response data: $responseData');

        if (responseData['success'] == true && responseData['data'] != null)
        {
          final List<dynamic> treatmentsData = responseData['data'];
          return treatmentsData
              .map((json) => MedicalTreatment.fromJson(json))
              .toList();
        } else {
          throw Exception(
              'Failed to parse medical history: ${responseData['message']}');
        }
      } else {
        throw Exception(
            'Failed to load medical history: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error fetching medical history: $e');
      throw e;
    }
  }

  Future<List<EmergencyContact>> getResidentContactInformation(
      String residentId) async {
    try {
      final response = await HttpClientService.get(
          '$apiBaseUrl/users/emcontact/$residentId');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        debugPrint('Response data: $responseData');

        if (responseData['success'] == true && responseData['data'] != null) {
          return List<Map<String, dynamic>>.from(responseData['data'])
              .map((e) => EmergencyContact.fromJson(e))
              .toList();
        } else {
          throw Exception(
              'Failed to parse contact information: ${responseData['message']}');
        }
      } else {
        throw Exception(
            'Failed to load contact information: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error fetching contact information: $e');
      throw e;
    }
  }

  Future<List<FamilyDetail>> getResidentFamilyInformation(
      String residentId) async {
    try {
      final response = await HttpClientService.get(
          '$apiBaseUrl/users/fmdetails/$residentId');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        debugPrint('Response data: $responseData');

        if (responseData['success'] == true && responseData['data'] != null) {
          return List<Map<String, dynamic>>.from(responseData['data'])
              .map((e) => FamilyDetail.fromJson(e))
              .toList();
        } else {
          throw Exception(
              'Failed to parse family information: ${responseData['message']}');
        }
      } else {
        throw Exception(
            'Failed to load family information: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error fetching family information: $e');
      throw e;
    }
  }

  Future<ResidentBudget> getResidentBudget(String residentId) async {
    try {
      final response = await HttpClientService.get(
          '$apiBaseUrl/resident-finance/budget/view/$residentId');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        debugPrint('Response data: $responseData');

        if (responseData['success'] == true && responseData['data'] != null) {
          // Handle possible nulls in the data map
          final data = responseData['data'] ?? {};
          return ResidentBudget.fromJson(data);
        } else {
          throw Exception(
              'Failed to parse budget information: ${responseData['message'] ?? "Unknown error"}');
        }
      } else {
        throw Exception(
            'Failed to load budget information: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error fetching budget information: $e');
      rethrow;
    }
  }
  Future<ServiceProviderResident> getResidentServiceProvider(
      String residentId) async {
    try {
      final response = await HttpClientService.get(
          '$apiBaseUrl/resident/svcprovider/$residentId');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        debugPrint('Response data: $responseData');

        if (responseData['success'] == true && responseData['data'] != null) {
          return ServiceProviderResident.fromJson(responseData['data']);
        } else {
          throw Exception(
              'Failed to parse service provider information: ${responseData['message']}');
        }
      } else {
        throw Exception(
            'Failed to load service provider information: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error fetching service information: $e');
      throw e;
    }
  }

  Future<List<DietPlan>> getResidentDietPlans(String residentId) async {
    try {
      final response = await HttpClientService.get(
          '$apiBaseUrl/diet-plans/$residentId');


      // Accept both 200 and 201 as success
      if (response.statusCode == 200 || response.statusCode == 201) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        debugPrint('Response data: $responseData');

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> mealsData = responseData['data'];
          return mealsData.map((json) => DietPlan.fromJson(json)).toList();
        } else {
          throw Exception(
              'Failed to parse meals: [${responseData['message']}');
        }
      } else {
        throw Exception(
            'Failed to load meals: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error fetching meals: $e');
      throw e;
    }
  }

  Future <List<Meal>> getDietPlanMeals(String dietPlanId) async {
    try {
      final response = await HttpClientService.get(
          '$apiBaseUrl/meals/getAll/$dietPlanId');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        debugPrint('Response data: $responseData');

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> mealsData = responseData['data'];
          return mealsData.map((json) => Meal.fromJson(json)).toList();
        } else {
          throw Exception(
              'Failed to parse meals: ${responseData['message']}');
        }
      } else {
        throw Exception(
            'Failed to load meals: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error fetching meals: $e');
      throw e;
    }
  }


}
