import 'dart:convert';
import 'package:carerez/config/env_config.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../models/request.dart';
import 'http_client.dart';

class RequestService {
  final String? _authToken;
  final String baseUrl;

  RequestService([this._authToken])
      : baseUrl =
            '${EnvConfig.apiBaseUrl}/service-request';

  Future<List<Request>> getRequests(
      {String? residentId, String? assignedTo}) async {
    try {
      Map<String, String> queryParams = {};
      if (residentId != null) queryParams['residentId'] = residentId;
      if (assignedTo != null) queryParams['assignedTo'] = assignedTo;

      final response = _authToken != null
          ? await http.get(
              Uri.parse(baseUrl).replace(queryParameters: queryParams),
              headers: {
                'Authorization': 'Bearer $_authToken',
              },
            )
          : await HttpClientService.get(
              baseUrl,
              params: queryParams,
            );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> requestsData = responseData['data'];
          return requestsData.map((json) => Request.fromJson(json)).toList();
        } else {
          throw Exception(
              'Failed to parse requests: ${responseData['message']}');
        }
      } else {
        throw Exception(
            'Failed to load requests: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error fetching requests: $e');
      // Return mock data if API fails
      return _getMockRequests(residentId, assignedTo);
    }
  }

  Future<Request> getRequestById(String id) async {
    try {
      final response = _authToken != null
          ? await http.get(
              Uri.parse('$baseUrl/$id'),
              headers: {
                'Authorization': 'Bearer $_authToken',
              },
            )
          : await HttpClientService.get('$baseUrl/$id');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);

        if (responseData['success'] == true && responseData['data'] != null) {
          return Request.fromJson(responseData['data']);
        } else {
          throw Exception(
              'Failed to parse request: ${responseData['message']}');
        }
      } else {
        throw Exception(
            'Failed to load request: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error fetching request: $e');
      // Return mock data if API fails
      return _getMockRequests(null, null).firstWhere(
        (request) => request.id == id,
        orElse: () => _getMockRequests(null, null).first,
      );
    }
  }

  Future<Request> createRequest(Request request) async {
    try {
      final response = _authToken != null
          ? await http.post(
              Uri.parse(baseUrl),
              headers: {
                'Authorization': 'Bearer $_authToken',
                'Content-Type': 'application/json',
              },
              body: json.encode(request.toJson()),
            )
          : await HttpClientService.post(
              baseUrl,
              body: json.encode(request.toJson()),
            );

      if (response.statusCode == 201 || response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);

        if (responseData['success'] == true && responseData['data'] != null) {
          return Request.fromJson(responseData['data']);
        } else {
          throw Exception(
              'Failed to create request: ${responseData['message']}');
        }
      } else {
        throw Exception(
            'Failed to create request: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error creating request: $e');
      // Return the request with a generated ID for testing
      return request.copyWith(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        createdAt: DateTime.now(),
      );
    }
  }

  Future<Request> updateRequest(String id, Map<String, dynamic> updates) async {
    try {
      final response = _authToken != null
          ? await http.patch(
              Uri.parse('$baseUrl/$id'),
              headers: {
                'Authorization': 'Bearer $_authToken',
                'Content-Type': 'application/json',
              },
              body: json.encode(updates),
            )
          : await HttpClientService.put(
              '$baseUrl/$id',
              body: json.encode(updates),
            );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);

        if (responseData['success'] == true && responseData['data'] != null) {
          return Request.fromJson(responseData['data']);
        } else {
          throw Exception(
              'Failed to update request: ${responseData['message']}');
        }
      } else {
        throw Exception(
            'Failed to update request: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error updating request: $e');
      // For testing, get the request and apply updates
      final request = await getRequestById(id);
      final updatedRequest = Request.fromJson({
        ...request.toJson(),
        ...updates,
      });
      return updatedRequest;
    }
  }

  Future<void> deleteRequest(String id) async {
    try {
      final response = _authToken != null
          ? await http.delete(
              Uri.parse('$baseUrl/$id'),
              headers: {
                'Authorization': 'Bearer $_authToken',
              },
            )
          : await HttpClientService.delete('$baseUrl/$id');

      if (response.statusCode != 204 && response.statusCode != 200) {
        throw Exception(
            'Failed to delete request: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error deleting request: $e');
      // For testing, just return
      return;
    }
  }

  Future<Request> updateRequestStatus(String id, RequestStatus status,
      {String? completedBy}) async {
    final updates = {
      'status': status.toString().split('.').last,
    };

    if (status == RequestStatus.completed && completedBy != null) {
      updates['completedAt'] = DateTime.now().toIso8601String();
      updates['completedBy'] = completedBy;
    }

    return updateRequest(id, updates);
  }

  Future<Request> assignRequest(String id, String assignedTo) async {
    return updateRequest(id, {'assignedTo': assignedTo});
  }

  Future<Request> addRequestComment(String id, RequestComment comment) async {
    try {
      final request = await getRequestById(id);
      final List<RequestComment> updatedComments = [
        ...request.comments,
        comment
      ];

      return await updateRequest(id, {
        'comments': updatedComments.map((c) => c.toJson()).toList(),
      });
    } catch (e) {
      debugPrint('Error adding comment to request: $e');
      throw Exception('Failed to add comment to request: $e');
    }
  }

  // Mock data for testing
  List<Request> _getMockRequests(String? residentId, String? assignedTo) {
    final List<Request> requests = [
      // Request(
      //   id: '1',
      //   residentId: 'R001',
      //   title: 'Leaking Faucet',
      //   description: 'The bathroom faucet is leaking and needs repair.',
      //   type: RequestType.maintenance,
      //   priority: RequestPriority.medium,
      //   status: RequestStatus.pending,
      //   createdAt: DateTime.now().subtract(Duration(days: 2)),
      //   createdBy: 'John Doe',
      //   comments: [
      //     RequestComment(
      //       id: '1',
      //       content: 'Maintenance team has been notified.',
      //       authorId: 'S001',
      //       authorName: 'Admin User',
      //       createdAt: DateTime.now().subtract(Duration(days: 1)),
      //     ),
      //   ],
      // ),
      // Request(
      //   id: '2',
      //   residentId: 'R001',
      //   title: 'Room Cleaning',
      //   description: 'Need room cleaning service tomorrow morning.',
      //   type: RequestType.housekeeping,
      //   priority: RequestPriority.low,
      //   status: RequestStatus.completed,
      //   createdAt: DateTime.now().subtract(Duration(days: 5)),
      //   createdBy: 'John Doe',
      //   completedAt: DateTime.now().subtract(Duration(days: 4)),
      //   completedBy: 'Jane Smith',
      // ),
      // Request(
      //   id: '3',
      //   residentId: 'R002',
      //   title: 'Special Diet Request',
      //   description: 'Need low sodium meals starting next week.',
      //   type: RequestType.dietary,
      //   priority: RequestPriority.high,
      //   status: RequestStatus.inProgress,
      //   createdAt: DateTime.now().subtract(Duration(days: 3)),
      //   createdBy: 'Alice Johnson',
      //   assignedTo: 'Dietary Department',
      // ),
      // Request(
      //   id: '4',
      //   residentId: 'R003',
      //   title: 'Medical Assistance',
      //   description: 'Need help with medication management.',
      //   type: RequestType.medical,
      //   priority: RequestPriority.urgent,
      //   status: RequestStatus.pending,
      //   createdAt: DateTime.now().subtract(Duration(hours: 12)),
      //   createdBy: 'Bob Williams',
      // ),
    ];

    // Filter by residentId if provided
    List<Request> filteredRequests = requests;
    if (residentId != null) {
      filteredRequests =
          filteredRequests.where((r) => r.residentId == residentId).toList();
    }

    // Filter by assignedTo if provided
    if (assignedTo != null) {
      filteredRequests =
          filteredRequests.where((r) => r.assignedTo == assignedTo).toList();
    }

    return filteredRequests;
  }
}
