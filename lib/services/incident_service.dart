import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;
import '../models/incident.dart';
import '../config/env_config.dart';
import 'http_client.dart';

class IncidentService {
  final String baseUrl = '${EnvConfig.apiBaseUrl}/incident';
  final String? _authToken;

  IncidentService([this._authToken]);

  Future<List<Incident>> getIncidents() async {
    try {
      final response = await HttpClientService.get('$baseUrl/all');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        debugPrint('Response data: $responseData');

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> incidentsData = responseData['data'];
          return incidentsData.map((json) => Incident.fromJson(json)).toList();
        } else {
          throw Exception('Failed to parse incidents: ${responseData['message']}');
        }
      } else {
        throw Exception('Failed to load incidents: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('Error fetching incidents: $e');
      // Fallback to empty list if API fails
      return [];
    }
  }

  Future<Incident> createIncident(Incident incident) async {
    try {
      final response = _authToken != null
          ? await http.post(
              Uri.parse('$baseUrl/create'),
              headers: {
                'Authorization': 'Bearer%20$_authToken',
                'Content-Type': 'application/json',
              },
              body: json.encode(incident.toJson()),
            )
          : await HttpClientService.post(
              '$baseUrl/create',
              body: json.encode(incident.toJson()),
            );

      if (response.statusCode == 201 || response.statusCode == 200) {
        return Incident.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to create incident: ${response.statusCode}');
      }
    } catch (e) {
      // Return the original incident if API fails
      return incident;
    }
  }

  Future<Incident> updateIncident(String id, Map<String, dynamic> updates) async {
    try {
      final response = _authToken != null
          ? await http.patch(
              Uri.parse('$baseUrl/$id'),
              headers: {
                'Authorization': 'Bearer $_authToken',
                'Content-Type': 'application/json',
              },
              body: json.encode(updates),
            )
          : await HttpClientService.put(
              '$baseUrl/$id',
              body: json.encode(updates),
            );

      if (response.statusCode == 200) {
        return Incident.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to update incident: ${response.statusCode}');
      }
    } catch (e) {
      // Return a mock updated incident if API fails
      return Incident(
        id: id,
        name: updates['name'] ?? 'Updated Incident',
        description: updates['description'] ?? 'Description',
        reportedBy: updates['reportedBy'] ?? 'Reporter',
        level: updates['level'] ?? 'Level',
        status: updates['status'] ?? 'Status',
        dateTime: DateTime.now(),
      );
    }
  }

  Future<void> deleteIncident(String id) async {
    try {
      final response = _authToken != null
          ? await http.delete(
              Uri.parse('$baseUrl/$id'),
              headers: {
                'Authorization': 'Bearer $_authToken',
                'Content-Type': 'application/json',
              },
            )
          : await HttpClientService.delete('$baseUrl/$id');

      if (response.statusCode != 204 && response.statusCode != 200) {
        throw Exception('Failed to delete incident: ${response.statusCode}');
      }
    } catch (e) {
      // Silently fail - the UI will still remove the incident
    }
  }

  Future<Incident> addComment(String id, String comment) async {
    try {
      final commentData = {
        'comment': comment,
        'timestamp': DateTime.now().toIso8601String(),
      };

      final response = _authToken != null
          ? await http.post(
              Uri.parse('$baseUrl/$id/comments'),
              headers: {
                'Authorization': 'Bearer $_authToken',
                'Content-Type': 'application/json',
              },
              body: json.encode(commentData),
            )
          : await HttpClientService.post(
              '$baseUrl/$id/comments',
              body: json.encode(commentData),
            );

      if (response.statusCode == 200) {
        return Incident.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to add comment: ${response.statusCode}');
      }
    } catch (e) {
      // Return a mock updated incident if API fails
      return Incident(
        id: id,
        name: 'Updated Incident',
        description: 'Description with new comment: $comment',
        reportedBy: 'Reporter',
        level: 'Level',
        status: 'Status',
        dateTime: DateTime.now(),
        timeline: [
          IncidentEvent(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            title: 'Comment Added',
            description: comment,
            timestamp: DateTime.now(),
            type: 'comment',
          )
        ],
      );
    }
  }

  // Fallback mock data in case API fails
  // List<Incident> _getMockIncidents() {
  //   return [
  //     Incident(
  //       id: '1',
  //       name: 'Server Downtime Critical',
  //       description: 'Main production server is not responding',
  //       reportedBy: 'Samuel Johnson',
  //       level: 'Emergency',
  //       status: 'In Progress',
  //       dateTime: DateTime.now(),
  //     ),
  //     Incident(
  //       id: '2',
  //       name: 'Network Connectivity Issue',
  //       description: 'Intermittent connection in east wing',
  //       reportedBy: 'John Smith',
  //       level: 'Neutral',
  //       status: 'Resolved',
  //       dateTime: DateTime.now().subtract(Duration(days: 1)),
  //     ),
  //     Incident(
  //       id: '3',
  //       name: 'Equipment Malfunction',
  //       description: 'Printer in room 203 is not printing',
  //       reportedBy: 'Jane Doe',
  //       level: 'Minor',
  //       status: 'Open',
  //       dateTime: DateTime.now().subtract(Duration(days: 2)),
  //     ),
  //     Incident(
  //       id: '4',
  //       name: 'Software Bug',
  //       description: 'Application is crashing on login',
  //       reportedBy: 'Mike Brown',
  //       level: 'Minor',
  //       status: 'Open',
  //       dateTime: DateTime.now().subtract(Duration(days: 3)),
  //     ),
  //     Incident(
  //       id: '5',
  //       name: 'Security Breach',
  //       description: 'Unauthorized access detected',
  //       reportedBy: 'Emily White',
  //       level: 'Critical',
  //       status: 'In Progress',
  //       dateTime: DateTime.now().subtract(Duration(days: 4)),
  //     ),
  //   ];
  // }
}
