import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../config/env_config.dart';
import '../models/activity.dart';
import '../screens/utils/snackbar_utils.dart';
import 'http_client.dart';


class ActivityService {
  final String? _authToken;
  final String baseUrl;

  ActivityService([this._authToken])
      : baseUrl =
            '${EnvConfig.apiBaseUrl}/activity-log/create';

  Future<List<Activity>> getActivities({
    String? residentId,
    String? staffId,
    String? category,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      Map<String, String> queryParams = {};
      if (residentId != null) queryParams['residentId'] = residentId;
      if (staffId != null) queryParams['staffId'] = staffId;
      if (category != null) queryParams['category'] = category;
      if (startDate != null) queryParams['startDate'] = startDate.toIso8601String();
      if (endDate != null) queryParams['endDate'] = endDate.toIso8601String();

      final uri = Uri.parse(baseUrl).replace(queryParameters: queryParams);
      
      final response = _authToken != null
          ? await HttpClientService.get(uri.toString())
          : await HttpClientService.get(uri.toString());

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => Activity.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load activities: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching activities: $e');
      // Return mock data for development
      return _getMockActivities(residentId);
    }
  }

  Future<Activity> createActivity(Activity activity) async {
    try {
      final response = _authToken != null
          ? await HttpClientService.post(
              baseUrl,
              body: json.encode(activity.toJson()),
            )
          : await HttpClientService.post(
              baseUrl,
              body: json.encode(activity.toJson()),
            );

      if (response.statusCode == 201) {
        return Activity.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to create activity: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error creating activity: $e');
      // Return mock data for development
      return activity.copyWith(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
      );
    }
  }

  Future<Activity> updateActivity(String activityId, Map<String, dynamic> updates) async {
    try {
      final response = _authToken != null
          ? await HttpClientService.put(
              '$baseUrl/$activityId',
              body: json.encode(updates),
            )
          : await HttpClientService.put(
              '$baseUrl/$activityId',
              body: json.encode(updates),
            );

      if (response.statusCode == 200) {
        return Activity.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to update activity: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error updating activity: $e');
      // Return mock data for development
      final mockActivity = _getMockActivities(null).first;
      return mockActivity.copyWith(
        id: activityId,
        notes: updates['notes'],
        response: updates['response'] != null 
            ? ActivityResponse.values.firstWhere(
                (e) => e.toString().split('.').last == updates['response'],
                orElse: () => ActivityResponse.neutral,
              )
            : null,
      );
    }
  }

  Future<void> deleteActivity(String activityId) async {
    try {
      final response = _authToken != null
          ? await HttpClientService.delete(
              '$baseUrl/$activityId',
            )
          : await HttpClientService.delete(
              '$baseUrl/$activityId',
            );

      if (response.statusCode != 204) {
        throw Exception('Failed to delete activity: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error deleting activity: $e');
      // For development, just log the error
    }
  }

  Future<void> sendResidentCareActivity(ResidentCareActivity activity) async {
    try {
      final response = _authToken != null
          ? await HttpClientService.post(
              baseUrl,
              body: json.encode(activity.toJson()),
            )
          : await HttpClientService.post(
              baseUrl,
              body: json.encode(activity.toJson()),
            );
      if (response.statusCode == 401 || (response.body.contains('Access denied: insufficient permissions'))) {
        throw AccessDeniedException();
      }
      if (response.statusCode != 201 && response.statusCode != 200) {
        throw Exception('Failed to send resident care activity:${response.body}');
      }
    } catch (e) {
      debugPrint('Error sending resident care activity: $e');
      rethrow;
    }
  }

  // Mock data for development
  List<Activity> _getMockActivities(String? residentId) {
    final now = DateTime.now();
    
    final List<Activity> activities = [
    ];
    
    if (residentId != null) {
      return activities.where((a) => a.residentId == residentId).toList();
    }
    
    return activities;
  }
}