import 'dart:convert';

import 'package:carerez/config/env_config.dart';
import 'package:carerez/models/assign_task.dart';
import 'package:carerez/services/http_client.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/calendar_task.dart';

class CalendarService {

  final String baseUrl = EnvConfig.apiBaseUrl;

   Future<List<AssignTask>> getCalendarTasks(String residentId) async {
    try {

      final response = await HttpClientService.get('$baseUrl/assign-task/$residentId');
      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> tasksData = responseData['data'];
          return (tasksData)
              .map((item) => AssignTask.fromJson(item as Map<String, dynamic>))
              .toList();
        } else {
          throw Exception('Failed to load tasks: ${responseData['message']}');
        }
      } else {
        throw Exception(
            'Failed to load tasks: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('Error loading tasks: $e');
      return [];
    }
  }

  // Add a new calendar task
  static Future<void> addCalendarTask(AssignTask task) async {
    // TODO: Implement actual add logic
    throw UnimplementedError();
  }

  // Update an existing calendar task
  static Future<void> updateCalendarTask(AssignTask task) async {
    // TODO: Implement actual update logic
    throw UnimplementedError();
  }

  // Delete a calendar task
  static Future<void> deleteCalendarTask(String taskId) async {
    // TODO: Implement actual delete logic
    throw UnimplementedError();
  }

  // Toggle completion status of a calendar task
  static Future<void> toggleTaskCompletion(String taskId, bool isCompleted) async {
    // TODO: Implement actual toggle logic
    throw UnimplementedError();
  }
}
