import 'dart:convert';
import 'package:carerez/config/env_config.dart';
import 'package:carerez/models/assigned_medicine.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'http_client.dart';
import '../models/medication_intake.dart';
import '../models/medication_feedback.dart';
import '../models/doctor_note.dart';

class MedicationService {
  final String? _authToken;
  final String baseUrl;

  MedicationService([this._authToken])
      : baseUrl = '${EnvConfig.apiBaseUrl}';

  // Get all medications for a resident
  Future<List<ResidentAssignedMedicine>> getMedications({String? residentId}) async {
    try {
      String url = baseUrl;
      if (residentId != null) {
        url = '$baseUrl/assign-medicine/view_all/$residentId';
      }

      final response = _authToken != null
          ? await HttpClientService.get(url)
          : await HttpClientService.get(url);

      if (response.statusCode == 200) {
        // The API returns a JSON object with a 'data' field containing the list
        final Map<String, dynamic> decoded = json.decode(response.body);
        final List<dynamic> data = decoded['data'] ?? [];
        return data.map((json) => ResidentAssignedMedicine.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load medications: \\${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching medications: $e');
      // Return mock data for development
      return [];
    }
  }

  // Get a specific medication by ID
  Future<ResidentAssignedMedicine> getMedicationById(String medicationId) async {
    try {
      final response = _authToken != null
          ? await HttpClientService.get('$baseUrl/view_all/$medicationId')
          : await HttpClientService.get('$baseUrl/view_all/$medicationId');

      if (response.statusCode == 200) {
        final Map<String, dynamic> decoded = json.decode(response.body);
        final data = decoded['data'];
        return ResidentAssignedMedicine.fromJson(data);
      } else {
        throw Exception('Failed to load medication: \\${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching medication: $e');
      // Return mock data for development
      return ResidentAssignedMedicine(
        medicalTreatmentId: '',
        assignMedicineId: '',
        isSequential: false,
        startDate: DateTime.now(),
        endDate: DateTime.now(),
        notes: null,
        isActive: false,
        medicine: Medicine.fromJson({}),
        medicineAttachments: [],
        selectTimes: [],
        customDates: [],
        medTreatmentAssignMed: MedTreatmentAssignMed.fromJson({}),
      );
    }
  }

  // Medication Intake Methods
  Future<List<MedicationIntake>> getMedicationIntakes({
    String? residentId,
    String? medicationId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      Map<String, String> queryParams = {};
      if (residentId != null) queryParams['residentId'] = residentId;
      if (medicationId != null) queryParams['medicationId'] = medicationId;
      if (startDate != null) queryParams['startDate'] = startDate.toIso8601String();
      if (endDate != null) queryParams['endDate'] = endDate.toIso8601String();
      final uri = Uri.parse('$baseUrl/intakes').replace(queryParameters: queryParams);
      final response = _authToken != null
          ? await HttpClientService.get(uri.toString())
          : await HttpClientService.get(uri.toString());
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => MedicationIntake.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load medication intakes: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching medication intakes: $e');
      return [];
    }
  }

  Future<MedicationIntake> recordMedicationIntake(MedicationIntake intake) async {
    try {
      final response = _authToken != null
          ? await HttpClientService.post('$baseUrl/intakes', body: json.encode(intake.toJson()))
          : await HttpClientService.post('$baseUrl/intakes', body: json.encode(intake.toJson()));
      if (response.statusCode == 201) {
        return MedicationIntake.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to record medication intake: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error recording medication intake: $e');
      return intake.copyWith(id: DateTime.now().millisecondsSinceEpoch.toString(), actualTime: DateTime.now());
    }
  }

  // Medication Feedback Methods
  Future<List<MedicationFeedback>> getMedicationFeedback({
    String? residentId,
    String? medicationId,
  }) async {
    try {
      Map<String, String> queryParams = {};
      if (residentId != null) queryParams['residentId'] = residentId;
      if (medicationId != null) queryParams['medicationId'] = medicationId;
      final uri = Uri.parse('$baseUrl/feedback').replace(queryParameters: queryParams);
      final response = _authToken != null
          ? await HttpClientService.get(uri.toString())
          : await HttpClientService.get(uri.toString());
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => MedicationFeedback.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load medication feedback: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching medication feedback: $e');
      return [];
    }
  }

  Future<MedicationFeedback> addMedicationFeedback(MedicationFeedback feedback) async {
    try {
      final response = _authToken != null
          ? await HttpClientService.post('$baseUrl/feedback', body: json.encode(feedback.toJson()))
          : await HttpClientService.post('$baseUrl/feedback', body: json.encode(feedback.toJson()));
      if (response.statusCode == 201) {
        return MedicationFeedback.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to add medication feedback: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error adding medication feedback: $e');
      return feedback.copyWith(id: DateTime.now().millisecondsSinceEpoch.toString());
    }
  }

  // Doctor Notes Methods
  Future<List<DoctorNote>> getDoctorNotes({String? residentId}) async {
    try {
      String url = '$baseUrl/doctor-note/view_all/${residentId ?? ''}';

      final response = _authToken != null
          ? await HttpClientService.get(url)
          : await HttpClientService.get(url);
      if (response.statusCode == 200) {
        final Map<String, dynamic> decoded = json.decode(response.body);
        final List<dynamic> data = decoded['data'] ?? [];
        return data.map((json) => DoctorNote.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load doctor notes: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching doctor notes: $e');
      return [];
    }
  }

  Future<DoctorNote> addDoctorNote(DoctorNote note) async {
    try {
      final response = _authToken != null
          ? await HttpClientService.post('$baseUrl/doctor-notes', body: json.encode(note.toJson()))
          : await HttpClientService.post('$baseUrl/doctor-notes', body: json.encode(note.toJson()));
      if (response.statusCode == 200 || response.statusCode == 201) {
        return DoctorNote.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to add doctor note: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error adding doctor note: $e');
      return note.copyWith(doctorNoteId: DateTime.now().millisecondsSinceEpoch.toString());
    }
  }

  // Create a new assigned medicine
  Future<ResidentAssignedMedicine> createAssignedMedicine(ResidentAssignedMedicine assignedMedicine) async {
    try {
      final response = _authToken != null
          ? await HttpClientService.post(
              baseUrl,
              body: json.encode(assignedMedicine),
            )
          : await HttpClientService.post(
              baseUrl,
              body: json.encode(assignedMedicine),
            );
      if (response.statusCode == 201) {
        final Map<String, dynamic> decoded = json.decode(response.body);
        final data = decoded['data'];
        return ResidentAssignedMedicine.fromJson(data);
      } else {
        throw Exception('Failed to create assigned medicine: \\${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error creating assigned medicine: $e');
      // Return empty model for development
      return ResidentAssignedMedicine(
        medicalTreatmentId: '',
        assignMedicineId: '',
        isSequential: false,
        startDate: DateTime.now(),
        endDate: DateTime.now(),
        notes: null,
        isActive: false,
        medicine: Medicine.fromJson({}),
        medicineAttachments: [],
        selectTimes: [],
        customDates: [],
        medTreatmentAssignMed: MedTreatmentAssignMed.fromJson({}),
      );
    }
  }

  // Update an existing assigned medicine
  Future<ResidentAssignedMedicine> updateAssignedMedicine(ResidentAssignedMedicine assignedMedicine) async {
    try {
      final response = _authToken != null
          ? await HttpClientService.put(
              '$baseUrl/${assignedMedicine.assignMedicineId}',
              body: json.encode(assignedMedicine),
            )
          : await HttpClientService.put(
              '$baseUrl/${assignedMedicine.assignMedicineId}',
              body: json.encode(assignedMedicine),
            );
      if (response.statusCode == 200) {
        final Map<String, dynamic> decoded = json.decode(response.body);
        final data = decoded['data'];
        return ResidentAssignedMedicine.fromJson(data);
      } else {
        throw Exception('Failed to update assigned medicine: \\${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error updating assigned medicine: $e');
      return assignedMedicine;
    }
  }

  // Delete an assigned medicine
  Future<void> deleteAssignedMedicine(String assignMedicineId) async {
    try {
      final response = _authToken != null
          ? await HttpClientService.delete('$baseUrl/$assignMedicineId')
          : await HttpClientService.delete('$baseUrl/$assignMedicineId');
      if (response.statusCode != 204) {
        throw Exception('Failed to delete assigned medicine: \\${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error deleting assigned medicine: $e');
    }
  }

  Future<List<MedicationIntake>> getMedicationHistory({String? residentId}) async {
    try {
      String url = '$baseUrl/doctor-note/view_all/${residentId ?? ''}';

      final response = _authToken != null
          ? await HttpClientService.get(url)
          : await HttpClientService.get(url);
      if (response.statusCode == 200) {
        final Map<String, dynamic> decoded = json.decode(response.body);
        final List<dynamic> data = decoded['data'] ?? [];
        return data.map((json) => MedicationIntake.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load doctor notes: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching doctor notes: $e');
      return [];
    }
  }
}
