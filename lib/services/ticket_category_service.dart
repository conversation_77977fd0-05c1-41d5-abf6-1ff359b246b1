import 'dart:convert';
import 'package:carerez/config/env_config.dart';
import 'package:carerez/services/http_client.dart';
import 'package:http/http.dart' as http;
import '../models/ticket_category.dart';

class TicketCategoryService {
  String _endpoint = '${EnvConfig.apiBaseUrl}/service-request-category/view_all';

  Future<List<TicketCategory>> fetchCategories() async {
    final response = await HttpClientService.get(_endpoint);
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final List<dynamic> categories = data['data'] ?? [];
      return categories.map((e) => TicketCategory.fromJson(e)).toList();
    } else {
      throw Exception('Failed to load categories');
    }
  }
}

