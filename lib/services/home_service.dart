import 'dart:convert';
import 'package:carerez/config/env_config.dart';
import 'package:carerez/services/http_client.dart';
import '../models/assign_task.dart';

class HomeService {
   String _endpoint = '${EnvConfig.apiBaseUrl}/home/<USER>';

  Future<List<Home>> getHomes() async {
    final response = await HttpClientService.get(_endpoint);
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final List<dynamic> homes = data['data'] ?? [];
      return homes.map((e) => Home.fromJson(e)).toList();
    } else {
      throw Exception('Failed to load homes');
    }
  }
}

