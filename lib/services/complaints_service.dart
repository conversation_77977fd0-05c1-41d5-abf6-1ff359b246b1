import 'dart:convert';
import 'package:carerez/config/env_config.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../models/complaints.dart';
import 'http_client.dart';

class ComplaintsService {
  final String? _authToken;
  final String baseUrl;

  ComplaintsService([this._authToken])
      : baseUrl = '${EnvConfig.apiBaseUrl}/complaint';

  Future<List<Complaints>> getComplaints() async {
    try {
      print('Fetching complaints from API...');
      final response = _authToken != null
          ? await http.get(
              Uri.parse('$baseUrl/all'),
              headers: {
                'Authorization': 'Bearer $_authToken',
              },
            )
          : await HttpClientService.get('$baseUrl/all');

      print('API Response status: ${response.statusCode}');
      print('API Response body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> complaintsData = responseData['data'];
          final complaints =
              complaintsData.map((json) => Complaints.fromJson(json)).toList();

          print('Parsed ${complaints.length} complaints');
          return complaints;
        } else {
          print('API returned success=false or no data');
          throw Exception(
              'Failed to parse complaints: ${responseData['message']}');
        }
      } else {
        print('API returned error status code: ${response.statusCode}');
        throw Exception(
            'Failed to load complaints: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('Error fetching complaints: $e');
      // Return mock data if API fails
      final mockComplaints = _getMockComplaints();
      print('Returning ${mockComplaints.length} mock complaints');
      return mockComplaints;
    }
  }

  Future<Complaints> getComplaintById(String id) async {
    try {
      final response = _authToken != null
          ? await http.get(
              Uri.parse('$baseUrl/get-complaint/$id'),
              headers: {
                'Authorization': 'Bearer $_authToken',
              },
            )
          : await HttpClientService.get('$baseUrl/$id');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);

        if (responseData['success'] == true && responseData['data'] != null) {
          return Complaints.fromJson(responseData['data']);
        } else {
          throw Exception(
              'Failed to parse complaint: ${responseData['message']}');
        }
      } else {
        throw Exception(
            'Failed to load complaint: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error fetching complaint: $e');
      // Return mock data if API fails
      return _getMockComplaints().firstWhere(
        (complaint) => complaint.complaintId == id,
        orElse: () => _getMockComplaints().first,
      );
    }
  }

  Future<Complaints> createComplaint(Complaints complaint) async {
    try {
      final response = _authToken != null
          ? await http.post(
              Uri.parse('$baseUrl/create'),
              headers: {
                'Authorization': 'Bearer $_authToken',
                'Content-Type': 'application/json',
              },
              body: json.encode(complaint.toJson()),
            )
          : await HttpClientService.post(
              '$baseUrl/create',
              body: json.encode(complaint.toJson()),
            );

      if (response.statusCode == 201 || response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);

        if (responseData['success'] == true && responseData['data'] != null) {
          debugPrint('Complaint created successfully: ${responseData['data']}');
          return Complaints.fromJson(responseData['data']);
        } else {
          debugPrint('Failed to create complaint: ${responseData['message']}');
          throw Exception(
              'Failed to create complaint: ${responseData['message']}');
        }
      } else {
        throw Exception(
            'Failed to create complaint: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error creating complaint: $e');
      // Return the original complaint if API fails
      return complaint;
    }
  }

  Future<Complaints> updateComplaint(
      String id, Map<String, dynamic> updates) async {
    try {
      final response = _authToken != null
          ? await http.put(
              Uri.parse('$baseUrl/update/$id'),
              headers: {
                'Authorization': 'Bearer $_authToken',
                'Content-Type': 'application/json',
              },
              body: json.encode(updates),
            )
          : await HttpClientService.put(
              '$baseUrl/$id',
              body: json.encode(updates),
            );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);

        if (responseData['success'] == true && responseData['data'] != null) {
          return Complaints.fromJson(responseData['data']);
        } else {
          throw Exception(
              'Failed to update complaint: ${responseData['message']}');
        }
      } else {
        throw Exception(
            'Failed to update complaint: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error updating complaint: $e');
      // Get the current complaint and apply updates
      final complaint = await getComplaintById(id);
      final Map<String, dynamic> complaintJson = complaint.toJson();
      complaintJson.addAll(updates);
      return Complaints.fromJson(complaintJson);
    }
  }

  Future<bool> deleteComplaint(String id) async {
    try {
      final response = _authToken != null
          ? await http.delete(
              Uri.parse('$baseUrl/$id'),
              headers: {
                'Authorization': 'Bearer $_authToken',
              },
            )
          : await HttpClientService.delete('$baseUrl/$id');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        return responseData['success'] == true;
      } else {
        throw Exception(
            'Failed to delete complaint: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error deleting complaint: $e');
      return false;
    }
  }

  Future<Complaints> addEvent(String id, ComplaintTimelineEvent event) async {
    try {
      final complaint = await getComplaintById(id);
      final List<ComplaintTimelineEvent> updatedTimeline = [
        ...complaint.complaintTimelines,
        event
      ];

      return await updateComplaint(id, {
        'complaintTimelines': updatedTimeline.map((e) => e.toJson()).toList()
      });
    } catch (e) {
      debugPrint('Error adding event to complaint: $e');
      throw Exception('Failed to add event to complaint: $e');
    }
  }

  Future<Complaints> addAttachment(
      String id, ComplaintAttachment attachment) async {
    try {
      final complaint = await getComplaintById(id);
      final List<ComplaintAttachment> updatedAttachments = [
        ...complaint.complaintAttachments,
        attachment
      ];

      return await updateComplaint(id, {
        'complaintAttachments':
            updatedAttachments.map((a) => a.toJson()).toList()
      });
    } catch (e) {
      debugPrint('Error adding attachment to complaint: $e');
      throw Exception('Failed to add attachment to complaint: $e');
    }
  }

  // Mock data for testing
  List<Complaints> _getMockComplaints() {
    return [

    ];
  }
}
