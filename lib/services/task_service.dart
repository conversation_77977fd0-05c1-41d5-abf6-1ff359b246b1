import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/calendar_task.dart';

class TaskService {
  static const String _storageKey = 'home_tasks';

  static Future<List<CalendarTask>> getTasks() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? tasksJson = prefs.getString(_storageKey);
      
      if (tasksJson == null || tasksJson.isEmpty) {
        return [];
      }

      final List<dynamic> decodedList = json.decode(tasksJson);
      return decodedList
          .map((item) => CalendarTask.fromJson(item as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('Error loading tasks: $e');
      return [];
    }
  }

  static Future<void> saveTasks(List<CalendarTask> tasks) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String tasksJson = json.encode(tasks.map((t) => t.toJson()).toList());
      await prefs.setString(_storageKey, tasksJson);
    } catch (e) {
      print('Error saving tasks: $e');
    }
  }

  static Future<void> addTask(CalendarTask task) async {
    try {
      final tasks = await getTasks();
      tasks.add(task);
      await saveTasks(tasks);
    } catch (e) {
      print('Error adding task: $e');
    }
  }

  static Future<void> updateTask(CalendarTask updatedTask) async {
    try {
      final tasks = await getTasks();
      final index = tasks.indexWhere((task) => task.id == updatedTask.id);
      if (index != -1) {
        tasks[index] = updatedTask;
        await saveTasks(tasks);
      }
    } catch (e) {
      print('Error updating task: $e');
    }
  }

  static Future<void> deleteTask(String taskId) async {
    try {
      final tasks = await getTasks();
      tasks.removeWhere((task) => task.id == taskId);
      await saveTasks(tasks);
    } catch (e) {
      print('Error deleting task: $e');
    }
  }
}
