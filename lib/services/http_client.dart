import 'package:flutter/cupertino.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'package:http/http.dart' as http;
import '../config/env_config.dart';
import 'http_interceptor.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HttpClientService {
  static final client = InterceptedClient.build(
    interceptors: [CustomInterceptor()],
  );

  // Get auth token from SharedPreferences
  static Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('access-token');
  }

  // Get headers with auth token
  static Future<Map<String, String>> _getAuthHeaders() async {
    final token = await _getToken();
    final headers = {
      'Content-Type': 'application/json',
    };

    if (token != null) {
      headers['Authorization'] = token;
      headers['Cookie'] = 'access-token=Bearer%20$token';
    }

    return headers;
  }

  static Future<http.Response> get(String url,
      {Map<String, String>? params}) async {
    final headers = await _getAuthHeaders();
    final uri = params != null
        ? Uri.parse(url).replace(queryParameters: params)
        : Uri.parse(url);
    return await client.get(uri, headers: headers);
  }

  static Future<http.Response> post(String url, {dynamic body}) async {
    final headers = await _getAuthHeaders();
    return await client.post(
      Uri.parse(url),
      headers: headers,
      body: body,
    );
  }

  static Future<http.Response> put(String url, {dynamic body}) async {
    final headers = await _getAuthHeaders();
    return await client.put(
      Uri.parse(url),
      headers: headers,
      body: body,
    );
  }

  static Future<http.Response> delete(String url) async {
    final headers = await _getAuthHeaders();
    return await client.delete(Uri.parse(url), headers: headers);
  }
}
