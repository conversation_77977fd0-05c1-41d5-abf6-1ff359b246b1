import 'dart:convert';
import 'package:carerez/config/env_config.dart';
import 'package:carerez/services/http_client.dart';
import 'package:http/http.dart' as http;
import '../../models/mar_sheet_model.dart';

class MarSheetService {

  final String baseUrl = '${EnvConfig.apiBaseUrl}/mar-sheet';
  Future<List<Medication>> fetchMarSheets({
    required String residentUUID,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final String url =
        '$baseUrl/view_all/$residentUUID?startDate=${startDate.toIso8601String().split('T')[0]}&endDate=${endDate.toIso8601String().split('T')[0]}';
    final response = await HttpClientService.get(url);
    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] is List) {
        return (data['data'] as List)
            .map((e) => Medication.fromJson(e))
            .toList();
      } else {
        throw Exception('Invalid response format');
      }
    } else {
      throw Exception('Failed to fetch MAR Sheets');
    }
  }

  // Add a MAR Sheet entry to the backend
  Future<void> addMarSheetEntry(Map<String, dynamic> entry, String resuidentUUID) async {
    try {
      final String url = '$baseUrl/create/$resuidentUUID';
      final response = await HttpClientService.post(
        url,
        body: json.encode(entry),
      );
      if (response.statusCode != 200 && response.statusCode != 201) {
        throw Exception('Failed to add MAR Sheet entry: \\n${response.body}');
      }
    } catch (e) {
      print('Error in addMarSheetEntry: $e');
      throw Exception('Failed to add MAR Sheet entry: $e');
    }
  }

  // Simulate updating a MAR Sheet entry
  Future<void> updateMarSheetEntry(MarSheet entry) async {
    await Future.delayed(const Duration(milliseconds: 500));
  }
}
