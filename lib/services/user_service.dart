import 'dart:convert';
import 'package:carerez/config/env_config.dart';
import 'package:carerez/models/user_model.dart';
import 'package:carerez/services/http_client.dart';

import '../models/staff_task.dart';

class UserService {
  final String _baseUrl = EnvConfig.apiBaseUrl;

  Future<UserModel> getCurrentUser() async {
    try {
      final response =
          await HttpClientService.get('$_baseUrl/users/dynamicUserProfile');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return UserModel.fromJson(data['data']);
        } else {
          throw Exception(data['message'] ?? 'Failed to fetch user data');
        }
      } else {
        final data = jsonDecode(response.body);
        throw Exception(data['message'] ?? 'Failed to fetch user data');
      }
    } catch (e) {
      throw Exception('Error fetching user data: ${e.toString()}');
    }
  }

  Future<List<StaffTask>> getStaffTasks(String staffId) async {
    try {
      final response = await HttpClientService.get('$_baseUrl/assign-task/$staffId');
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          final result = data['data'];
          if (result is List) {
            return (result)
                .map((item) => StaffTask.fromJson(item as Map<String, dynamic>))
                .toList();
          } else if (result is Map) {
            // If API returns a single object
            return [StaffTask.fromJson(result as Map<String, dynamic>)];
          } else {
            return [];
          }
        } else {
          throw Exception(data['message'] ?? 'Failed to fetch staff tasks');
        }
      } else {
        final data = jsonDecode(response.body);
        throw Exception(data['message'] ?? 'Failed to fetch staff tasks');
      }
    } catch (e) {
      throw Exception('Error fetching staff tasks: ${e.toString()}');
    }
  }


}
