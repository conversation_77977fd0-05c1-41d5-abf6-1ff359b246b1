import 'dart:convert';
import 'package:carerez/services/http_client.dart';
import 'package:http/http.dart' as http;
import '../models/ticket.dart';
import '../config/env_config.dart';
import '../screens/utils/snackbar_utils.dart';

class TicketService {
  final String _baseUrl = EnvConfig.apiBaseUrl;
  final String _authToken;

  TicketService(this._authToken);

  Future<List<TicketModel>> getTickets() async {
    try {
      final response = await HttpClientService.get(
        '$_baseUrl/service-request/all',
      );
      if (response.statusCode == 200) {
        final Map<String, dynamic> decoded = json.decode(response.body);
        final List<dynamic> data = decoded['data'] ?? [];
        return data.map((json) => TicketModel.fromJson(json)).toList();
      }
      else if(response.statusCode ==401) {
        throw AccessDeniedException('Unauthorized access. Please log in again.');
      }
      else {
        throw Exception('Failed to load tickets');
      }
    } catch (e) {
      if (e is AccessDeniedException) rethrow;
      return [];
    }
  }

  Future<TicketModel> createTicket(TicketModel ticket,String unit,String home) async {
    try {
      final Map<String, dynamic> body = {
        'serviceRequestName': ticket.title,
        'homeId': home,
        'addedBy': ticket.addedBy,
        'residentId': ticket.residentId,
        'unitId': unit, // Set this from your UI or ticket if available
        'status': ticket.status == TicketStatus.open ? 'new' : ticket.status.toString().split('.').last,
        'dateTime': ticket.date,
        'description': ticket.description,
        'location': ticket.location,
        'note': ticket.notes, // Add note if available in your UI
        'serviceRequestCategoryId': ticket.category, // Set this from your UI or ticket if available
        'attachments': (ticket.attachments ?? []).map((url) => {
          'attachmentURL': url,
          'attachmentName': url.split('/').last
        }).toList(),
      };
      final response = await HttpClientService.post(
        '$_baseUrl/service-request/create',
        body: json.encode(body),
      );

      if (response.statusCode == 201) {
        return TicketModel.fromJson(json.decode(response.body));
      } else if (response.statusCode == 401) {
        throw AccessDeniedException('Unauthorized access. Please log in again.');
      } else {
        throw Exception('Failed to create ticket');
      }
    } catch (e) {
      if (e is AccessDeniedException) rethrow;
      throw Exception('Failed to connect to server');
    }
  }

  Future<TicketModel> updateTicket(String id, Map<String, dynamic> updates) async {
    try {
      final response = await http.patch(
        Uri.parse('$_baseUrl/$id'),
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
        body: json.encode(updates),
      );

      if (response.statusCode == 200) {
        return TicketModel.fromJson(json.decode(response.body));
      } else if (response.statusCode == 401) {
        throw AccessDeniedException('Unauthorized access. Please log in again.');
      } else {
        throw Exception('Failed to update ticket');
      }
    } catch (e) {
      if (e is AccessDeniedException) rethrow;
      throw Exception('Failed to connect to server');
    }
  }

  Future<void> deleteTicket(String id) async {
    try {
      final response = await http.delete(
        Uri.parse('$_baseUrl/$id'),
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
      );
      if (response.statusCode == 200) {
        return;
      } else if (response.statusCode == 401) {
        throw AccessDeniedException('Unauthorized access. Please log in again.');
      } else {
        throw Exception('Failed to delete ticket');
      }
    } catch (e) {
      if (e is AccessDeniedException) rethrow;
      throw Exception('Failed to connect to server');
    }
  }

  Future<TicketModel> addComment(String id, TimelineEntry entry) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/$id/comments'),
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
        body: json.encode(entry.toJson()),
      );

      if (response.statusCode == 200) {
        return TicketModel.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to add comment');
      }
    } catch (e) {
      throw Exception('Failed to connect to server');
    }
  }
}