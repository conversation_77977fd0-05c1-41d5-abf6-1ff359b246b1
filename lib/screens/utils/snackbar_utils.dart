import 'package:flutter/material.dart';



void showAccessDeniedSnackBar(BuildContext context) {
  ScaffoldMessenger.of(context).showSnackBar(
    const SnackBar(
      content: Text('Access denied: insufficient permissions. Please contact your administrator.'),
      backgroundColor: Colors.red,
    ),
  );
}

class AccessDeniedException implements Exception {
  final String message;
  AccessDeniedException([this.message = 'Access denied: insufficient permissions']);
  @override
  String toString() => message;
}
