import 'dart:convert';

import 'package:carerez/blocs/ticket/ticket_event.dart';
import 'package:carerez/blocs/user/user_bloc.dart';
import 'package:carerez/models/assign_task.dart';
import 'package:carerez/models/ticket.dart';
import 'package:carerez/models/unit.dart';
import 'package:carerez/services/unit_service.dart';
import 'package:carerez/utils/screen.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:iconify_flutter/icons/wi.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/resident/resident_bloc.dart';
import '../../blocs/resident/resident_event.dart';
import '../../blocs/resident/resident_state.dart';
import '../../blocs/ticket/ticket_bloc.dart';
import '../../blocs/ticket/ticket_state.dart';

import '../../blocs/user/user_event.dart';
import '../../blocs/user/user_state.dart';
import '../../components/drawer.dart';
import '../../routes/router_constants.dart';
import '../../services/home_service.dart';
import '../../services/resident_service.dart';
import '../../services/ticket_category_service.dart';
import '../utils/snackbar_utils.dart';
import '../../models/ticket_category.dart';

class TicketsScreen extends StatefulWidget {
  @override
  _TicketsScreenState createState() => _TicketsScreenState();
}

class _TicketsScreenState extends State<TicketsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  late List<Unit> units = [];
  late List<Home> homes = [];
  late List<TicketCategory> categories = [];

  // Move all form-related variables to class level
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descController = TextEditingController();
  final TextEditingController _categoryController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();
  final TextEditingController _notController = TextEditingController();
  final TextEditingController _attachmentController = TextEditingController();

  List<String> attachments = [];
  TicketStatus status = TicketStatus.open;
  String? selectedPriority = 'Medium';
  String? selectedCategoryId;
  String? selectedCategoryName;
  String? selectedResident;
  String? selectedUnit;
  String? selectedHome;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    context.read<TicketBloc>().add(FetchTickets());
    fetchUnitsHomesAndCategories();
  }

  @override
  void dispose() {
    // Don't forget to dispose controllers
    _searchController.dispose();
    _titleController.dispose();
    _descController.dispose();
    _categoryController.dispose();
    _locationController.dispose();
    _notController.dispose();
    _attachmentController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void fetchUnitsHomesAndCategories() async {
    try {
      units = await UnitService().getUnits();
      homes = await HomeService().getHomes();
      categories = await TicketCategoryService().fetchCategories();
      setState(() {}); // Refresh UI after fetching
    } catch (e) {
      print('Error fetching units and homes: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) =>
              ResidentBloc(ResidentService())..add(FetchResidents()),
        ),
      ],
      child: BlocListener<TicketBloc, TicketState>(
        listener: (context, state) {
          if (state is TicketAccessDenied) {
            showAccessDeniedSnackBar(context);
          }
        },
        child: Scaffold(
          key: scaffoldKey,
          backgroundColor: const Color(0xFFF5F5F7),
          appBar: _buildAppBar(),
          drawer: buildDrawer(scaffoldKey: scaffoldKey),
          body: Column(
            children: [
              _buildStatisticsSection(),
              _buildSearchBar(),
              _buildTabBar(),
              Expanded(
                child: BlocBuilder<TicketBloc, TicketState>(
                  builder: (context, state) {
                    if (state is TicketLoading) {
                      return const Center(child: CircularProgressIndicator());
                    } else if (state is TicketLoaded) {
                      final open = state.tickets
                          .where((t) => t.status == TicketStatus.open)
                          .toList();
                      final inProgress = state.tickets
                          .where((t) => t.status == TicketStatus.inProgress)
                          .toList();
                      final completed = state.tickets
                          .where((t) => t.status == TicketStatus.completed)
                          .toList();
                      return TabBarView(
                        controller: _tabController,
                        children: [
                          _buildTicketList(open, TicketStatus.open),
                          _buildTicketList(inProgress, TicketStatus.inProgress),
                          _buildTicketList(completed, TicketStatus.completed),
                          _buildCreateTicketTab(context),
                        ],
                      );
                    } else if (state is TicketError) {
                      return Center(child: Text('Error: ${state.message}'));
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color.fromRGBO(90, 38, 101, 1),
      elevation: 0,
      leading: IconButton(
        icon: Icon(HugeIcons.strokeRoundedMenu01, color: Colors.white),
        onPressed: () => scaffoldKey.currentState?.openDrawer(),
      ),
      title: const Text(
        'Service Tickets',
        style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
      ),
      actions: [
        IconButton(
          icon: Icon(
            Icons.arrow_back_ios_new,
            color: Colors.white,
            size: 20,
          ),
          onPressed: () {
            GoRouter.of(context).goNamed(RouteConstants.homescreenRouteName);
          },
        )
      ],
    );
  }

  Widget _buildStatisticsSection() {
    return BlocBuilder<TicketBloc, TicketState>(
      builder: (context, state) {
        int open = 0, inProgress = 0, completed = 0, cancelled = 0;
        if (state is TicketLoaded) {
          open =
              state.tickets.where((t) => t.status == TicketStatus.open).length;
          inProgress = state.tickets
              .where((t) => t.status == TicketStatus.inProgress)
              .length;
          completed = state.tickets
              .where((t) => t.status == TicketStatus.completed)
              .length;
          cancelled = state.tickets
              .where((t) => t.status == TicketStatus.cancelled)
              .length;
        }
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: const BoxDecoration(
            color: Color.fromRGBO(90, 38, 101, 1),
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(16),
              bottomRight: Radius.circular(16),
            ),
          ),
          child: Row(
            children: [
              _buildCompactStatCard(
                  'Open', open, Icons.fiber_new, Colors.blue.shade300),
              const SizedBox(width: 8),
              _buildCompactStatCard('Progress', inProgress, Icons.pending,
                  Colors.orange.shade300),
              const SizedBox(width: 8),
              _buildCompactStatCard(
                  'Done', completed, Icons.check_circle, Colors.green.shade300),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCompactStatCard(
      String title, int count, IconData icon, Color accentColor) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.08),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.white.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: accentColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                icon,
                color: accentColor,
                size: 16,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    count.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      height: 1.0,
                    ),
                  ),
                  Text(
                    title,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      height: 1.0,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search tickets...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () => _searchController.clear(),
          ),
          filled: true,
          fillColor: Colors.white,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(30),
            borderSide: BorderSide.none,
          ),
        ),
        onChanged: (value) => _filterTickets(value),
      ),
    );
  }

  Widget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      labelColor: const Color.fromRGBO(90, 38, 101, 1),
      unselectedLabelColor: Colors.grey,
      indicatorColor: const Color.fromRGBO(90, 38, 101, 1),
      tabs: const [
        Tab(text: 'Open'),
        Tab(text: 'In Progress'),
        Tab(text: 'Completed'),
        Tab(text: 'Create'),
      ],
    );
  }

  Widget _buildCreateTicketTab(BuildContext context) {
    // Now using class-level variables instead of local ones
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF4C63D2), Color(0xFF7C4DFF)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF4C63D2).withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Icon(
                        Icons.confirmation_number_outlined,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 16),
                    const Text(
                      'Create New Ticket',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                        letterSpacing: 0.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Submit your request or issue for quick resolution',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.8),
                    fontWeight: FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Form Section
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 20,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Basic Information
                    _buildSectionTitle('Basic Information'),
                    const SizedBox(height: 16),
                    _buildModernTextField(
                      controller: _titleController,
                      label: 'Ticket Title',
                      icon: Icons.title_rounded,
                      hint: 'Enter a brief, descriptive title',
                      validator: (v) =>
                          v == null || v.isEmpty ? 'Title required' : null,
                    ),

                    const SizedBox(height: 10),

                    BlocBuilder<ResidentBloc, ResidentState>(
                      builder: (context, state) {
                        final mediaQuery = MediaQuery.of(context);
                        final screenWidth = mediaQuery.size.width;
                        if (state is ResidentLoading) {
                          return Container(
                            padding: EdgeInsets.all(screenWidth * 0.04),
                            decoration: BoxDecoration(
                              color: const Color(0xFFF7FAFC),
                              borderRadius:
                                  BorderRadius.circular(screenWidth * 0.03),
                              border: Border.all(
                                color: const Color(0xFFE2E8F0),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                SizedBox(
                                  width: screenWidth * 0.05,
                                  height: screenWidth * 0.05,
                                  child: const CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Color(0xFFFF6B6B),
                                  ),
                                ),
                                SizedBox(width: screenWidth * 0.03),
                                Text(
                                  'Loading residents...',
                                  style: TextStyle(
                                    color: Color(0xFF718096),
                                    fontSize: screenWidth * 0.035,
                                  ),
                                ),
                              ],
                            ),
                          );
                        } else if (state is ResidentLoaded &&
                            state.filteredResidents.isNotEmpty) {
                          return DropdownButtonFormField<String>(
                            decoration: InputDecoration(
                              labelText: 'Select Resident',
                              prefixIcon: Icon(Icons.person_outline,
                                  color: Color(0xFF4C63D2)),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                  color: Color(0xFFE2E8F0),
                                  width: 1,
                                ),
                              ),
                              filled: true,
                              fillColor: const Color(0xFFF7FAFC),
                            ),
                            value: selectedResident,
                            items: state.filteredResidents
                                .map((resident) => DropdownMenuItem<String>(
                                      value: resident.id,
                                      child: Text(resident.name),
                                    ))
                                .toList(),
                            onChanged: (value) {
                              setState(() {
                                selectedResident = value;
                              });
                            },
                            validator: (value) => value == null
                                ? 'Please select a resident'
                                : null,
                            isExpanded: true,
                          );
                        } else if (state is ResidentLoaded &&
                            state.filteredResidents.isEmpty) {
                          return Text('No residents found',
                              style: TextStyle(color: Colors.red));
                        } else if (state is ResidentError) {
                          return Text(
                              'Error loading residents: ${state.message}',
                              style: TextStyle(color: Colors.red));
                        }
                        return const SizedBox();
                      },
                    ),

                    const SizedBox(height: 16),

                    _buildModernTextField(
                      controller: _descController,
                      label: 'Description',
                      icon: Icons.description_outlined,
                      maxLines: 3,
                      hint:
                          'Provide detailed information about your request or issue...',
                      validator: (v) => v == null || v.isEmpty
                          ? 'Description required'
                          : null,
                    ),

                    const SizedBox(height: 16),

                    _buildModernTextField(
                      controller: _notController,
                      label: 'Notes',
                      icon: Icons.description_outlined,
                      maxLines: 2,
                      hint: 'Any additional notes or context',
                      validator: (v) => v == null || v.isEmpty
                          ? 'Description required'
                          : null,
                    ),
                    const SizedBox(height: 16),
                    // Categorization
                    _buildSectionTitle('Categorization'),
                    const SizedBox(height: 16),
                    // Category Dropdown
                    DropdownButtonFormField<String>(
                      decoration: InputDecoration(
                        labelText: 'Category',
                        prefixIcon: Icon(Icons.category_outlined,
                            color: Color(0xFF4C63D2)),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(
                            color: Color(0xFFE2E8F0),
                            width: 1,
                          ),
                        ),
                        filled: true,
                        fillColor: const Color(0xFFF7FAFC),
                      ),
                      value: selectedCategoryId,
                      items: categories
                          .map((cat) => DropdownMenuItem<String>(
                                value: cat.id,
                                child: Text(cat.name),
                              ))
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          selectedCategoryId = value;
                        });
                      },
                      validator: (value) =>
                          value == null ? 'Please select a category' : null,
                      isExpanded: true,
                    ),
                    const SizedBox(height: 20),
                    _buildModernDropdown(
                      label: 'Priority',
                      icon: Icons.flag_outlined,
                      value: selectedPriority,
                      items: ['Low', 'Medium', 'High', 'Urgent']
                          .map((priority) => DropdownMenuItem<String>(
                                value: priority,
                                child: Row(
                                  children: [
                                    Container(
                                      width: 12,
                                      height: 12,
                                      decoration: BoxDecoration(
                                        color: _getPriorityColor(priority),
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(priority,
                                        overflow: TextOverflow.clip,
                                        style: const TextStyle(fontSize: 14)),
                                  ],
                                ),
                              ))
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          selectedPriority = value;
                        });
                      },
                    ),

                    const SizedBox(height: 20),

                    _buildModernDropdown(
                        label: 'Units',
                        icon: Icons.ad_units_sharp,
                        value: selectedUnit,
                        items: [
                          ...units.map((unit) => DropdownMenuItem<String>(
                                value: unit.id,
                                child: Text(unit.name),
                              ))
                        ],
                        onChanged: (value) {
                          setState(() {
                            selectedUnit = value;
                          });
                        }),

                    SizedBox(height: 20),

                    _buildModernDropdown(
                        label: 'Homes',
                        icon: Icons.home_outlined,
                        value: selectedHome,
                        items: [
                          ...homes.map((home) => DropdownMenuItem<String>(
                                value: home.homeId,
                                child: Text(home.homeName),
                              ))
                        ],
                        onChanged: (value) {
                          setState(() {
                            selectedHome = value;
                          });
                        }),

                    const SizedBox(height: 20),

                    _buildModernTextField(
                      controller: _locationController,
                      label: 'Location',
                      icon: Icons.location_on_outlined,
                      hint: 'Where is this issue located?',
                      validator: (v) =>
                          v == null || v.isEmpty ? 'Location required' : null,
                    ),

                    const SizedBox(height: 32),

                    // Attachments Section
                    _buildSectionTitle('Supporting Information'),
                    const SizedBox(height: 16),
                    _buildAttachmentSection(),

                    const SizedBox(height: 40),

                    // Submit Button
                    Container(
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF4C63D2), Color(0xFF7C4DFF)],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF4C63D2).withOpacity(0.4),
                            blurRadius: 15,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: ElevatedButton(
                        onPressed: () {
                          final userState = context.read<UserBloc>().state;
                          String addedById = '';
                          if (userState is UserLoaded) {
                            addedById = userState.user.id;
                          }
                          if (_formKey.currentState!.validate()) {
                            final ticket = TicketModel(
                              id: DateTime.now()
                                  .millisecondsSinceEpoch
                                  .toString(),
                              date: DateTime.now().toIso8601String(),
                              title: _titleController.text,
                              description: _descController.text,
                              notes: _notController.text,
                              category: selectedCategoryId ?? '',
                              location: _locationController.text,
                              addedBy: addedById,
                              residentId: selectedResident ?? '',
                              status: status,
                              attachments: attachments,
                              timeline: [],
                            );

                            context.read<TicketBloc>().add(CreateTicket(ticket,
                                unit: selectedUnit, home: selectedHome));

                            // Success feedback
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: const Row(
                                  children: [
                                    Icon(Icons.check_circle,
                                        color: Colors.white),
                                    SizedBox(width: 8),
                                    Text('Ticket created successfully!'),
                                  ],
                                ),
                                backgroundColor: Colors.green,
                                behavior: SnackBarBehavior.floating,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            );

                            // Clear form after successful submission
                            _clearForm();
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          shadowColor: Colors.transparent,
                          padding: const EdgeInsets.symmetric(vertical: 18),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.send_rounded,
                              color: Colors.white,
                              size: 20,
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Submit Ticket',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Bottom spacing
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  // Add method to clear form
  void _clearForm() {
    _titleController.clear();
    _descController.clear();
    _locationController.clear();
    _notController.clear();
    _attachmentController.clear();
    setState(() {
      selectedResident = null;
      selectedCategoryId = null;
      selectedPriority = 'Medium';
      selectedUnit = null;
      selectedHome = null;
      attachments = [];
    });
  }

  Widget _buildSectionTitle(String title) {
    return Row(
      children: [
        Container(
          width: 4,
          height: 20,
          decoration: BoxDecoration(
            color: const Color(0xFF4C63D2),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2D3748),
          ),
        ),
      ],
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    int maxLines = 1,
    String? hint,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF7FAFC),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFE2E8F0),
          width: 1,
        ),
      ),
      child: TextFormField(
        controller: controller,
        maxLines: maxLines,
        validator: validator,
        style: const TextStyle(
          fontSize: 16,
          color: Color(0xFF2D3748),
        ),
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          prefixIcon: Icon(
            icon,
            color: const Color(0xFF4C63D2),
            size: 20,
          ),
          labelStyle: const TextStyle(
            color: Color(0xFF718096),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          hintStyle: TextStyle(
            color: const Color(0xFF718096).withOpacity(0.7),
            fontSize: 14,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
          floatingLabelBehavior: FloatingLabelBehavior.auto,
        ),
      ),
    );
  }

  Widget _buildModernDropdown({
    required String label,
    required IconData icon,
    required String? value,
    required List<DropdownMenuItem<String>> items,
    required Function(String?) onChanged,
    String? Function(String?)? validator,
  }) {
    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(
          icon,
          color: const Color(0xFF4C63D2),
          size: 20,
        ),
        labelStyle: const TextStyle(
          color: Color(0xFF718096),
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        filled: true,
        fillColor: const Color(0xFFF7FAFC),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFFE2E8F0),
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFFE2E8F0),
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFF4C63D2),
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
      dropdownColor: Colors.white,
      value: value,
      style: const TextStyle(
        fontSize: 16,
        color: Color(0xFF2D3748),
      ),
      items: items,
      onChanged: onChanged,
      validator: validator,
      isExpanded: true,
    );
  }

  Widget _buildAttachmentSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFF7FAFC),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFFE2E8F0),
              width: 1,
            ),
          ),
          child: TextFormField(
            controller: _attachmentController,
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF2D3748),
            ),
            decoration: InputDecoration(
              labelText: 'Additional Resources',
              hintText:
                  'Add URLs, references, or additional information (comma separated)',
              prefixIcon: const Icon(
                Icons.link_rounded,
                color: Color(0xFF4C63D2),
                size: 20,
              ),
              labelStyle: const TextStyle(
                color: Color(0xFF718096),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              hintStyle: TextStyle(
                color: const Color(0xFF718096).withOpacity(0.7),
                fontSize: 14,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
              floatingLabelBehavior: FloatingLabelBehavior.auto,
            ),
            onChanged: (value) {
              final newAttachments = value
                  .split(',')
                  .map((e) => e.trim())
                  .where((e) => e.isNotEmpty)
                  .toList();
              setState(() {
                attachments = newAttachments;
              });
            },
          ),
        ),
        const SizedBox(height: 8),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 16,
                color: const Color(0xFF718096).withOpacity(0.7),
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  'You can add links to documents, images, or other relevant resources',
                  style: TextStyle(
                    fontSize: 12,
                    color: const Color(0xFF718096).withOpacity(0.7),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'low':
        return const Color(0xFF48BB78);
      case 'medium':
        return const Color(0xFFED8936);
      case 'high':
        return const Color(0xFFF56565);
      case 'urgent':
        return const Color(0xFF9F2C32);
      default:
        return const Color(0xFF718096);
    }
  }

  Widget _buildTicketList(List<TicketModel> tickets, TicketStatus status) {
    return tickets.isEmpty
        ? _buildEmptyState(status)
        : ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: tickets.length,
            itemBuilder: (context, index) => _buildTicketCard(tickets[index]),
          );
  }

  Widget _buildTicketCard(TicketModel ticket) {
    return Card(
      color: Colors.white,
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: ExpansionTile(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                _buildStatusBadge(ticket.status),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    ticket.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              'Created on ${ticket.date.toString().split('T')[0]}',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  ticket.description,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Divider(),
                const SizedBox(height: 8),
                _buildTicketActions(ticket),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge(TicketStatus status) {
    final statusConfig = _getStatusConfig(status);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusConfig.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusConfig.icon, size: 16, color: statusConfig.color),
          const SizedBox(width: 4),
          Text(
            statusConfig.label,
            style: TextStyle(
              color: statusConfig.color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTicketActions(TicketModel ticket) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildActionButton(
          'View',
          Icons.visibility,
          Colors.blue,
          () => _viewTicketDetails(ticket),
        ),
        if (ticket.status != TicketStatus.completed &&
            ticket.status != TicketStatus.cancelled) ...[
          _buildActionButton(
            'Close',
            Icons.check_circle,
            Colors.green,
            () => _closeTicket(ticket),
          ),
          _buildActionButton(
            'Cancel',
            Icons.cancel,
            Colors.red,
            () => _cancelTicket(ticket),
          ),
        ],
      ],
    );
  }

  Widget _buildActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      icon: Icon(icon, color: Colors.white, size: 20),
      label: Text(label, style: const TextStyle(color: Colors.white)),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      onPressed: onPressed,
    );
  }

  Widget _buildEmptyState(TicketStatus status) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getStatusConfig(status).icon,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No ${status.toString().split('.').last} tickets',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tickets will appear here when created',
            style: TextStyle(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  void _viewTicketDetails(TicketModel ticket) {
    GoRouter.of(context).goNamed(
      RouteConstants.ticketDetailScreenRouteName,
      extra: ticket,
    );
  }

  void _closeTicket(TicketModel ticket) {
    setState(() {
      ticket.status = TicketStatus.completed;
      _updateTicketLists(ticket);
    });
  }

  void _cancelTicket(TicketModel ticket) {
    setState(() {
      ticket.status = TicketStatus.cancelled;
      _updateTicketLists(ticket);
    });
  }

  void _updateTicketLists(TicketModel ticket) {
    context
        .read<TicketBloc>()
        .add(UpdateTicketStatus(ticket.id, ticket.status));
  }

  void _filterTickets(String query) {
    // Implement ticket filtering logic
  }

  void _showFilterDialog() {
    // Implement filter dialog
  }

  void _showSortDialog() {
    // Implement sort dialog
  }

  void _showCreateTicketDialog() {
    // Implement create ticket dialog
  }
}

class StatusConfig {
  final Color color;
  final IconData icon;
  final String label;

  StatusConfig({
    required this.color,
    required this.icon,
    required this.label,
  });
}

StatusConfig _getStatusConfig(TicketStatus status) {
  switch (status) {
    case TicketStatus.open:
      return StatusConfig(
        color: Colors.blue,
        icon: Icons.fiber_new,
        label: 'Open',
      );
    case TicketStatus.inProgress:
      return StatusConfig(
        color: Colors.orange,
        icon: Icons.pending,
        label: 'In Progress',
      );
    case TicketStatus.completed:
      return StatusConfig(
        color: Colors.green,
        icon: Icons.check_circle,
        label: 'Completed',
      );
    case TicketStatus.cancelled:
      return StatusConfig(
        color: Colors.red,
        icon: Icons.cancel,
        label: 'Cancelled',
      );
  }
}
