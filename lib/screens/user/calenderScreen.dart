import 'dart:ui';

import 'package:carerez/blocs/user/user_bloc.dart';
import 'package:carerez/blocs/user/user_event.dart';
import 'package:carerez/components/bottomNavBar.dart';
import 'package:carerez/components/drawer.dart';
import 'package:carerez/models/calendar_task.dart';
import 'package:carerez/routes/router_constants.dart';
import 'package:carerez/services/calendar_service.dart';
import 'package:carerez/utils/screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';
import 'dart:math' as math;

import '../../blocs/auth/auth_bloc.dart';
import '../../blocs/user/user_state.dart';
import '../../models/assign_task.dart';
import '../../services/task_service.dart';

class CalendarPage extends StatefulWidget {
  @override
  _CalendarPageState createState() => _CalendarPageState();
}

class _CalendarPageState extends State<CalendarPage>
    with TickerProviderStateMixin {
  final List<String> _timeSlots = List.generate(24, (index) {
    final hour = index;
    final period = hour < 12 ? 'AM' : 'PM';
    final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    return '${displayHour.toString().padLeft(2, '0')}:00 $period';
  });

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final ScrollController _timeScrollController = ScrollController();
  final ScrollController _horizontalScrollController = ScrollController();
  final PageController _pageController = PageController();

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _bounceController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _bounceAnimation;

  DateTime _currentDate = DateTime.now();
  List<AssignTask> _tasks = [];
  bool _isLoading = false;
  String _selectedView = 'week';

  final _taskTypes = [
    'Heat Pack',
    'Skin Integrity',
    'Conscious',
    'PAP Therapy',
    'Clinic',
  ];

  // Dynamic visible days based on screen width
  int get _visibleDays {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 3; // Mobile: show 3 days
    if (screenWidth < 900) return 5; // Tablet: show 5 days
    return 7; // Desktop: show 7 days
  }

  late final _ScrollControllerManager _scrollManager;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers with staggered animations
    _fadeController = AnimationController(
      duration: Duration(milliseconds: 600),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: Duration(milliseconds: 800),
      vsync: this,
    );
    _bounceController = AnimationController(
      duration: Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _fadeController,
        curve: Interval(0.0, 0.7, curve: Curves.easeOut),
      ),
    );
    _slideAnimation = Tween<Offset>(
      begin: Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));
    _bounceAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _bounceController,
        curve: Curves.elasticOut,
      ),
    );

    _scrollManager = _ScrollControllerManager(
      timeScrollController: _timeScrollController,
      horizontalScrollController: _horizontalScrollController,
    );

    final userState = context.read<UserBloc>().state;
    if (userState is! UserLoaded) {
      context.read<UserBloc>().add(FetchCurrentUser());
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeCalendar();
    });
  }

  Future<void> _initializeCalendar() async {
    // Staggered animation sequence
    _fadeController.forward();
    await Future.delayed(Duration(milliseconds: 200));
    _slideController.forward();
    await Future.delayed(Duration(milliseconds: 300));
    _bounceController.forward();

    await _loadTasks();
    _scrollToCurrentTime();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _bounceController.dispose();
    _scrollManager.dispose();
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadTasks() async {
    setState(() => _isLoading = true);

    final userState = context.read<UserBloc>().state;
    String? staffId;
    if (userState is UserLoaded) {
      staffId = userState.user.id;
    } else {
      setState(() => _isLoading = false);
      return;
    }

    try {
      final tasks = await CalendarService().getCalendarTasks(staffId);
      if (mounted) {
        setState(() {
          _tasks = tasks;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading tasks: $e');
      if (mounted) {
        setState(() {
          _tasks = [];
          _isLoading = false;
        });
        _showErrorSnackBar('Failed to load tasks. Please try again.');
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.white, size: 20),
            SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red.shade600,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        margin: EdgeInsets.all(16),
        elevation: 8,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle_outline, color: Colors.white, size: 20),
            SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green.shade600,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        margin: EdgeInsets.all(16),
        elevation: 8,
      ),
    );
  }

  List<AssignTask> _getTasksForSlot(DateTime date, String timeSlot) {
    if (_tasks.isEmpty) return [];
    return _tasks.where((task) {
      final taskDate = task.date;
      return taskDate.year == date.year &&
          taskDate.month == date.month &&
          taskDate.day == date.day;
    }).toList();
  }

  Widget _buildCurrentTimeIndicator() {
    final now = DateTime.now();
    final currentMinutes = now.hour * 60 + now.minute;
    final cellHeight = 80.0; // Fixed cell height for mobile
    final position = (currentMinutes / 60) * cellHeight;

    return AnimatedPositioned(
      duration: Duration(seconds: 1),
      curve: Curves.easeInOut,
      top: position,
      left: 50.0,
      right: 0,
      child: Container(
        height: 3,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.red.shade600,
              Colors.red.shade400,
            ],
          ),
          borderRadius: BorderRadius.circular(2),
          boxShadow: [
            BoxShadow(
              color: Colors.red.shade600.withOpacity(0.4),
              blurRadius: 8,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 10,
              height: 10,
              decoration: BoxDecoration(
                color: Colors.red.shade600,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.red.shade600.withOpacity(0.6),
                    blurRadius: 6,
                    spreadRadius: 1,
                  ),
                ],
              ),
            ),
            Expanded(child: Container()),
          ],
        ),
      ),
    );
  }

  Widget _buildTaskCell(DateTime date, String timeSlot, List<AssignTask> tasks) {
    final isCurrentHour = _isCurrentHour(date, timeSlot);
    final isToday = _isToday(date);

    return AnimatedContainer(
      duration: Duration(milliseconds: 400),
      curve: Curves.easeInOut,
      height: 80.0, // Fixed height for consistency
      margin: EdgeInsets.symmetric(horizontal: 2, vertical: 1),
      decoration: BoxDecoration(
        color: isCurrentHour && isToday
            ? Color.fromRGBO(90, 38, 101, 0.08)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isCurrentHour && isToday
              ? Color.fromRGBO(90, 38, 101, 0.2)
              : Colors.grey.withOpacity(0.1),
          width: isCurrentHour && isToday ? 1.5 : 0.5,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          onTap: () => _showAddTaskDialog(context, date, timeSlot),
          borderRadius: BorderRadius.circular(8),
          splashColor: Color.fromRGBO(90, 38, 101, 0.1),
          highlightColor: Color.fromRGBO(90, 38, 101, 0.05),
          child: tasks.isEmpty
              ? Center(
            child: AnimatedScale(
              scale: isCurrentHour && isToday ? 1.2 : 1.0,
              duration: Duration(milliseconds: 300),
              child: Icon(
                Icons.add_circle_outline,
                color: isCurrentHour && isToday
                    ? Color.fromRGBO(90, 38, 101, 0.6)
                    : Colors.grey.shade300,
                size: 24,
              ),
            ),
          )
              : Padding(
            padding: EdgeInsets.all(6),
            child: Column(
              children: tasks.take(2).map((task) => _buildTaskCard(task)).toList(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTaskCard(AssignTask task) {
    return Expanded(
      child: Container(
        margin: EdgeInsets.only(bottom: 4),
        child: Material(
          elevation: 3,
          borderRadius: BorderRadius.circular(10),
          shadowColor: _getTaskColor(task.taskColor).withOpacity(0.3),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  _getTaskColor(task.taskColor),
                  _getTaskColor(task.taskColor).withOpacity(0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(10),
            ),
            child: InkWell(
              onTap: () => _showTaskDetails(task),
              borderRadius: BorderRadius.circular(10),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                child: Row(
                  children: [
                    Container(
                      width: 3,
                      height: 20,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.8),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        task.task,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 11,
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    GestureDetector(
                      onTap: () => _showDeleteConfirmation(task),
                      child: Container(
                        padding: EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return now.year == date.year &&
        now.month == date.month &&
        now.day == date.day;
  }

  bool _isCurrentHour(DateTime date, String timeSlot) {
    final now = DateTime.now();
    final slotTime = _parseDateTime(date, timeSlot);

    return now.year == date.year &&
        now.month == date.month &&
        now.day == date.day &&
        now.hour == slotTime.hour;
  }

  Color _getTaskColor(String type) {
    switch (type) {
      case 'Heat Pack':
        return Colors.orange.shade600;
      case 'Skin Integrity':
        return Colors.blue.shade600;
      case 'Conscious':
        return Colors.green.shade600;
      case 'PAP Therapy':
        return Colors.purple.shade600;
      case 'Clinic':
        return Colors.red.shade600;
      case 'neutral':
        return Colors.blueGrey.shade600;
      case 'emergency':
        return Colors.red.shade900;
      default:
        return Colors.grey.shade600;
    }
  }

  void _scrollToCurrentTime() {
    final now = DateTime.now();
    final currentHour = now.hour;
    final currentMinute = now.minute;

    final rowHeight = 80.0; // Fixed cell height
    final scrollPosition = (currentHour + currentMinute / 60) * rowHeight;

    Future.delayed(Duration(milliseconds: 500), () {
      if (_scrollManager.timeScrollController.hasClients) {
        _scrollManager.scrollToVerticalOffset(
          math.max(0, scrollPosition - (rowHeight * 2)),
          duration: Duration(milliseconds: 1000),
          curve: Curves.easeOutCubic,
        );
      }
    });
  }

  List<Map<String, dynamic>> _getDaysWithDates() {
    List<Map<String, dynamic>> daysWithDates = [];

    // Calculate start date to center current date
    int dayOffset = _visibleDays ~/ 2;
    DateTime startDate = _currentDate.subtract(Duration(days: dayOffset));

    for (int i = 0; i < _visibleDays; i++) {
      DateTime day = startDate.add(Duration(days: i));
      bool isToday = _isToday(day);

      daysWithDates.add({
        "day": DateFormat('EEE').format(day),
        "date": day.day.toString(),
        "month": DateFormat('MMM').format(day),
        "isToday": isToday,
        "dateTime": day,
      });
    }
    return daysWithDates;
  }

  Widget _buildHeader() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color.fromRGBO(90, 38, 101, 1),
            Color.fromRGBO(120, 60, 140, 1),
            Color.fromRGBO(90, 38, 101, 0.9),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 12,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          children: [
            // App Bar
            Container(
              height: 60,
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  IconButton(
                    icon: Icon(Icons.menu, color: Colors.white, size: 24),
                    onPressed: () => _scaffoldKey.currentState!.openDrawer(),
                  ),
                  Expanded(
                    child: Text(
                      'Calendar',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 0.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: IconButton(
                      icon: Icon(Icons.refresh, color: Colors.white, size: 20),
                      onPressed: () {
                        setState(() {
                          _currentDate = DateTime.now();
                        });
                        _loadTasks();
                        _scrollToCurrentTime();
                      },
                    ),
                  ),
                  SizedBox(width: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: IconButton(
                      icon: Icon(Icons.home, color: Colors.white, size: 20),
                      onPressed: () => GoRouter.of(context)
                          .goNamed(RouteConstants.homescreenRouteName),
                    ),
                  ),
                ],
              ),
            ),

            // Month/Year Display
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text(
                DateFormat('MMMM yyyy').format(_currentDate),
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),

            // Date Navigation
            Container(
              height: 90,
              child: Row(
                children: [
                  // Navigation buttons
                  Container(
                    width: 50,
                    child: _buildNavigationButton(
                      Icons.chevron_left,
                          () => _navigateWeek(-1),
                    ),
                  ),

                  // Date headers
                  Expanded(
                    child: Row(
                      children: _getDaysWithDates().map((dayData) {
                        return Expanded(child: _buildDateHeader(dayData));
                      }).toList(),
                    ),
                  ),

                  Container(
                    width: 50,
                    child: _buildNavigationButton(
                      Icons.chevron_right,
                          () => _navigateWeek(1),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationButton(IconData icon, VoidCallback onTap) {
    return ScaleTransition(
      scale: _bounceAnimation,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(25),
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Icon(icon, color: Colors.white, size: 20),
          ),
        ),
      ),
    );
  }

  Widget _buildDateHeader(Map<String, dynamic> dayData) {
    final isToday = dayData["isToday"] as bool;

    return AnimatedContainer(
      duration: Duration(milliseconds: 400),
      curve: Curves.easeInOut,
      margin: EdgeInsets.symmetric(horizontal: 4, vertical: 8),
      decoration: BoxDecoration(
        color: isToday
            ? Colors.white.withOpacity(0.25)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        border: isToday
            ? Border.all(color: Colors.white.withOpacity(0.4), width: 1.5)
            : null,
        boxShadow: isToday ? [
          BoxShadow(
            color: Colors.white.withOpacity(0.2),
            blurRadius: 8,
            spreadRadius: 1,
          ),
        ] : null,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            dayData["day"]!,
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontWeight: FontWeight.w500,
              fontSize: 12,
              letterSpacing: 0.5,
            ),
          ),
          SizedBox(height: 6),
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: isToday ? Colors.white : Colors.transparent,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Text(
                dayData["date"]!,
                style: TextStyle(
                  color: isToday ? Color.fromRGBO(90, 38, 101, 1) : Colors.white,
                  fontSize: 16,
                  fontWeight: isToday ? FontWeight.bold : FontWeight.w600,
                ),
              ),
            ),
          ),
          SizedBox(height: 4),
          if (isToday)
            Container(
              width: 4,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
            ),
        ],
      ),
    );
  }

  void _navigateWeek(int direction) {
    setState(() {
      _currentDate = _currentDate.add(Duration(days: _visibleDays * direction));
    });
    _loadTasks();

    // Add slide animation
    _slideController.reset();
    _slideController.forward();
  }

  @override
  Widget build(BuildContext context) {
    final double timeColumnWidth = 50.0; // Reduced for mobile
    final double dayColumnWidth = (MediaQuery.of(context).size.width - timeColumnWidth) / _visibleDays;

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      key: _scaffoldKey,
      drawer: buildDrawer(scaffoldKey: _scaffoldKey),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: Column(
            children: [
              _buildHeader(),

              // Loading indicator
              if (_isLoading)
                Container(
                  height: 3,
                  child: LinearProgressIndicator(
                    backgroundColor: Colors.grey.shade200,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Color.fromRGBO(90, 38, 101, 1),
                    ),
                  ),
                ),

              // Calendar grid
              Expanded(
                child: Stack(
                  children: [
                    Row(
                      children: [
                        // Time column
                        Container(
                          width: timeColumnWidth,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 4,
                                offset: Offset(2, 0),
                              ),
                            ],
                          ),
                          child: ListView.builder(
                            controller: _scrollManager.timeScrollController,
                            itemCount: _timeSlots.length,
                            itemBuilder: (context, index) => Container(
                              height: 80.0,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(
                                    color: Colors.grey.withOpacity(0.15),
                                    width: 0.5,
                                  ),
                                ),
                              ),
                              child: Text(
                                _timeSlots[index].replaceAll(':00', ''),
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                        ),

                        // Calendar cells
                        Expanded(
                          child: Row(
                            children: List.generate(_visibleDays, (dayIndex) {
                              final date = _getDaysWithDates()[dayIndex]["dateTime"] as DateTime;
                              return Container(
                                width: dayColumnWidth,
                                decoration: BoxDecoration(
                                  border: Border(
                                    right: BorderSide(
                                      color: Colors.grey.withOpacity(0.1),
                                      width: 0.5,
                                    ),
                                  ),
                                ),
                                child: ListView.builder(
                                  controller: _scrollManager.getVerticalControllerForDay(dayIndex),
                                  itemCount: _timeSlots.length,
                                  itemBuilder: (context, timeIndex) {
                                    final tasks = _getTasksForSlot(date, _timeSlots[timeIndex]);
                                    return _buildTaskCell(date, _timeSlots[timeIndex], tasks);
                                  },
                                ),
                              );
                            }),
                          ),
                        ),
                      ],
                    ),

                    // Current time indicator
                    if (_isCurrentDateVisible())
                      _buildCurrentTimeIndicator(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: FloatingBottomNavBar(selectedIndex: 1),
      floatingActionButton: ScaleTransition(
        scale: _bounceAnimation,
        child: FloatingActionButton(
          onPressed: () => _showAddTaskDialog(context, DateTime.now(), _getCurrentTimeSlot()),
          backgroundColor: Color.fromRGBO(90, 38, 101, 1),
          elevation: 8,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color.fromRGBO(90, 38, 101, 1),
                  Color.fromRGBO(120, 60, 140, 1),
                ],
              ),
              borderRadius: BorderRadius.circular(28),
            ),
            child: Icon(Icons.add, color: Colors.white, size: 28),
          ),
        ),
      ),
    );
  }

  bool _isCurrentDateVisible() {
    final now = DateTime.now();
    final visibleDates = _getDaysWithDates();

    return visibleDates.any((dayData) {
      final date = dayData["dateTime"] as DateTime;
      return now.year == date.year &&
          now.month == date.month &&
          now.day == date.day;
    });
  }

  String _getCurrentTimeSlot() {
    final now = DateTime.now();
    final hour = now.hour;
    final period = hour < 12 ? 'AM' : 'PM';
    final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    return '${displayHour.toString().padLeft(2, '0')}:00 $period';
  }

  // [Keep all the existing dialog and utility methods unchanged]
  void _showAddTaskDialog(BuildContext context, DateTime date, String timeSlot) {
    final TextEditingController titleController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();
    String selectedType = _taskTypes[0];
    DateTime selectedDate = date;
    TimeOfDay selectedTime = TimeOfDay.fromDateTime(date);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              backgroundColor: Colors.transparent,
              insetPadding: EdgeInsets.all(16),
              child: Container(
                width: MediaQuery.of(context).size.width,
                constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * 0.8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Container(
                      padding: EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Color.fromRGBO(90, 38, 101, 1),
                            Color.fromRGBO(120, 60, 140, 1),
                          ],
                        ),
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.event_note, color: Colors.white, size: 24),
                          SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'Add New Task',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          IconButton(
                            icon: Icon(Icons.close, color: Colors.white),
                            onPressed: () => Navigator.pop(context),
                          ),
                        ],
                      ),
                    ),

                    // Form Content
                    Flexible(
                      child: SingleChildScrollView(
                        padding: EdgeInsets.all(24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Title Input
                            _buildInputField(
                              controller: titleController,
                              label: 'Task Title',
                              icon: Icons.title,
                              hint: 'Enter task title',
                            ),
                            SizedBox(height: 20),

                            // Task Type Dropdown
                            Text(
                              'Task Type',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey.shade700,
                              ),
                            ),
                            SizedBox(height: 8),
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 16),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade300),
                                borderRadius: BorderRadius.circular(12),
                                color: Colors.grey.shade50,
                              ),
                              child: DropdownButtonHideUnderline(
                                child: DropdownButton<String>(
                                  value: selectedType,
                                  isExpanded: true,
                                  icon: Icon(Icons.keyboard_arrow_down),
                                  items: _taskTypes.map((String type) {
                                    return DropdownMenuItem<String>(
                                      value: type,
                                      child: Row(
                                        children: [
                                          Container(
                                            width: 20,
                                            height: 20,
                                            margin: EdgeInsets.only(right: 12),
                                            decoration: BoxDecoration(
                                              color: _getTaskColor(type),
                                              borderRadius: BorderRadius.circular(4),
                                            ),
                                          ),
                                          Text(type, style: TextStyle(fontSize: 16)),
                                        ],
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (String? newValue) {
                                    if (newValue != null) {
                                      setState(() {
                                        selectedType = newValue;
                                      });
                                    }
                                  },
                                ),
                              ),
                            ),
                            SizedBox(height: 20),

                            // Date and Time Pickers
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDateTimePicker(
                                    label: 'Date',
                                    icon: Icons.calendar_today,
                                    value: DateFormat('MMM dd, yyyy').format(selectedDate),
                                    onTap: () async {
                                      final DateTime? picked = await showDatePicker(
                                        context: context,
                                        initialDate: selectedDate,
                                        firstDate: DateTime.now().subtract(Duration(days: 1)),
                                        lastDate: DateTime.now().add(Duration(days: 365)),
                                        builder: (context, child) {
                                          return Theme(
                                            data: Theme.of(context).copyWith(
                                              colorScheme: ColorScheme.light(
                                                primary: Color.fromRGBO(90, 38, 101, 1),
                                              ),
                                            ),
                                            child: child!,
                                          );
                                        },
                                      );
                                      if (picked != null) {
                                        setState(() {
                                          selectedDate = picked;
                                        });
                                      }
                                    },
                                  ),
                                ),
                                SizedBox(width: 12),
                                Expanded(
                                  child: _buildDateTimePicker(
                                    label: 'Time',
                                    icon: Icons.access_time,
                                    value: selectedTime.format(context),
                                    onTap: () async {
                                      final TimeOfDay? picked = await showTimePicker(
                                        context: context,
                                        initialTime: selectedTime,
                                        builder: (context, child) {
                                          return Theme(
                                            data: Theme.of(context).copyWith(
                                              colorScheme: ColorScheme.light(
                                                primary: Color.fromRGBO(90, 38, 101, 1),
                                              ),
                                            ),
                                            child: child!,
                                          );
                                        },
                                      );
                                      if (picked != null) {
                                        setState(() {
                                          selectedTime = picked;
                                        });
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 20),

                            // Description Input
                            _buildInputField(
                              controller: descriptionController,
                              label: 'Description',
                              icon: Icons.description,
                              hint: 'Enter task description (optional)',
                              maxLines: 3,
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Action Buttons
                    Container(
                      padding: EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(20),
                          bottomRight: Radius.circular(20),
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextButton(
                              onPressed: () => Navigator.pop(context),
                              style: TextButton.styleFrom(
                                padding: EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: Text(
                                'Cancel',
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: 12),
                          Expanded(
                            flex: 2,
                            child: ElevatedButton(
                              onPressed: titleController.text.isEmpty ? null : () {
                                Navigator.pop(context);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Color.fromRGBO(90, 38, 101, 1),
                                padding: EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                elevation: 2,
                              ),
                              child: Text(
                                'Add Task',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required String hint,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.grey.shade700,
          ),
        ),
        SizedBox(height: 8),
        TextField(
          controller: controller,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon, color: Colors.grey.shade500),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Color.fromRGBO(90, 38, 101, 1),
                width: 2,
              ),
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          ),
        ),
      ],
    );
  }

  Widget _buildDateTimePicker({
    required String label,
    required IconData icon,
    required String value,
    required VoidCallback onTap,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.grey.shade700,
          ),
        ),
        SizedBox(height: 8),
        InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(12),
              color: Colors.grey.shade50,
            ),
            child: Row(
              children: [
                Icon(icon, color: Colors.grey.shade500, size: 20),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    value,
                    style: TextStyle(fontSize: 16),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  DateTime _parseDateTime(DateTime date, String timeSlot) {
    final format = DateFormat('h:mm a');
    final time = format.parse(timeSlot);
    return DateTime(
      date.year,
      date.month,
      date.day,
      time.hour,
      time.minute,
    );
  }

  void _showDeleteConfirmation(AssignTask task) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text('Delete Task'),
        content: Text('Are you sure you want to delete "${task.task}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteTask(task);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('Delete', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteTask(AssignTask task) async {
    try {
      await TaskService.deleteTask(task.assignTaskId);
      await _loadTasks();
      _showSuccessSnackBar('Task deleted successfully');
    } catch (e) {
      _showErrorSnackBar('Failed to delete task');
    }
  }

  void _showTaskDetails(AssignTask task) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          padding: EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.event_note, color: _getTaskColor(task.taskColor)),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      task.task,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Text('Date: ${DateFormat('MMM dd, yyyy').format(task.date)}'),
              Text('Time: ${DateFormat('h:mm a').format(task.date)}'),
              Text('Type: ${task.taskColor}'),
              SizedBox(height: 12),
              Text('Assigned By: ${task.assignedBy != null ? task.assignedBy!.userDetails.firstName + ' ' + task.assignedBy!.userDetails.lastName : 'N/A'}'),
              SizedBox(height: 8),
              Text('Home: ${task.home.homeName}'),
            ],
          ),
        ),
      ),
    );
  }
}

// Enhanced scroll controller manager with better synchronization
class _ScrollControllerManager {
  final ScrollController timeScrollController;
  final ScrollController horizontalScrollController;
  final Map<int, ScrollController> _dayControllers = {};
  bool _isScrolling = false;

  _ScrollControllerManager({
    required this.timeScrollController,
    required this.horizontalScrollController,
  }) {
    timeScrollController.addListener(_onTimeScroll);
  }

  ScrollController getVerticalControllerForDay(int dayIndex) {
    if (!_dayControllers.containsKey(dayIndex)) {
      final controller = ScrollController();
      controller.addListener(() => _onDayScroll(dayIndex));
      _dayControllers[dayIndex] = controller;
    }
    return _dayControllers[dayIndex]!;
  }

  void _onTimeScroll() {
    if (_isScrolling) return;
    _isScrolling = true;

    for (final controller in _dayControllers.values) {
      if (controller.hasClients &&
          controller.position.maxScrollExtent > 0 &&
          (controller.offset - timeScrollController.offset).abs() > 1.0) {
        controller.jumpTo(timeScrollController.offset);
      }
    }

    _isScrolling = false;
  }

  void _onDayScroll(int dayIndex) {
    if (_isScrolling) return;
    _isScrolling = true;

    final controller = _dayControllers[dayIndex]!;

    if (timeScrollController.hasClients &&
        timeScrollController.position.maxScrollExtent > 0 &&
        (timeScrollController.offset - controller.offset).abs() > 1.0) {
      timeScrollController.jumpTo(controller.offset);
    }

    for (final entry in _dayControllers.entries) {
      if (entry.key != dayIndex &&
          entry.value.hasClients &&
          entry.value.position.maxScrollExtent > 0 &&
          (entry.value.offset - controller.offset).abs() > 1.0) {
        entry.value.jumpTo(controller.offset);
      }
    }

    _isScrolling = false;
  }

  void scrollToVerticalOffset(double offset, {Duration? duration, Curve? curve}) {
    if (duration != null && curve != null) {
      if (timeScrollController.hasClients) {
        timeScrollController.animateTo(offset, duration: duration, curve: curve);
      }
      for (final controller in _dayControllers.values) {
        if (controller.hasClients) {
          controller.animateTo(offset, duration: duration, curve: curve);
        }
      }
    } else {
      if (timeScrollController.hasClients) {
        timeScrollController.jumpTo(offset);
      }
      for (final controller in _dayControllers.values) {
        if (controller.hasClients) {
          controller.jumpTo(offset);
        }
      }
    }
  }

  void dispose() {
    timeScrollController.removeListener(_onTimeScroll);
    for (final controller in _dayControllers.values) {
      controller.dispose();
    }
    _dayControllers.clear();
  }
}