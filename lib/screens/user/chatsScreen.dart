import 'package:carerez/utils/screen.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';

import '../../components/bottomNavBar.dart';
import '../../components/drawer.dart';
import '../../routes/router_constants.dart';

class ChatsScreen extends StatefulWidget {
  ChatsScreen({super.key});

  @override
  State<ChatsScreen> createState() => _ChatsScreenState();
}

class _ChatsScreenState extends State<ChatsScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey<ScaffoldState>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      drawer: buildDrawer(scaffoldKey: _scaffoldKey),
      backgroundColor: const Color(0xFFF5F5F7), // Light gray background
      appBar: AppBar(
        backgroundColor: const Color.fromRGBO(90, 38, 101, 1),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.menu, color: Colors.white),
          onPressed: () => _scaffoldKey.currentState?.openDrawer(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
            onPressed: () => GoRouter.of(context)
                .goNamed(RouteConstants.homescreenRouteName),
          ),
        ],
        title: Row(
          children: [
            //profile icon
            Icon(HugeIcons.strokeRoundedChatting01,
                color: Colors.white, size: 24),
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Chats',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                ),
                Text(
                  'Connect with Anyone',
                  style: TextStyle(
                    color: Color(0xFF8E8E93),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            if (chatsData.isEmpty)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Center(
                  child: Text(
                    'Feature not available yet',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 16,
                    ),
                  ),
                ),
              )
            else
              Expanded(
                child: ListView.builder(
                  itemCount: chatsData.length,
                  itemBuilder: (context, index) => ChatCard(
                    chat: chatsData[index],
                    press: () {},
                  ),
                ),
              ),
          ],
        ),
      ),
      bottomNavigationBar: FloatingBottomNavBar(
        selectedIndex: 2,
      ),
    );
  }
}

class ChatCard extends StatelessWidget {
  const ChatCard({
    super.key,
    required this.chat,
    required this.press,
  });

  final Chat chat;
  final VoidCallback press;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: press,
      child: Padding(
        padding:
            const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0 * 0.75),
        child: Row(
          children: [
            CircleAvatarWithActiveIndicator(
              image: chat.image,
              isActive: chat.isActive,
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      chat.name,
                      style: const TextStyle(
                          fontSize: 16, fontWeight: FontWeight.w500),
                    ),
                    SizedBox(height: SizeConfig.screenH! * 0.01),
                    Opacity(
                      opacity: 0.64,
                      child: Text(
                        chat.lastMessage,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Opacity(
              opacity: 0.64,
              child: Text(chat.time),
            ),
          ],
        ),
      ),
    );
  }
}

class FillOutlineButton extends StatelessWidget {
  const FillOutlineButton({
    super.key,
    this.isFilled = true,
    required this.press,
    required this.text,
  });

  final bool isFilled;
  final VoidCallback press;
  final String text;

  @override
  Widget build(BuildContext context) {
    return MaterialButton(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(30),
        side: const BorderSide(color: Colors.white),
      ),
      elevation: isFilled ? 2 : 0,
      color: isFilled ? Colors.white : Colors.transparent,
      onPressed: press,
      child: Text(
        text,
        style: TextStyle(
          color: isFilled ? const Color(0xFF1D1D35) : Colors.white,
          fontSize: 12,
        ),
      ),
    );
  }
}

class CircleAvatarWithActiveIndicator extends StatelessWidget {
  const CircleAvatarWithActiveIndicator({
    super.key,
    this.image,
    this.radius = 32,
    this.isActive,
  });

  final String? image;
  final double? radius;
  final bool? isActive;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        CircleAvatar(
          radius: radius,
          backgroundImage: NetworkImage(image!),
        ),
        if (isActive!)
          Positioned(
            right: 0,
            bottom: 0,
            child: Container(
              height: SizeConfig.screenH! * 0.02,
              width: SizeConfig.screenW! * 0.02,
              decoration: BoxDecoration(
                color: const Color(0xFF00BF6D),
                shape: BoxShape.circle,
                border: Border.all(
                    color: Theme.of(context).scaffoldBackgroundColor, width: 3),
              ),
            ),
          )
      ],
    );
  }
}

class Chat {
  final String name, lastMessage, image, time;
  final bool isActive;

  Chat({
    this.name = '',
    this.lastMessage = '',
    this.image = '',
    this.time = '',
    this.isActive = false,
  });
}

List chatsData = [
  // Chat(
  //   name: "Dr. Emily Clark",
  //   lastMessage: "Please make sure to take your medication...",
  //   image: "https://i.postimg.cc/g25VYN7X/doctor-1.png",
  //   time: "2m ago",
  //   isActive: true,
  // ),
  // Chat(
  //   name: "Dr. Michael Thompson",
  //   lastMessage: "How are you feeling after the surgery?",
  //   image: "https://i.postimg.cc/cCsYDjvj/doctor-2.png",
  //   time: "15m ago",
  //   isActive: false,
  // ),
  // Chat(
  //   name: "Dr. Olivia Green",
  //   lastMessage: "Your test results came in...",
  //   image: "https://i.postimg.cc/sXC5W1s3/doctor-3.png",
  //   time: "1d ago",
  //   isActive: false,
  // ),
  // Chat(
  //   name: "Dr. Daniel Lewis",
  //   lastMessage: "You’re doing great, keep it up!",
  //   image: "https://i.postimg.cc/4dvVQZxV/doctor-4.png",
  //   time: "2d ago",
  //   isActive: true,
  // ),
  // Chat(
  //   name: "Dr. Sophia Walker",
  //   lastMessage: "Let me know if you have any more questions.",
  //   image: "https://i.postimg.cc/FzDSwZcK/doctor-5.png",
  //   time: "3d ago",
  //   isActive: false,
  // ),
  // Chat(
  //   name: "Dr. Emily Clark",
  //   lastMessage: "Please make sure to take your medication...",
  //   image: "https://i.postimg.cc/g25VYN7X/doctor-1.png",
  //   time: "2m ago",
  //   isActive: true,
  // ),
  // Chat(
  //   name: "Dr. Michael Thompson",
  //   lastMessage: "How are you feeling after the surgery?",
  //   image: "https://i.postimg.cc/cCsYDjvj/doctor-2.png",
  //   time: "15m ago",
  //   isActive: false,
  // ),
  // Chat(
  //   name: "Dr. Olivia Green",
  //   lastMessage: "Your test results came in...",
  //   image: "https://i.postimg.cc/sXC5W1s3/doctor-3.png",
  //   time: "1d ago",
  //   isActive: false,
  // ),
];
