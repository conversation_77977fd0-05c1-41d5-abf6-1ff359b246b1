import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:carerez/utils/screen.dart';
import 'package:carerez/components/bottomNavBar.dart';
import 'package:carerez/components/drawer.dart';
import 'package:carerez/utils/icons.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  bool isDarkMode = false;
  bool isNotificationsEnabled = true;
  bool isLocationEnabled = true;
  String selectedLanguage = 'English';
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      drawer: buildDrawer(scaffoldKey: _scaffoldKey),
      appBar: AppBar(
        backgroundColor: const Color.fromRGBO(90, 38, 101, 1),
        title: const Text(
          "Settings",
          style: TextStyle(color: Colors.white),
        ),
        leading: IconButton(
          icon: const Icon(Icons.menu, color: Colors.white),
          onPressed: () {
            _scaffoldKey.currentState!.openDrawer();
          },
        ),
      ),
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Section
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    bottom: BorderSide(color: Colors.grey.shade200),
                  ),
                ),
                child: Text(
                  "Customize your app settings and preferences",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ),

              const SizedBox(height: 8),

              // Account Settings Section
              _buildSectionHeader("Account Settings"),
              _buildSettingsGroup([
                ProfileMenuCard(
                  icon: Icons.person_outline,
                  title: "Profile Information",
                  subTitle: "Change your account information",
                  press: () {
                    // Navigate to profile edit screen
                  },
                ),
                ProfileMenuCard(
                  icon: Icons.lock_outline,
                  title: "Security",
                  subTitle: "Change password and security settings",
                  press: () {
                    // Navigate to security settings
                  },
                ),
                ProfileMenuCard(
                  icon: Icons.payment_outlined,
                  title: "Payment Methods",
                  subTitle: "Manage your payment options",
                  press: () {
                    // Navigate to payment methods
                  },
                ),
              ]),

              const SizedBox(height: 16),

              // App Preferences Section
              _buildSectionHeader("App Preferences"),
              _buildSettingsGroup([
                SwitchSettingCard(
                  icon: Icons.dark_mode_outlined,
                  title: "Dark Mode",
                  subTitle: "Toggle dark theme",
                  value: isDarkMode,
                  onChanged: (value) {
                    setState(() {
                      isDarkMode = value;
                    });
                    // Implement dark mode logic
                  },
                ),
                SwitchSettingCard(
                  icon: Icons.notifications_outlined,
                  title: "Notifications",
                  subTitle: "Enable push notifications",
                  value: isNotificationsEnabled,
                  onChanged: (value) {
                    setState(() {
                      isNotificationsEnabled = value;
                    });
                    // Implement notifications logic
                  },
                ),
                DropdownSettingCard(
                  icon: Icons.language_outlined,
                  title: "Language",
                  subTitle: "Choose your preferred language",
                  value: selectedLanguage,
                  items: ['English', 'Spanish', 'French', 'German'],
                  onChanged: (value) {
                    setState(() {
                      selectedLanguage = value!;
                    });
                    // Implement language change logic
                  },
                ),
              ]),

              const SizedBox(height: 16),

              // Privacy & Location Section
              _buildSectionHeader("Privacy & Location"),
              _buildSettingsGroup([
                SwitchSettingCard(
                  icon: Icons.location_on_outlined,
                  title: "Location Services",
                  subTitle: "Enable location access",
                  value: isLocationEnabled,
                  onChanged: (value) {
                    setState(() {
                      isLocationEnabled = value;
                    });
                    // Implement location services logic
                  },
                ),
                ProfileMenuCard(
                  icon: Icons.privacy_tip_outlined,
                  title: "Privacy Policy",
                  subTitle: "Read our privacy policy",
                  press: () {
                    // Open privacy policy
                  },
                ),
                ProfileMenuCard(
                  icon: Icons.description_outlined,
                  title: "Terms of Service",
                  subTitle: "Read our terms of service",
                  press: () {
                    // Open terms of service
                  },
                ),
              ]),

              const SizedBox(height: 16),

              // Support & About Section
              _buildSectionHeader("Support & About"),
              _buildSettingsGroup([
                ProfileMenuCard(
                  icon: Icons.help_outline,
                  title: "Help & Support",
                  subTitle: "Get help or contact support",
                  press: () {
                    // Navigate to help center
                  },
                ),
                ProfileMenuCard(
                  icon: Icons.info_outline,
                  title: "About",
                  subTitle: "App version and information",
                  press: () {
                    // Show about dialog
                    _showAboutDialog();
                  },
                ),
              ]),

              const SizedBox(height: 24),

              // Logout Button
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: ElevatedButton(
                  onPressed: () {
                    // Implement logout logic
                    _showLogoutDialog();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red[400],
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 50),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    "Log Out",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
      bottomNavigationBar: const FloatingBottomNavBar(
        selectedIndex: 3,
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Color.fromRGBO(90, 38, 101, 1),
        ),
      ),
    );
  }

  Widget _buildSettingsGroup(List<Widget> children) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: children,
      ),
    );
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("About CareRez"),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text("Version: 1.0.0"),
            const SizedBox(height: 8),
            const Text("© 2024 CareRez. All rights reserved."),
            const SizedBox(height: 16),
            const Text(
              "CareRez is a comprehensive healthcare residence management system designed to streamline operations and enhance patient care.",
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text("Close"),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("Confirm Logout"),
        content: const Text("Are you sure you want to log out?"),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text("Cancel"),
          ),
          TextButton(
            onPressed: () {
              // Implement logout logic here
              Navigator.pop(context);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text("Logout"),
          ),
        ],
      ),
    );
  }
}

class ProfileMenuCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subTitle;
  final VoidCallback press;

  const ProfileMenuCard({
    super.key,
    required this.icon,
    required this.title,
    required this.subTitle,
    required this.press,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: press,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Icon(
              icon,
              size: 24,
              color: const Color.fromRGBO(90, 38, 101, 0.8),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subTitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey,
            ),
          ],
        ),
      ),
    );
  }
}

class SwitchSettingCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subTitle;
  final bool value;
  final ValueChanged<bool> onChanged;

  const SwitchSettingCard({
    super.key,
    required this.icon,
    required this.title,
    required this.subTitle,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Icon(
            icon,
            size: 24,
            color: const Color.fromRGBO(90, 38, 101, 0.8),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subTitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: const Color.fromRGBO(90, 38, 101, 1),
          ),
        ],
      ),
    );
  }
}

class DropdownSettingCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subTitle;
  final String value;
  final List<String> items;
  final ValueChanged<String?> onChanged;

  const DropdownSettingCard({
    super.key,
    required this.icon,
    required this.title,
    required this.subTitle,
    required this.value,
    required this.items,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Icon(
            icon,
            size: 24,
            color: const Color.fromRGBO(90, 38, 101, 0.8),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subTitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          DropdownButton<String>(
            value: value,
            items: items.map((String item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(item),
              );
            }).toList(),
            onChanged: onChanged,
            underline: Container(),
          ),
        ],
      ),
    );
  }
}
