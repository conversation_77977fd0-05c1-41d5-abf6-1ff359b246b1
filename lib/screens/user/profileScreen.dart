import 'package:carerez/components/drawer.dart';
import 'package:carerez/utils/screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
// TODO: add flutter_svg to pubspec.yaml
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:slide_action/slide_action.dart';

import '../../blocs/user/user_bloc.dart';
import '../../blocs/user/user_event.dart';
import '../../blocs/user/user_state.dart';
import '../../routes/router_constants.dart';
import '../../utils/icons.dart';
import 'homeScreen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    context.read<UserBloc>().add(FetchCurrentUser());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      drawer: buildDrawer(scaffoldKey: _scaffoldKey),
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Color.fromRGBO(90, 38, 101, 1),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.menu,
            color: Colors.white,
          ),
          onPressed: () {
            _scaffoldKey.currentState?.openDrawer();
          },
        ),
        actions: [
          //to go back
          IconButton(
            icon: const Icon(
              Icons.arrow_back_ios,
              color: Colors.white,
            ),
            onPressed: () {
              GoRouter.of(context).goNamed(RouteConstants.homescreenRouteName);
            },
          ),
        ],
        title: Row(
          children: [
            //profile icon
            Icon(HugeIcons.strokeRoundedProfile02,
                color: Colors.white, size: 24),
            const SizedBox(width: 8),
             Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Profile',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                ),
                Text(
                  'Manage your profile',
                  style: TextStyle(
                    color: Color(0xFF8E8E93),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      body: BlocBuilder<UserBloc, UserState>(builder: (context, state) {
        final mediaQuery = MediaQuery.of(context);
        final screenWidth = mediaQuery.size.width;
        final screenHeight = mediaQuery.size.height;
        if (state is UserLoaded || state is UserAndTasksLoaded) {
          final user = state is UserLoaded ? state.user : (state as UserAndTasksLoaded).user;
          return Column(
            children: [
              Container(
                width: screenWidth,
                height: screenHeight * 0.25,
                decoration: BoxDecoration(
                  color: Color.fromRGBO(90, 38, 101, 1),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    SizedBox(height: screenHeight * 0.025),
                    ProfilePic(),
                    SizedBox(height: screenHeight * 0.01),
                    Text(
                      user.fullName,
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: screenWidth * 0.05,
                      ),
                    ),
                    SizedBox(height: screenHeight * 0.01),
                    Text(
                      user.userCode,
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: screenWidth * 0.035,
                      ),
                    ),
                  ],
                ),
              ),
              //tab bar
              DefaultTabController(
                length: 3,
                child: Expanded(
                  child: Column(
                    children: [
                      TabBar(
                        indicatorColor: Color.fromRGBO(90, 38, 101, 1),
                        tabs: [
                          Tab(
                            child: Text(
                              'General',
                              style: GoogleFonts.poppins(
                                color: Color.fromRGBO(90, 38, 101, 1),
                                fontSize: 16,
                              ),
                            ),
                          ),
                          Tab(
                            child: Text(
                              'Attendance',
                              style: GoogleFonts.poppins(
                                color: Color.fromRGBO(90, 38, 101, 1),
                                fontSize: 16,
                              ),
                            ),
                          ),
                          Tab(
                            child: Text(
                              'Security',
                              style: GoogleFonts.poppins(
                                color: Color.fromRGBO(90, 38, 101, 1),
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ],
                      ),
                      Expanded(
                        child: TabBarView(
                          physics: const BouncingScrollPhysics(),
                          children: [
                            buildGeneralTab(),
                            buildAttendanceTab(),
                            buildSecurityTab(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        } else if (state is UserLoading) {
          return Center(
            child: CircularProgressIndicator(
              color: Color.fromRGBO(90, 38, 101, 1),
            ),
          );
        } else if (state is UserError) {
          return Center(
            child: Text(
              'Error: ${state.message}',
              style: TextStyle(color: Colors.red),
            ),
          );
        } else {
          return Center(
            child: Text(
              'Unexpected state',
              style: TextStyle(color: Colors.red),
            ),
          );
        }
      }),
    );
  }

  Widget buildGeneralTab() {
    return SingleChildScrollView(
      child: BlocBuilder<UserBloc, UserState>(builder: (context, state) {
        if (state is UserLoading) {
          return Center(
            child: CircularProgressIndicator(
              color: Color.fromRGBO(90, 38, 101, 1),
            ),
          );
        } else if (state is UserError) {
          return Center(
            child: Text(
              'Error: ${state.message}',
              style: TextStyle(color: Colors.red),
            ),
          );
        } else if (state is UserLoaded || state is UserAndTasksLoaded) {
          // Display user information
          final user = state is UserLoaded ? state.user : (state as UserAndTasksLoaded).user;
          return Column(
            children: [
              buildTextFormField('Full Name', user.fullName),
              buildTextFormField('Email', user.emailAddress),
              buildTextFormField('Phone Number', user.phoneNumber),
              buildTextFormField('Address', '${user.physicalAddress}'),
              buildTextFormField('Date of Birth', '01/01/2000'),
            ],
          );
        }
        return Center(
          child: Text(
            'Unexpected state',
            style: TextStyle(color: Colors.red),
          ),
        );
        }
      ),
    );
  }

  Widget buildSecurityTab() {
    return SingleChildScrollView(
      child: Column(
        children: [
          buildTextFormField('Password', '********'),
          buildTextFormField('2FA', 'Enabled'),
          //Button for password Reset
          Container(
            margin: EdgeInsets.all(8),
            height: MediaQuery.of(context).size.height * 0.06,
            padding: EdgeInsets.all(10),
            width: MediaQuery.of(context).size.width * 1,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: Color.fromRGBO(90, 38, 101, 1),
              ),
              color: Colors.white,
            ),
            child: Row(
              children: [
                Text(
                  'Reset Password',
                  style: GoogleFonts.poppins(
                    color: Color.fromRGBO(90, 38, 101, 1),
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Spacer(),
                //arrow icon
                Icon(
                  Icons.arrow_forward_ios,
                  color: Color.fromRGBO(90, 38, 101, 1),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildTextFormField(String label, String value) {
    return Padding(
      padding: EdgeInsets.fromLTRB(20, 10, 20, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              color: Color.fromRGBO(90, 38, 101, 1),
              fontSize: 16,
            ),
          ),
          TextFormField(
            initialValue: value,
            enabled: false,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
          SizedBox(height: MediaQuery.of(context).size.height * 0.01),
          Divider(
            color: Colors.grey,
          )
        ],
      ),
    );
  }
}

Widget buildSlideButton(String text, VoidCallback action, bool isActive) {
  return SlideAction(
    trackHeight: 60,
    actionSnapThreshold: 0.85,
    snapAnimationDuration: const Duration(milliseconds: 300),
    snapAnimationCurve: Curves.easeOut,

    // Background (Track)
    trackBuilder: (context, state) {
      return Container(
        decoration: BoxDecoration(
          color: isActive
              ? Colors.red.shade300
              : Colors.green.shade300, // Toggle color
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 5,
              spreadRadius: 2,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Text(
          text,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: isActive ? Colors.black : Colors.white, // Toggle text color
          ),
        ),
      );
    },

    // Thumb (Sliding button)
    thumbBuilder: (context, state) {
      return Container(
        width: 30,
        height: 30,
        decoration: BoxDecoration(
          color: isActive
              ? Colors.green.shade300
              : Colors.red.shade300, // Toggle thumb color
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 5,
              spreadRadius: 2,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          Icons.arrow_forward_ios,
          color: Colors.white, // Toggle arrow color
        ),
      );
    },

    action: action, // Toggle state when slid
  );
}

class buildAttendanceTab extends StatefulWidget {
  const buildAttendanceTab({super.key});

  @override
  State<buildAttendanceTab> createState() => _buildAttendanceTabState();
}

class _buildAttendanceTabState extends State<buildAttendanceTab> {
  bool isCheckedIn = false;
  bool isOnBreak = false;
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            //button to check attendance history
            Container(
              margin: EdgeInsets.all(8),
              height: MediaQuery.of(context).size.height * 0.06,
              padding: EdgeInsets.all(10),
              width: MediaQuery.of(context).size.width * 1,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: Color.fromRGBO(90, 38, 101, 1),
                ),
                color: Colors.white,
              ),
              child: Row(
                children: [
                  Text(
                    'Attendance History',
                    style: GoogleFonts.poppins(
                      color: Color.fromRGBO(90, 38, 101, 1),
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Spacer(),
                  //arrow icon
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Color.fromRGBO(90, 38, 101, 1),
                  ),
                ],
              ),
            ),

            // Slide Button for Check-In / Check-Out
            buildSlideButton(
              isCheckedIn ? "Slide to Check-Out" : "Slide to Check-In",
              () {
                setState(() {
                  isCheckedIn = !isCheckedIn;
                  print("isCheckedIn: $isCheckedIn");
                });
              },
              isCheckedIn,
            ),

            const SizedBox(height: 16),

            // Slide Button for Break-In / Break-Out
            buildSlideButton(
              isOnBreak ? "Slide to Break-Out" : "Slide to Break-In",
              () {
                setState(() {
                  isOnBreak = !isOnBreak;
                });
              },
              isOnBreak,
            ),
          ],
        ),
      ),
    );
  }
}
