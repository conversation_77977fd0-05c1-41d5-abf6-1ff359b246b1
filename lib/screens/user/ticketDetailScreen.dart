import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

import '../../components/drawer.dart';
import '../../models/ticket.dart';
import '../../models/ticketDetail.dart';
import '../../routes/router_constants.dart';

class TicketDetailScreen extends StatefulWidget {
  final TicketModel ticket;
  const TicketDetailScreen({Key? key, required this.ticket}) : super(key: key);
  @override
  _TicketDetailScreenState createState() => _TicketDetailScreenState();
}

class _TicketDetailScreenState extends State<TicketDetailScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    final ticket = widget.ticket;
    return Scaffold(
      backgroundColor: Colors.white,
      key: _scaffoldKey,
      drawer: buildDrawer(scaffoldKey: _scaffoldKey),
      appBar: AppBar(
        backgroundColor: const Color.fromRGBO(90, 38, 101, 0.9),
        leading: IconButton(
          onPressed: () {
            _scaffoldKey.currentState!.openDrawer();
          },
          icon: const Icon(Icons.menu, color: Colors.white),
        ),
        actions: [
          IconButton(
            onPressed: () {
              GoRouter.of(context).goNamed(RouteConstants.homescreenRouteName);
            },
            icon: const Icon(Icons.arrow_back, color: Colors.white),
          ),
        ],
      ),
      body: Stack(
        children: [
          Container(
            width: double.infinity,
            height: 200,
            decoration: BoxDecoration(
              color: Color.fromRGBO(90, 38, 101, 0.9),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Ticket Details Container
                Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Color(0xFF3B2C4E),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Title",
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold),
                              ),
                              SizedBox(height: 8),
                              Text(
                                ticket.title,
                                style: TextStyle(
                                    color: Colors.white, fontSize: 18),
                              ),
                            ],
                          ),
                          Text(
                            "Date\n${ticket.date.toString().split('T')[0]}",
                            style: TextStyle(
                                color: Colors.white, fontSize: 16),
                            textAlign: TextAlign.end,
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      Text(
                        ticket.description,
                        style: TextStyle(color: Colors.white70),
                      ),
                      SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Added By",
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold),
                              ),
                              SizedBox(height: 8),
                              Text(
                                ticket.addedBy,
                                style: TextStyle(
                                    color: Colors.white, fontSize: 18),
                              ),
                            ],
                          ),
                          Column(
                            children: [
                              Text(
                                "Location",
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold),
                              ),
                              SizedBox(height: 8),
                              Text(
                                ticket.location,
                                style: TextStyle(
                                    color: Colors.white, fontSize: 18),
                                textAlign: TextAlign.end,
                              ),
                            ],
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      Text(
                        "Category",
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text(
                        ticket.category,
                        style: TextStyle(color: Colors.white, fontSize: 18),
                      ),
                      SizedBox(height: 16),
                      // Attachments
                      Text(
                        "Attachments",
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: ticket.attachments != null
                            ? ticket.attachments!.map((attachment) {
                                return Container(
                                  margin: EdgeInsets.only(right: 8),
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    color: Colors.white24,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Center(
                                      child: Text(attachment,
                                          style: TextStyle(
                                              fontSize: 8,
                                              color: Colors.white70))),
                                );
                              }).toList()
                            : [],
                      ),
                      SizedBox(height: 16),
                      Divider(color: Colors.white24),
                      SizedBox(height: 8),
                      // Status Row
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "STATUS",
                            style: TextStyle(
                                color: Colors.white, fontSize: 12),
                          ),
                          Text(
                            ticket.status.toString().split('.').last.toUpperCase(),
                            style: TextStyle(
                              color: Colors.greenAccent,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 32),
                Text("Timeline",
                    style: TextStyle(
                        fontSize: 18, fontWeight: FontWeight.bold)),
                SizedBox(height: 16),
                Expanded(
                  child: ListView.builder(
                    itemCount: ticket.timeline?.length ?? 0,
                    itemBuilder: (context, index) {
                      final timelineEvent = ticket.timeline != null
                          ? ticket.timeline!.reversed.toList()[index]
                          : null;
                      if (timelineEvent == null) return SizedBox.shrink();
                      return TimelineTile(
                        date: timelineEvent.date,
                        description: timelineEvent.description,
                        isFirst: index == 0,
                        isLast: index == (ticket.timeline?.length ?? 1) - 1,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class TimelineTile extends StatelessWidget {
  final String date;
  final String description;
  final bool isFirst;
  final bool isLast;

  const TimelineTile({
    required this.date,
    required this.description,
    this.isFirst = false,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            if (!isFirst) Container(height: 20, width: 2, color: Colors.grey),
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: Colors.blue,
                shape: BoxShape.circle,
              ),
            ),
            if (!isLast) Container(height: 20, width: 2, color: Colors.grey),
          ],
        ),
        SizedBox(width: 8),
        Expanded(
          child: Container(
            margin: EdgeInsets.only(bottom: 16),
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Color(0xFF3B2C4E),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(date, style: TextStyle(color: Colors.white, fontSize: 12)),
                SizedBox(height: 4),
                Text(description, style: TextStyle(color: Colors.white)),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
