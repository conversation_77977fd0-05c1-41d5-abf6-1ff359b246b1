import 'package:carerez/components/drawer.dart';
import 'package:carerez/blocs/auth/auth_bloc.dart';
import 'package:carerez/blocs/auth/auth_event.dart';
import 'package:carerez/models/staff_task.dart';
import 'package:carerez/utils/screen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

import '../../components/bottomNavBar.dart';
import '../../dialogs/logout.dart';
import '../../routes/router_constants.dart';
import '../../utils/icons.dart';
import 'package:carerez/blocs/user/user_bloc.dart';
import 'package:carerez/blocs/user/user_event.dart';
import 'package:carerez/blocs/user/user_state.dart';
import 'package:carerez/models/user_model.dart';

class UserHomescreen extends StatefulWidget {
  const UserHomescreen({super.key});

  @override
  State<UserHomescreen> createState() => _UserHomescreenState();
}

class _UserHomescreenState extends State<UserHomescreen> {
  final TextEditingController _startLocationController =
  TextEditingController();
  final TextEditingController _endLocationController = TextEditingController();

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    // Fetch user data when the screen initializes
    context.read<UserBloc>().add(FetchCurrentUser());
  }


  @override
  void dispose() {
    // No need to access BlocProvider here
    _startLocationController.dispose();
    _endLocationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    SizeConfig().init(context);
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.white,
      appBar: PreferredSize(
        preferredSize: SizeConfig.screenH! > 800
            ? const Size.fromHeight(250)
            : const Size.fromHeight(200),
        child: AppBar(
          toolbarHeight: SizeConfig.screenH! > 800 ? 250 : 200,
          backgroundColor: Color.fromRGBO(90, 38, 101, 1),
          title: Center(
            child: BlocBuilder<UserBloc, UserState>(
              builder: (context, state) {
                if (state is UserLoaded || state is UserAndTasksLoaded) {
                  final user = state is UserLoaded ? state.user : (state as UserAndTasksLoaded).user;
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        'Welcome',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                        ),
                      ),
                      SizedBox(height: SizeConfig.screenH! > 800 ? 20 : 10),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ProfilePic(),
                          SizedBox(width: SizeConfig.screenW! > 800 ? 20 : 10),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                user.fullName.isNotEmpty
                                    ? user.fullName
                                    : (user.tenantName != null && user.tenantName!.isNotEmpty
                                        ? user.tenantName
                                        : 'User').toString(),
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                ),
                              ),
                              SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Text(
                                  user.roles.isNotEmpty?
                                    user.roles.first:
                                    'Tenant Admin',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 15,
                                  ),
                                ),
                              )
                            ],
                          ),
                        ],
                      ),
                    ],
                  );
                } else if (state is UserLoading) {
                  return const Center(child: CircularProgressIndicator(color: Colors.white));
                } else {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        'Loading...',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                        ),
                      ),
                      const Text(
                        'Please wait',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                        ),
                      )
                    ],
                  );
                }
              },
            ),
          ),
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(
              bottom: Radius.circular(50),
            ),
          ),
          leading: const SizedBox.shrink(), // Removes default leading space
          actions: [
            Column(
              children: [
                IconButton(
                  icon: Icon(Icons.logout, color: Colors.white),
                  onPressed: () => showLogoutDialog(context),
                ),
              ],
            ),
          ],
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.symmetric(
              vertical: SizeConfig.screenH! > 800 ? 20 : 10),
          child: Column(
            children: [
              SizedBox(height: SizeConfig.screenH! > 800 ? 20 : 10),
              ProfileMenu(
                text: "Profile",
                subtitle: "Update your profile",
                icon: Icons.person,
                press: () => {
                  GoRouter.of(context).goNamed(RouteConstants.profileRouteName),
                },
              ),
              ProfileMenu(
                text: "Units",
                subtitle: "View all units",
                icon: Icons.storefront_rounded,
                press: () {
                  GoRouter.of(context).goNamed(RouteConstants.unitsRouteName);
                },
              ),
              ProfileMenu(
                text: "Residents",
                subtitle: "View all residents",
                icon: Icons.people_outline,
                press: () {
                  GoRouter.of(context)
                      .goNamed(RouteConstants.residentsRouteName);
                },
              ),
              ProfileMenu(
                text: "Staff",
                subtitle: "View status of other staff working",
                icon: Icons.people_outline_outlined,
                press: () {
                  GoRouter.of(context).goNamed(RouteConstants.staffsRouteName);
                },
              ),
              ProfileMenu(
                text: "Incident Report",
                subtitle: "Create/View incident reports",
                icon: Icons.bug_report_outlined,
                press: () {
                  GoRouter.of(context)
                      .goNamed(RouteConstants.incidentsRouteName);
                },
              ),
              ProfileMenu(
                text: "Complaints",
                subtitle: "View all complaints",
                icon: Icons.report_problem_outlined,
                press: () {
                  GoRouter.of(context).goNamed(RouteConstants.complaintsRouteName);
                },
              ),
              ProfileMenu(
                text: "Hand Off Notes",
                subtitle: "View Notes Left by Other Nurses",
                icon: Icons.note,
                press: () {
                  GoRouter.of(context).goNamed(RouteConstants.notesRouteName);
                },
              ),
              ProfileMenu(
                text: "Service Requests",
                subtitle: "View and Manage Tickets",
                icon: CupertinoIcons.ticket,
                press: () {
                  GoRouter.of(context).goNamed(RouteConstants.ticketsRouteName);
                },
              ),

              BlocBuilder<UserBloc, UserState>(
                builder: (context, state) {
                  if (state is UserLoaded) {
                    // Only fetch staff tasks if user is not TenantAdmin
                    String userType = '';
                    if (state.user.roles != null && state.user.roles.isNotEmpty) {
                      userType = state.user.roles[0].split(':')[0];
                    } else if (state.user.tenantName != null) {
                      userType = 'TenantAdmin';
                    }
                    if (userType != 'TenantAdmin' && userType != '') {
                      context.read<UserBloc>().add(FetchStaffTasks(state.user.id));
                    }
                  }
                  if (state is UserAndTasksLoaded) {
                    final tasks = state.tasks;
                    if (tasks.isEmpty) {
                      return const Center(child: Text('No active tasks'));
                    }
                    return SizedBox(
                      height: SizeConfig.screenH! > 800 ? 100 : 80,
                      child: ListView.builder(
                        padding: EdgeInsets.zero,
                        itemCount: tasks.length,
                        shrinkWrap: true,
                        scrollDirection: Axis.horizontal,
                        itemBuilder: (context, index) {
                          final task = tasks[index];
                          return ActiveTaskBar(
                            title: task.task,
                            imageUrl: task.assignedBy?.userDetails?.profileUrl != null
                                ? task.assignedBy!.userDetails!.profileUrl!
                                : "https://i.postimg.cc/0jqKB6mS/Profile-Image.png",
                            subtitle: task.date.toIso8601String().substring(0, 10),
                          );
                        }),
                    );
                  } else if (state is UserLoading) {
                    return const Center(child: CircularProgressIndicator());
                  } else {
                    return const SizedBox.shrink();
                  }
                },
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: FloatingBottomNavBar(),
    );
  }
}

class ProfilePic extends StatelessWidget {
  const ProfilePic({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, state) {
        String? profileImageUrl;

        if (state is UserLoaded &&
            state.user.companyInfo?.profileImageURL != null) {
          profileImageUrl = state.user.companyInfo!.profileImageURL;
        }

        return SizedBox(
          height: SizeConfig.screenH! > 800 ? 135 : 115,
          width: SizeConfig.screenW! > 800 ? 135 : 115,
          child: CircleAvatar(
            backgroundImage: profileImageUrl != null
                ? NetworkImage(profileImageUrl)
                : const NetworkImage(
                "https://i.postimg.cc/0jqKB6mS/Profile-Image.png"),
          ),
        );
      },
    );
  }
}

class ProfileMenu extends StatelessWidget {
  const ProfileMenu({
    super.key,
    required this.text,
    required this.icon,
    required this.subtitle,
    this.press,
  });

  final String text;
  final String subtitle;
  final IconData icon;
  final VoidCallback? press;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: SizeConfig.screenW! * 0.04,
          vertical: SizeConfig.screenH! * 0.01),
      child: TextButton(
        style: TextButton.styleFrom(
          foregroundColor: Color.fromRGBO(90, 38, 101, 1),
          padding: EdgeInsets.all(20),
          shape:
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
          backgroundColor: const Color(0xFFF5F6F9),
        ),
        onPressed: press,
        child: Row(
          children: [
            Icon(
              icon as IconData?,
              size: 32,
              color: const Color(0xFF757575),
            ),
            SizedBox(width: SizeConfig.screenW! * 0.04),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    text,
                    style: const TextStyle(
                      color: Color(0xFF757575),
                    ),
                  ),
                  SizedBox(height: SizeConfig.screenH! * 0.01),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFFA1A1A1),
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              color: Color(0xFF757575),
            ),
          ],
        ),
      ),
    );
  }
}

class ActiveTaskBar extends StatelessWidget {
  final String imageUrl;
  final String title;
  final String subtitle;

  const ActiveTaskBar({
    super.key,
    required this.imageUrl,
    required this.title,
    required this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: SizeConfig.screenH! > 800 ? 100 : 100,
      width: SizeConfig.screenW! > 800 ? 300 : 250,
      padding: EdgeInsets.symmetric(
          horizontal: SizeConfig.screenW! * 0.04,
          vertical: SizeConfig.screenH! * 0.01),
      decoration: BoxDecoration(
        color: Color.fromRGBO(245, 245, 245, 0.9),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          CircleAvatar(
            radius: 30,
            backgroundImage: NetworkImage(imageUrl),
          ),
          SizedBox(width: SizeConfig.screenW! * 0.04),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  subtitle,
                  style: const TextStyle(
                    color: Color(0xFF757575),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: SizeConfig.screenH! * 0.01),
                Text(
                  title,
                  style: const TextStyle(
                    color: Color(0xFFA1A1A1),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}