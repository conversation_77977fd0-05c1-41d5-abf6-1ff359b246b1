import 'package:carerez/components/bottomNavBar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../components/drawer.dart';
import '../../blocs/notification/notification_bloc.dart';
import '../../blocs/notification/notification_event.dart';
import '../../blocs/notification/notification_state.dart';
import '../../screens/utils/emptyNotifications.dart';

class NotificationsScreen extends StatefulWidget {
  NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    context.read<NotificationBloc>().add(FetchNotifications());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.white,
      drawer: buildDrawer(scaffoldKey: _scaffoldKey),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.menu, color: Colors.black),
          onPressed: () => _scaffoldKey.currentState!.openDrawer(),
        ),
        title: const Text("Notifications"),
        actions: [
          IconButton(
            icon: const Icon(Icons.clear_all, color: Colors.black),
            onPressed: () {
              context.read<NotificationBloc>().add(ClearAllNotifications());
            },
          ),
        ],
      ),
      body: BlocBuilder<NotificationBloc, NotificationState>(
        builder: (context, state) {
          if (state is NotificationLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          
          if (state is NotificationError) {
            return Center(child: Text(state.message));
          }
          
          if (state is NotificationLoaded) {
            if (state.notifications.isEmpty) {
              return const EmptyNotificationsScreen();
            }
            
            return SafeArea(
              child: ListView.builder(
                itemCount: state.notifications.length,
                itemBuilder: (context, index) => NotificationCard(
                  notification: state.notifications[index],
                  press: () {
                    context.read<NotificationBloc>().add(
                      MarkNotificationAsRead(state.notifications[index].id),
                    );
                  },
                ),
              ),
            );
          }
          
          return const SizedBox();
        },
      ),
      bottomNavigationBar: const FloatingBottomNavBar(selectedIndex: 4),
    );
  }

  Widget NotificationCard({notification, press}) {
    return GestureDetector(
      onTap: press,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: Material(
          elevation: 4,
          borderRadius: BorderRadius.circular(15),
          color: Colors.white, // Background color for the card
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              color: Colors.white, // Card background color
            ),
            child: ListTile(
              contentPadding:
                  EdgeInsets.all(12), // More spacing for a cleaner look
              leading: CircleAvatar(
                radius: 30, // Larger avatar for a modern look
                backgroundImage: NetworkImage(notification["image"]),
              ),
              title: Text(
                notification["title"],
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16, // Slightly bigger font for the title
                  color: Colors.black87, // Modern dark text color
                ),
              ),
              subtitle: Padding(
                padding: const EdgeInsets.only(top: 4.0),
                child: Text(
                  notification["description"],
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black54, // Subtle color for the description
                  ),
                ),
              ),
              trailing: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    notification["time"],
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600], // Time text in a subtle color
                    ),
                  ),
                  // Add an icon for a modern feel
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

const notificationData = [
  {
    "title": "Patient Alert",
    "description": "Patient in Room 101 needs assistance",
    "time": "5m ago",
    "image": "https://i.postimg.cc/g25VYN7X/doctor-1.png",
  },
  {
    "title": "New Task",
    "description": "Check vitals for Patient in Room 203",
    "time": "10m ago",
    "image": "https://i.postimg.cc/g25VYN7X/doctor-5.png",
  },
  {
    "title": "Medication Reminder",
    "description": "Administer medication to Patient in Room 305",
    "time": "15m ago",
    "image": "https://i.postimg.cc/g25VYN7X/doctor-4.png",
  },
  {
    "title": "Shift Update",
    "description": "Your next shift starts in 30 minutes",
    "time": "30m ago",
    "image": "https://i.postimg.cc/g25VYN7X/doctor-3.png",
  }
];

