import 'package:carerez/utils/screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:go_router/go_router.dart';
import '../../models/splashData.dart';
import '../../routes/router_constants.dart';
import '../../blocs/auth/auth_bloc.dart';
import '../../blocs/auth/auth_state.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  late AnimationController _backgroundController;
  late Animation<double> _backgroundAnimation;

  @override
  void initState() {
    super.initState();
    _backgroundController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 8),
    )..repeat();
    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_backgroundController);
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    super.dispose();
  }

  void _checkAuthStatus() {
    final authState = context.read<AuthBloc>().state;

    if (authState is AuthAuthenticated) {
      context.goNamed(RouteConstants.homescreenRouteName);
    } else {
      context.goNamed(RouteConstants.signInRouteName);
    }
  }

  void _onFinishSplash() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool('hasSeenSplash', true);
    _checkAuthStatus();
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final screenHeight = mediaQuery.size.height;
    SizeConfig().init(context);
    return Scaffold(
      body: AnimatedBuilder(
        animation: _backgroundAnimation,
        builder: (context, child) {
          return Container(
            height: double.infinity,
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.topLeft,
                radius: 1.5 + 0.3 * _backgroundAnimation.value,
                colors: [
                  Color(0xFF667eea),
                  Color(0xFF764ba2),
                  Color(0xFF1e3c72),
                  Color(0xFF2a5298),
                ],
                stops: [
                  0.0,
                  0.3 + 0.1 * _backgroundAnimation.value,
                  0.7 + 0.1 * _backgroundAnimation.value,
                  1.0,
                ],
              ),
            ),
            child: SafeArea(
              child: Column(
                children: [
                  Expanded(
                    flex: 4,
                    child: PageView.builder(
                      controller: _pageController,
                      onPageChanged: (index) {
                        setState(() {
                          _currentPage = index;
                        });
                      },
                      itemCount: SplashData.splashData.length,
                      itemBuilder: (context, index) => SplashContent(
                        text: SplashData.splashData[index]["text"]!,
                        image: SplashData.splashData[index]["image"]!,
                        pageIndex: index,
                      ),
                    ),
                  ),
                  _currentPage == SplashData.splashData.length - 1
                      ? Expanded(
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                                vertical: screenHeight * 0.025,
                                horizontal: screenWidth * 0.06),
                            child: Column(
                              children: [
                                const Spacer(),
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.15),
                                        blurRadius: 20,
                                        offset: Offset(0, screenHeight * 0.012),
                                      ),
                                    ],
                                  ),
                                  child: ElevatedButton(
                                    onPressed: _onFinishSplash,
                                    style: ElevatedButton.styleFrom(
                                      elevation: 0,
                                      backgroundColor: Colors.white,
                                      foregroundColor: const Color(0xFF667eea),
                                      minimumSize: Size(
                                          double.infinity, screenHeight * 0.07),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          "Get Started",
                                          style: GoogleFonts.inter(
                                            fontSize: screenWidth * 0.045,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                        SizedBox(width: screenWidth * 0.02),
                                        const Icon(Icons.arrow_forward_rounded),
                                      ],
                                    ),
                                  ),
                                ),
                                SizedBox(height: screenHeight * 0.025),
                                Text(
                                  "Swipe left to explore more",
                                  style: GoogleFonts.inter(
                                    fontSize: screenWidth * 0.035,
                                    color: Colors.white.withOpacity(0.7),
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                const Spacer(),
                              ],
                            ),
                          ),
                        )
                      : Expanded(
                          child: Container(
                            padding: EdgeInsets.symmetric(
                                vertical: screenHeight * 0.05),
                            child: Column(
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: List.generate(
                                    SplashData.splashData.length,
                                    (index) => buildModernDot(
                                        index: index,
                                        screenWidth: screenWidth,
                                        screenHeight: screenHeight),
                                  ),
                                ),
                                SizedBox(height: screenHeight * 0.03),
                                Text(
                                  "Swipe to continue",
                                  style: GoogleFonts.inter(
                                    fontSize: screenWidth * 0.035,
                                    color: Colors.white.withOpacity(0.7),
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget buildModernDot(
      {required int index,
      required double screenWidth,
      required double screenHeight}) {
    bool isActive = _currentPage == index;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 400),
      curve: Curves.easeInOut,
      margin: EdgeInsets.symmetric(horizontal: screenWidth * 0.01),
      height: screenHeight * 0.01,
      width: isActive ? screenWidth * 0.06 : screenWidth * 0.02,
      decoration: BoxDecoration(
        color: isActive ? Colors.white : Colors.white.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        boxShadow: isActive
            ? [
                BoxShadow(
                  color: Colors.white.withOpacity(0.3),
                  blurRadius: 8,
                  offset: Offset(0, screenHeight * 0.002),
                ),
              ]
            : [],
      ),
    );
  }
}

class SplashContent extends StatefulWidget {
  final String text;
  final String image;
  final int pageIndex;

  const SplashContent({
    Key? key,
    required this.text,
    required this.image,
    required this.pageIndex,
  }) : super(key: key);

  @override
  State<SplashContent> createState() => _SplashContentState();
}

class _SplashContentState extends State<SplashContent>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late AnimationController _floatingController;
  late Animation<double> _imageScaleAnim;
  late Animation<double> _fadeAnim;
  late Animation<Offset> _slideAnim;
  late Animation<double> _floatingAnim;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _floatingController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    )..repeat(reverse: true);

    _imageScaleAnim = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _fadeAnim = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnim = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.2, 0.8, curve: Curves.easeOut),
    ));

    _floatingAnim = Tween<double>(
      begin: -8.0,
      end: 8.0,
    ).animate(CurvedAnimation(
      parent: _floatingController,
      curve: Curves.easeInOut,
    ));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    _floatingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          const SizedBox(height: 40),

          // App Title with modern styling
          FadeTransition(
            opacity: _fadeAnim,
            child: SlideTransition(
              position: _slideAnim,
              child: ShaderMask(
                shaderCallback: (bounds) => const LinearGradient(
                  colors: [Colors.white, Colors.white70],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ).createShader(bounds),
                child: Text(
                  "CareRez",
                  style: GoogleFonts.poppins(
                    fontSize: 42,
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                    letterSpacing: 1.5,
                    height: 1.2,
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Subtitle text
          FadeTransition(
            opacity: _fadeAnim,
            child: SlideTransition(
              position: _slideAnim,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  widget.text,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    color: Colors.white.withOpacity(0.9),
                    fontWeight: FontWeight.w400,
                    height: 1.5,
                  ),
                ),
              ),
            ),
          ),

          const Spacer(),

          // Modern image container with floating animation
          AnimatedBuilder(
            animation: _floatingAnim,
            builder: (context, child) {
              return Transform.translate(
                  offset: Offset(0, _floatingAnim.value),
                  child: ScaleTransition(
                    scale: _imageScaleAnim,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(32),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 30,
                            offset: const Offset(0, 15),
                          ),
                          BoxShadow(
                            color: Colors.white.withOpacity(0.1),
                            blurRadius: 20,
                            offset: const Offset(0, -5),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(32),
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Colors.white.withOpacity(0.1),
                                Colors.transparent,
                              ],
                            ),
                          ),
                          child: Stack(
                            children: [
                              Image.asset(
                                widget.image,
                                fit: BoxFit.cover,
                                width: MediaQuery.of(context).size.width * 0.75,
                                height:
                                    MediaQuery.of(context).size.height * 0.32,
                              ),
                              // Glassmorphic overlay
                              Container(
                                width: MediaQuery.of(context).size.width * 0.75,
                                height:
                                    MediaQuery.of(context).size.height * 0.32,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(32),
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      Colors.white.withOpacity(0.1),
                                      Colors.transparent,
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ));
            },
          ),

          const SizedBox(height: 60),
        ],
      ),
    );
  }
}
