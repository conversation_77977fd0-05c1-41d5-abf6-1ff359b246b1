import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hugeicons/hugeicons.dart';

import '../../../components/drawer.dart';
import '../../../routes/router_constants.dart';
import '../../../blocs/resident/resident_bloc.dart';
import '../../../blocs/resident/resident_event.dart';
import '../../../blocs/resident/resident_state.dart';

class ResidentMedicalHistory extends StatefulWidget {
  final String residentId;
  final String residentUUID;
  const ResidentMedicalHistory({super.key, required this.residentId, required this.residentUUID});

  @override
  State<ResidentMedicalHistory> createState() => _ResidentMedicalHistoryState();
}

class _ResidentMedicalHistoryState extends State<ResidentMedicalHistory> {
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    // Fetch medical history when the screen loads
    context
        .read<ResidentBloc>()
        .add(FetchResidentMedicalHistory(widget.residentUUID));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      drawer: buildDrawer(scaffoldKey: _scaffoldKey),
      appBar: AppBar(
        backgroundColor: Colors.white,
        leading: IconButton(
          icon: Icon(HugeIcons.strokeRoundedMenu01),
          color: Colors.black,
          onPressed: () {
            _scaffoldKey.currentState!.openDrawer();
          },
        ),
        title: const Text(
          'Resident Medical History',
          style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.arrow_back),
            color: Colors.black,
            onPressed: () {
              GoRouter.of(context).goNamed(
                RouteConstants.residentPersonalRouteName,
                pathParameters: {'residentId': widget.residentId,'residentUUID': widget.residentUUID},
              );
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Container(
          color: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          child: BlocBuilder<ResidentBloc, ResidentState>(
            builder: (context, state) {
              if (state is ResidentLoading) {
                return const Center(child: CircularProgressIndicator());
              } else if (state is ResidentMedicalHistoryLoaded) {
                final history = state.medicalHistory;
                if (history.isEmpty) {
                  return const Center(child: Text('No medical history found.'));
                }
                return ListView.builder(
                  itemCount: history.length,
                  itemBuilder: (context, index) {
                    final medication = history[index];
                    return InkWell(
                      onTap: ()=> GoRouter.of(context).pushNamed(
                        RouteConstants.residentMedicalHistoryDetailsRouteName, extra: medication,
                        pathParameters: {'residentId': widget.residentId},
                      ),
                      child: Card(
                        color: Colors.white,
                        margin: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        elevation: 4,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16)),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      color: Colors.deepPurple.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    padding: const EdgeInsets.all(8),
                                    child: const Icon(Icons.medical_services, color: Colors.deepPurple, size: 28),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          medication.treatmentName,
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.deepPurple,
                                            fontSize: 18,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        const SizedBox(height: 2),
                                        Text(
                                          'Doctor: ${medication.doctorName}',
                                          style: const TextStyle(fontSize: 13, color: Colors.black87),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Chip(
                                    label: Text(
                                      medication.isActive ? 'Active' : 'Inactive',
                                      style: const TextStyle(color: Colors.white, fontSize: 12),
                                    ),
                                    backgroundColor: medication.isActive ? Colors.green : Colors.red,
                                    padding: EdgeInsets.zero,
                                  ),
                                ],
                              ),
                              const SizedBox(height: 10),
                              Text(
                                medication.treatmentDescription,
                                style: const TextStyle(fontSize: 15, color: Colors.black54),
                                maxLines: 3,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 12),
                              Row(
                                children: [
                                  const Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                                  const SizedBox(width: 4),
                                  Text(
                                    'Date: ${medication.treatmentDate.toLocal().toString().split(' ')[0]}',
                                    style: const TextStyle(fontSize: 13, color: Colors.black87),
                                  ),
                                  const SizedBox(width: 16),
                                  const Icon(Icons.access_time, size: 16, color: Colors.grey),
                                  const SizedBox(width: 4),
                                  Text(
                                    'Created: ${medication.createdAt.toLocal().toString().split(' ')[0]}',
                                    style: const TextStyle(fontSize: 13, color: Colors.black87),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 10),
                              Row(
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      color: Colors.blue.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    child: Row(
                                      children: [
                                        const Icon(Icons.repeat, size: 16, color: Colors.blue),
                                        const SizedBox(width: 4),
                                        Text('Follow-ups: ${medication.followups.length}', style: const TextStyle(fontSize: 13, color: Colors.blue)),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: Colors.orange.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    child: Row(
                                      children: [
                                        const Icon(Icons.attachment, size: 16, color: Colors.orange),
                                        const SizedBox(width: 4),
                                        Text('Attachments: ${medication.attachments.length}', style: const TextStyle(fontSize: 13, color: Colors.orange)),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                );
              } else if (state is ResidentError) {
                return Center(child: Text('Error: ${state.message}'));
              } else {
                return const Center(child: Text('No data available.'));
              }
            },
          ),
        ),
      ),
    );
  }
}
