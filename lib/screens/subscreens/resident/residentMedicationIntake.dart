import 'dart:convert';

import 'package:carerez/services/http_client.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:intl/intl.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../components/drawer.dart';
import '../../../models/mar_sheet_model.dart';
import '../../../routes/router_constants.dart';
import '../../../utils/screen.dart';
import '../../../../blocs/mar_sheet/mar_sheet_bloc.dart';
import '../../../../blocs/mar_sheet/mar_sheet_event.dart';
import '../../../../blocs/mar_sheet/mar_sheet_state.dart';

class ResidentMedicationIntake extends StatefulWidget {
  final String residentUUID;
  final String residentId;

  const ResidentMedicationIntake({
    super.key,
    required this.residentId,
    required this.residentUUID
  });

  @override
  State<ResidentMedicationIntake> createState() => _ResidentMedicationIntakeState();
}

class _ResidentMedicationIntakeState extends State<ResidentMedicationIntake> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final TextEditingController _notesController = TextEditingController();

  // Form state variables
  String? selectedMedicine;
  String? selectedPlace;
  TimeOfDay? selectedTime;
  String? notes;
  String? selectedMood;
  String? selectedStatus;
  bool showNotesInput = false;
  bool isSubmitting = false;

  // Status options
  List<Map<String, String>> statusOptions = [];
  bool isLoadingStatuses = false;

  // Selected medication objects
  Medication? selectedMedication;
  AssignMedicineSelectTime? selectedTimeDrop;

  @override
  void initState() {
    super.initState();
    _fetchStatusOptions();
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  // Fetch status options from API
  Future<void> _fetchStatusOptions() async {
    setState(() {
      isLoadingStatuses = true;
    });

    try {
      final response = await HttpClientService.get(
          'http://13.201.148.227:8002/mar-sheet-status/view_all');
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body)['data'];
        setState(() {
          statusOptions = data
              .map<Map<String, String>>((item) => {
            'code': item['symbol'] as String,
            'label': item['name'] as String,
            'id': item['marSheetStatusId'] as String,
          })
              .toList();
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error fetching status options: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        isLoadingStatuses = false;
      });
    }
  }

  // Clear form data
  void _clearForm() {
    setState(() {
      selectedMedicine = null;
      selectedPlace = null;
      selectedTime = null;
      selectedStatus = null;
      selectedMood = null;
      notes = null;
      showNotesInput = false;
      selectedMedication = null;
      selectedTimeDrop = null;
      _notesController.clear();
    });
  }

  // Update selected medication objects when selections change
  void _updateSelectedObjects(List<Medication> medicines) {
    if (selectedMedicine != null) {
      selectedMedication = medicines.firstWhere(
            (e) => e.medicineName == selectedMedicine,
        orElse: () => medicines.isNotEmpty ? medicines[0] : medicines.first,
      );

      if (selectedPlace != null && selectedMedication != null) {
        final timeDrops = selectedMedication!.assignMedicineSelectTime;
        if (timeDrops.isNotEmpty) {
          try {
            selectedTimeDrop = timeDrops.firstWhere((e) => e.name == selectedPlace);
          } catch (_) {
            selectedTimeDrop = timeDrops.isNotEmpty ? timeDrops[0] : null;
          }
        }
      }
    }
  }

  // Handle form submission
  // Future<void> _handleSubmit() async {
  //   final bloc = context.read<MarSheetBloc>();
  //   final state = bloc.state;
  //   if (state is! MarSheetLoaded) return;
  //
  //   final medicines = state.medications;
  //   _updateSelectedObjects(medicines);
  //
  //   // Validation
  //   if (selectedMedication == null || selectedTimeDrop == null || selectedTime == null) {
  //     ScaffoldMessenger.of(context).showSnackBar(
  //       const SnackBar(
  //         content: Text('Please complete all required fields.'),
  //         backgroundColor: Colors.orange,
  //       ),
  //     );
  //     return;
  //   }
  //
  //   final marSheetEntry = {
  //     "marSheetStatusId": selectedStatus,
  //     "timeOfInTake":
  //     "${selectedTime!.hour.toString().padLeft(2, '0')}:${selectedTime!.minute.toString().padLeft(2, '0')}:00",
  //     "dateOfInTake": DateFormat('yyyy-MM-dd').format(DateTime.now()),
  //     "amountTaken": selectedTimeDrop!.dosage,
  //     "note": notes ?? "",
  //     "assignMedicineId": selectedMedication!.assignMedicineId,
  //     "residentId": widget.residentUUID,
  //     "assignMedicineSelectTimeId": selectedTimeDrop!.selectTimeId,
  //     "medicineId": selectedMedication!.medicineId,
  //   };
  //
  //   debugPrint("MarSheet Entry: $marSheetEntry");
  // }

  void _handleSubmit() {
    if (selectedMedicine == null || selectedPlace == null || selectedTime == null) {
      debugPrint('Form is incomplete');
      return;
    }

    final bloc = context.read<MarSheetBloc>();
    final marSheetEntry = {
        "marSheetStatusId": selectedStatus,
        "timeOfInTake":
        "${selectedTime!.hour.toString().padLeft(2, '0')}:${selectedTime!.minute.toString().padLeft(2, '0')}:00",
        "dateOfInTake": DateFormat('yyyy-MM-dd').format(DateTime.now()),
        "amountTaken": selectedTimeDrop!.dosage,
        "note": notes ?? "",
        "assignMedicineId": selectedMedication!.assignMedicineId,
        "residentId": widget.residentUUID,
        "selectTimeId": selectedTimeDrop!.selectTimeId,
        "medicineId": selectedMedication!.medicineId,
      };
    debugPrint("MarSheet Entry: $marSheetEntry");

    bloc.add(AddMarSheetEntry(
      entry: marSheetEntry,
      residentUUID: widget.residentUUID,
      residentId: widget.residentId,
      startDate: DateTime.now(),
      endDate: DateTime.now()
    ));

    debugPrint('SubmitMarSheet event dispatched');
  }

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final startDate = DateTime(now.year, now.month, now.day);
    final endDate = startDate.add(Duration(days: 6));

    return BlocProvider(
      create: (_) => MarSheetBloc()
        ..add(FetchMarSheet(
          residentUUID: widget.residentUUID,
          startDate: startDate,
          endDate: endDate,
        )),
      child: BlocListener<MarSheetBloc, MarSheetState>(
        listener: (context, state) {
          debugPrint('MarSheetBloc State Changed: ${state.runtimeType}');

          if (state is MarSheetSubmitting) {
            setState(() {
              isSubmitting = true;
            });
          } else if (state is MarSheetSubmissionSuccess) {
            setState(() {
              isSubmitting = false;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Medication intake recorded successfully!'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 3),
              ),
            );
            _clearForm();
          } else if (state is MarSheetSubmissionError) {
            setState(() {
              isSubmitting = false;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error recording medication intake: ${state.message}'),
                backgroundColor: Colors.red,
                duration: Duration(seconds: 5),
              ),
            );
            debugPrint('Submission Error: ${state.message}');
          }
        },
        child: BlocBuilder<MarSheetBloc, MarSheetState>(
          builder: (context, state) {
            return Scaffold(
              backgroundColor: const Color(0xFFF8F9FA),
              key: _scaffoldKey,
              drawer: buildDrawer(scaffoldKey: _scaffoldKey),
              appBar: _buildAppBar(),
              body: _buildBody(state),
            );
          },
        ),
      ),
    );
  }

  // Build AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: const Color.fromRGBO(90, 38, 101, 1),
      leading: IconButton(
        onPressed: () {
          _scaffoldKey.currentState!.openDrawer();
        },
        icon: const Icon(HugeIcons.strokeRoundedMenu01, color: Colors.white),
      ),
      title: const Text(
        'Medication Intake',
        style: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          onPressed: () {
            GoRouter.of(context).goNamed(RouteConstants.homescreenRouteName);
          },
          icon: const Icon(Icons.home, color: Colors.white),
        ),
      ],
    );
  }

  // Build main body based on state
  Widget _buildBody(MarSheetState state) {
    if (state is MarSheetLoading) {
      return _buildLoadingWidget();
    } else if (state is MarSheetLoaded) {
      final medicines = state.medications;
      if (medicines.isEmpty) {
        return _buildEmptyStateWidget();
      }
      _updateSelectedObjects(medicines);
      return _buildFormWidget(medicines);
    } else if (state is MarSheetError) {
      return _buildErrorWidget(state.message);
    }
    return const SizedBox.shrink();
  }

  // Loading widget
  Widget _buildLoadingWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color.fromRGBO(90, 38, 101, 1)),
          ),
          SizedBox(height: 16),
          Text(
            'Loading medication data...',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  // Empty state widget
  Widget _buildEmptyStateWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.info_outline, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'No medication records found for this resident.',
            style: TextStyle(fontSize: 16, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Error widget
  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Error: $message',
            style: const TextStyle(fontSize: 16, color: Colors.red),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              final now = DateTime.now();
              final startDate = DateTime(now.year, now.month, now.day);
              final endDate = startDate.add(Duration(days: 6));
              context.read<MarSheetBloc>().add(FetchMarSheet(
                residentUUID: widget.residentUUID,
                startDate: startDate,
                endDate: endDate,
              ));
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  // Main form widget
  Widget _buildFormWidget(List<Medication> medicines) {
    final medicineNames = medicines.map((e) => e.medicineName).toList();

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildMedicineSelectionCard(medicineNames),
            const SizedBox(height: 20),
            if (selectedMedicine != null && selectedMedication != null) ...[
              _buildTimeDropCard(selectedMedication!.assignMedicineSelectTime),
              const SizedBox(height: 20),
            ],
            if (selectedMedication != null) ...[
              _buildMedicationInfoCard(),
              const SizedBox(height: 20),
            ],
            _buildTimeSelectionCard(),
            const SizedBox(height: 20),
            _buildStatusSelectionCard(),
            const SizedBox(height: 20),
            _buildNotesCard(),
            const SizedBox(height: 20),
            _buildMoodSelectionCard(),
            const SizedBox(height: 32),
            _buildContinueButton(),
          ],
        ),
      ),
    );
  }

  // Medicine selection card
  Widget _buildMedicineSelectionCard(List<String> medicineNames) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.medical_services,
                    color: const Color.fromRGBO(90, 38, 101, 1), size: 24),
                const SizedBox(width: 8),
                const Text(
                  'Select Medicine',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildDropdownButton(
              value: selectedMedicine,
              hint: 'Choose a medicine',
              items: medicineNames,
              onChanged: (String? newValue) {
                setState(() {
                  selectedMedicine = newValue;
                  selectedPlace = null; // Reset time drop when medicine changes
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  // Time drop selection card
  Widget _buildTimeDropCard(List<AssignMedicineSelectTime> timeDrops) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.schedule,
                    color: const Color.fromRGBO(90, 38, 101, 1), size: 24),
                const SizedBox(width: 8),
                const Text(
                  'Select Time Drop',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildDropdownButton(
              value: selectedPlace,
              hint: 'Choose time drop',
              items: timeDrops.map((e) => e.name).toList(),
              onChanged: (String? newValue) {
                debugPrint('Slected Place: $newValue');
                setState(() {
                  selectedPlace = newValue;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  // Medication info card
  Widget _buildMedicationInfoCard() {
    if (selectedMedication == null) return const SizedBox.shrink();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'Medication Information',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (selectedTimeDrop?.dosage != null) ...[
              _buildInfoRow('Dosage', '${selectedTimeDrop!.dosage} ${selectedMedication!.unit}', Icons.local_pharmacy),
              const SizedBox(height: 8),
            ],
            if (selectedMedication!.startDate != null && selectedMedication!.endDate != null) ...[
              _buildInfoRow(
                  'Start Date',
                  DateFormat('yyyy-MM-dd').format(selectedMedication!.startDate!),
                  Icons.calendar_today),
              const SizedBox(height: 8),
              _buildInfoRow(
                  'End Date',
                  DateFormat('yyyy-MM-dd').format(selectedMedication!.endDate!),
                  Icons.calendar_today),
              const SizedBox(height: 8),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(color: Colors.grey),
          ),
        ),
      ],
    );
  }

  // Time selection card
  Widget _buildTimeSelectionCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.access_time,
                    color: const Color.fromRGBO(90, 38, 101, 1), size: 24),
                const SizedBox(width: 8),
                const Text(
                  'When was the Medicine taken?',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildTimePicker(),
          ],
        ),
      ),
    );
  }

  // Status selection card
  Widget _buildStatusSelectionCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.assignment,
                    color: const Color.fromRGBO(90, 38, 101, 1), size: 24),
                const SizedBox(width: 8),
                const Text(
                  'Medication Status',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                if (isLoadingStatuses)
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: const Color(0xFFF8F9FA),
                borderRadius: BorderRadius.circular(12.0),
                border: Border.all(color: Colors.grey.shade300),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: DropdownButton<String>(
                value: selectedStatus,
                hint: Text(isLoadingStatuses
                    ? 'Loading statuses...'
                    : 'Select status (optional)'),
                icon: const Icon(Icons.arrow_drop_down, color: Colors.grey),
                isExpanded: true,
                underline: const SizedBox(),
                items: statusOptions.map<DropdownMenuItem<String>>(
                        (Map<String, String> status) {
                      return DropdownMenuItem<String>(
                        value: status['id'],
                        child: Text('${status['code']} - ${status['label']}'),
                      );
                    }).toList(),
                onChanged: isLoadingStatuses
                    ? null
                    : (String? newValue) {
                  setState(() {
                    selectedStatus = newValue;
                  });
                },
              ),
            ),
            if (selectedStatus != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue.shade600, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Selected: ${statusOptions.firstWhere((s) => s['id'] == selectedStatus)['label']}',
                        style: TextStyle(
                          color: Colors.blue.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // Notes card
  Widget _buildNotesCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.note_add,
                    color: const Color.fromRGBO(90, 38, 101, 1), size: 24),
                const SizedBox(width: 8),
                const Text(
                  'Add Notes',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: () {
                    setState(() {
                      showNotesInput = !showNotesInput;
                    });
                  },
                  icon: Icon(
                    showNotesInput ? Icons.remove : Icons.add,
                    color: const Color.fromRGBO(90, 38, 101, 1),
                  ),
                  label: Text(
                    showNotesInput ? 'Cancel' : 'Add Note',
                    style:
                    const TextStyle(color: Color.fromRGBO(90, 38, 101, 1)),
                  ),
                ),
              ],
            ),
            if (showNotesInput) ...[
              const SizedBox(height: 16),
              TextField(
                controller: _notesController,
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: 'Enter your notes here...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                    const BorderSide(color: Color.fromRGBO(90, 38, 101, 1)),
                  ),
                  filled: true,
                  fillColor: const Color(0xFFF8F9FA),
                ),
                onChanged: (value) {
                  setState(() {
                    notes = value;
                  });
                },
              ),
            ],
            if (notes != null && notes!.isNotEmpty && !showNotesInput) ...[
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Text(
                  'Note: $notes',
                  style: TextStyle(color: Colors.green.shade700),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // Mood selection card
  Widget _buildMoodSelectionCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.mood,
                    color: const Color.fromRGBO(90, 38, 101, 1), size: 24),
                const SizedBox(width: 8),
                const Text(
                  'How is Resident Feeling?',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildMoodSelection(),
          ],
        ),
      ),
    );
  }

  // Generic dropdown button
  Widget _buildDropdownButton({
    String? value,
    required String hint,
    required List<String> items,
    required Function(String?) onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(color: Colors.grey.shade300),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: DropdownButton<String>(
        value: value,
        hint: Text(hint, style: const TextStyle(color: Colors.grey)),
        icon: const Icon(Icons.arrow_drop_down, color: Colors.grey),
        isExpanded: true,
        underline: const SizedBox(),
        items: items.map<DropdownMenuItem<String>>((String value) {
          return DropdownMenuItem<String>(
            value: value,
            child: Text(value),
          );
        }).toList(),
        onChanged: onChanged,
      ),
    );
  }

  // Time picker widget
  Widget _buildTimePicker() {
    return GestureDetector(
      onTap: () async {
        TimeOfDay? pickedTime = await showTimePicker(
          context: context,
          initialTime: TimeOfDay.now(),
          builder: (context, child) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: const ColorScheme.light(
                  primary: Color.fromRGBO(90, 38, 101, 1),
                ),
              ),
              child: child!,
            );
          },
        );
        if (pickedTime != null) {
          setState(() {
            selectedTime = pickedTime;
          });
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
        decoration: BoxDecoration(
          color: const Color(0xFFF8F9FA),
          borderRadius: BorderRadius.circular(12.0),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              selectedTime != null
                  ? DateFormat.jm().format(DateTime(
                  0, 0, 0, selectedTime!.hour, selectedTime!.minute))
                  : 'Select Time',
              style: TextStyle(
                fontSize: 16,
                color: selectedTime != null ? Colors.black : Colors.grey,
              ),
            ),
            Icon(Icons.access_time, color: Colors.grey.shade600),
          ],
        ),
      ),
    );
  }

  // Mood selection widget
  Widget _buildMoodSelection() {
    List<Map<String, dynamic>> moods = [
      {
        'icon': Icons.sentiment_very_satisfied,
        'label': 'Very Happy',
        'color': Colors.green
      },
      {
        'icon': Icons.sentiment_satisfied,
        'label': 'Happy',
        'color': Colors.lightGreen
      },
      {'icon': Icons.sentiment_neutral, 'label': 'OK', 'color': Colors.amber},
      {
        'icon': Icons.sentiment_dissatisfied,
        'label': 'Sad',
        'color': Colors.orange
      },
      {
        'icon': Icons.sentiment_very_dissatisfied,
        'label': 'Very Sad',
        'color': Colors.red
      },
    ];

    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children: moods.map((mood) {
        final isSelected = selectedMood == mood['label'];
        return GestureDetector(
          onTap: () {
            setState(() {
              selectedMood = mood['label'];
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isSelected
                  ? const Color.fromRGBO(90, 38, 101, 0.1)
                  : Colors.white,
              border: Border.all(
                color: isSelected
                    ? const Color.fromRGBO(90, 38, 101, 1)
                    : Colors.grey.shade300,
                width: 2,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  mood['icon'],
                  size: 32,
                  color: mood['color'],
                ),
                const SizedBox(height: 4),
                Text(
                  mood['label'],
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight:
                    isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected
                        ? const Color.fromRGBO(90, 38, 101, 1)
                        : Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  // Continue button
  Widget _buildContinueButton() {
    final bool canSubmit = selectedMedicine != null &&
        selectedPlace != null &&
        selectedTime != null &&
        !isSubmitting;

    return Center(
      child: ElevatedButton(
        onPressed: canSubmit ? _handleSubmit : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: canSubmit
              ? const Color.fromRGBO(90, 38, 101, 1)
              : Colors.grey,
          padding: const EdgeInsets.symmetric(horizontal: 48.0, vertical: 16.0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0),
          ),
          elevation: 2,
        ),
        child: isSubmitting
            ? const SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            color: Colors.white,
            strokeWidth: 2,
          ),
        )
            : Text(
          canSubmit ? 'Continue' : 'Please Complete Form',
          style: const TextStyle(
              fontSize: 18,
              color: Colors.white,
              fontWeight: FontWeight.w600),
        ),
      ),
    );
  }
}