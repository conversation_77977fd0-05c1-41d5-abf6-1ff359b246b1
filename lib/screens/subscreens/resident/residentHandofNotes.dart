import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:intl/intl.dart';

import '../../../blocs/handoff_note/handoff_note_bloc.dart';
import '../../../blocs/handoff_note/handoff_note_event.dart';
import '../../../blocs/handoff_note/handoff_note_state.dart';
import '../../../components/drawer.dart';
import '../../../models/handoff_note.dart';
import '../../../routes/router_constants.dart';
import '../../../utils/screen.dart';

class Residenthandofnotes extends StatefulWidget {
  final String residentId;
  const Residenthandofnotes({super.key, required this.residentId});

  @override
  State<Residenthandofnotes> createState() => _ResidenthandofnotesState();
}

class _ResidenthandofnotesState extends State<Residenthandofnotes> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final TextEditingController _noteController = TextEditingController();

  // List of nurses
  final List<String> nurses = ["Nurse A", "Nurse B", "Nurse C", "Nurse D"];
  String? selectedNurse;

  @override
  void initState() {
    super.initState();
    // Fetch handoff notes for this resident
    context
        .read<HandoffNoteBloc>()
        .add(FetchHandoffNotes(residentId: widget.residentId));
  }

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      key: _scaffoldKey,
      drawer: buildDrawer(scaffoldKey: _scaffoldKey),
      appBar: AppBar(
        backgroundColor: const Color.fromRGBO(90, 38, 101, 0.9),
        leading: IconButton(
          onPressed: () {
            _scaffoldKey.currentState!.openDrawer();
          },
          icon: const Icon(HugeIcons.strokeRoundedMenu01, color: Colors.white),
        ),
        actions: [
          IconButton(
            onPressed: () {
              GoRouter.of(context).goNamed(RouteConstants.homescreenRouteName);
            },
            icon: const Icon(Icons.arrow_back, color: Colors.white),
          ),
        ],
      ),
      body: SafeArea(
        child: BlocConsumer<HandoffNoteBloc, HandoffNoteState>(
          listener: (context, state) {
            if (state is HandoffNoteCreated) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Handoff note created successfully')),
              );
              _noteController.clear();
              selectedNurse = null;
            } else if (state is HandoffNoteError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Error: ${state.message}')),
              );
            }
          },
          builder: (context, state) {
            return SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Handoff Notes',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Color.fromRGBO(90, 38, 101, 1),
                      ),
                    ),
                    SizedBox(height: SizeConfig.screenH! * 0.01),
                    const Text(
                      'Create and manage handoff notes for this resident.',
                      style: TextStyle(fontSize: 16),
                    ),
                    SizedBox(height: SizeConfig.screenH! * 0.02),

                    // Dropdown for selecting nurse
                    const Text(
                      'Assign Note to Nurse',
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: SizeConfig.screenH! * 0.01),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          isExpanded: true,
                          hint: const Text('Select Nurse'),
                          value: selectedNurse,
                          onChanged: (String? newValue) {
                            setState(() {
                              selectedNurse = newValue;
                            });
                          },
                          items: nurses
                              .map<DropdownMenuItem<String>>((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(value),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                    SizedBox(height: SizeConfig.screenH! * 0.02),

                    // Note content
                    const Text(
                      'Note Content',
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: SizeConfig.screenH! * 0.01),
                    TextField(
                      controller: _noteController,
                      maxLines: 5,
                      decoration: InputDecoration(
                        hintText: 'Enter handoff note details here...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                      ),
                    ),
                    SizedBox(height: SizeConfig.screenH! * 0.02),

                    // Add Note Button
                    Center(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          if (_noteController.text.isNotEmpty &&
                              selectedNurse != null) {
                            final newNote = HandoffNote(
                              id: '', // Will be generated by the service
                              residentId: widget.residentId,
                              content: _noteController.text,
                              assignedTo: selectedNurse!,
                              createdAt: DateTime.now(),
                              createdBy:
                                  'Current User', // Replace with actual user
                            );

                            context
                                .read<HandoffNoteBloc>()
                                .add(CreateHandoffNote(newNote));
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                  content: Text('Please fill in all fields')),
                            );
                          }
                        },
                        icon: const Icon(Icons.note_add, color: Colors.white),
                        label: const Text('Add Note',
                            style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              const Color.fromRGBO(90, 38, 101, 0.9),
                          padding: const EdgeInsets.symmetric(
                              vertical: 15, horizontal: 30),
                          textStyle: const TextStyle(fontSize: 18),
                        ),
                      ),
                    ),

                    SizedBox(height: SizeConfig.screenH! * 0.03),

                    // Display existing notes
                    const Text(
                      'Existing Handoff Notes',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: SizeConfig.screenH! * 0.01),

                    if (state is HandoffNoteLoading)
                      Center(child: CircularProgressIndicator())
                    else if (state is HandoffNoteLoaded)
                      state.filteredNotes.isEmpty
                          ? Center(child: Text('No handoff notes found'))
                          : ListView.builder(
                              shrinkWrap: true,
                              physics: NeverScrollableScrollPhysics(),
                              itemCount: state.filteredNotes.length,
                              itemBuilder: (context, index) {
                                final note = state.filteredNotes[index];
                                return _buildNoteItem(note);
                              },
                            ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildNoteItem(HandoffNote note) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Assigned to: ${note.assignedTo}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color.fromRGBO(90, 38, 101, 1),
              ),
            ),
            SizedBox(height: SizeConfig.screenH! * 0.01),
            Text(
              'Created by: ${note.createdBy}',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: SizeConfig.screenH! * 0.01),
            Text(
              'Created at: ${DateFormat('yyyy-MM-dd HH:mm').format(note.createdAt)}',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: SizeConfig.screenH! * 0.02),
            Text(
              note.content,
              style: const TextStyle(
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
