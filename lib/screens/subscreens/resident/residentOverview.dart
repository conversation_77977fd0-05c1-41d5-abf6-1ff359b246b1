import 'package:carerez/models/emergency_contact.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hugeicons/hugeicons.dart';
import '../../../components/drawer.dart';
import '../../../routes/router_constants.dart';
import '../../../blocs/resident/resident_bloc.dart';
import '../../../blocs/resident/resident_event.dart';
import '../../../blocs/resident/resident_state.dart';
import '../../../services/resident_service.dart';

class Residentoverview extends StatefulWidget {
  final String residentId;
  final String residentUUID;
  const Residentoverview({super.key, required this.residentId, required this.residentUUID});

  @override
  State<Residentoverview> createState() => _ResidentoverviewState();
}

class _ResidentoverviewState extends State<Residentoverview> with SingleTickerProviderStateMixin {
  final _scaffoldKey = GlobalKey<ScaffoldState>();
  late ResidentBloc _residentBloc;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _residentBloc = ResidentBloc(ResidentService());
    Future.microtask(() {
      _residentBloc.add(FetchResidentOverview(widget.residentUUID, widget.residentId));
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _residentBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _residentBloc,
      child: BlocBuilder<ResidentBloc, ResidentState>(
        builder: (context, state) {
          return Scaffold(
            backgroundColor: const Color(0xFFF8FAFC),
            key: _scaffoldKey,
            drawer: buildDrawer(scaffoldKey: _scaffoldKey),
            body: NestedScrollView(
              headerSliverBuilder: (context, innerBoxIsScrolled) {
                return [
                  SliverAppBar(
                    backgroundColor: Colors.white,
                    elevation: 0,
                    surfaceTintColor: Colors.transparent,
                    pinned: true,
                    expandedHeight: 280,
                    leading: IconButton(
                      icon:  Icon(HugeIcons.strokeRoundedMenu01,),
                      color: Colors.white,
                      onPressed: () {
                        _scaffoldKey.currentState!.openDrawer();
                      },
                    ),
                    actions: [
                      Container(
                        margin: const EdgeInsets.only(right: 16),
                        decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: IconButton(
                          icon: const Icon(Icons.arrow_back_rounded),
                          color: Colors.white,
                          onPressed: () {
                            GoRouter.of(context).goNamed(
                              RouteConstants.residentPersonalRouteName,
                              pathParameters: {
                                'residentId': widget.residentId,
                                'residentUUID': widget.residentUUID,
                              },
                            );
                          },
                        ),
                      ),
                    ],
                    flexibleSpace: FlexibleSpaceBar(
                      background: _buildProfileHeader(state),
                    ),
                  ),
                  SliverPersistentHeader(
                    delegate: _SliverAppBarDelegate(
                      TabBar(
                        controller: _tabController,
                        labelColor: const Color(0xFF6366F1),
                        unselectedLabelColor: const Color(0xFF64748B),
                        indicatorColor: const Color(0xFF6366F1),
                        indicatorWeight: 3,
                        labelStyle: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                        unselectedLabelStyle: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                        isScrollable: true,
                        tabAlignment: TabAlignment.start,
                        tabs: const [
                          Tab(
                            icon: Icon(Icons.person_rounded, size: 20),
                            text: 'Personal',
                          ),
                          Tab(
                            icon: Icon(Icons.account_balance_wallet_rounded, size: 20),
                            text: 'Budget',
                          ),
                          Tab(
                            icon: Icon(Icons.medical_services_rounded, size: 20),
                            text: 'Provider',
                          ),
                          Tab(
                            icon: Icon(Icons.family_restroom_rounded, size: 20),
                            text: 'Family',
                          ),
                          Tab(
                            icon: Icon(Icons.emergency_rounded, size: 20),
                            text: 'Emergency',
                          ),
                        ],
                      ),
                    ),
                    pinned: true,
                  ),
                ];
              },
              body: _buildTabContent(state),
            ),
          );
        },
      ),
    );
  }

  Widget _buildProfileHeader(ResidentState state) {
    if (state is ResidentOverviewLoaded) {
      final resident = state.resident;
      return Container(
        color: Color.fromRGBO(90, 38, 101, 0.9),
        child: SafeArea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [

              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(60),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(56),
                  child: Image.network(
                    resident.image,
                    height: 100,
                    width: 100,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return const Icon(
                        Icons.person_rounded,
                        size: 100,
                        color: Colors.white70,
                      );
                    },
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return const Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(height: 10),
              Text(
                _formatDisplayText(resident.name ?? 'N/A'),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                'Resident User Code: ${resident.userCode ?? 'Not Available'}',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _formatDisplayText(resident.status ?? 'Active'),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      decoration: const BoxDecoration(
        color: Color.fromRGBO(90, 38, 101, 0.9),
      ),
      child: const SafeArea(
        child: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      ),
    );
  }

  Widget _buildTabContent(ResidentState state) {
    if (state is ResidentLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6366F1)),
        ),
      );
    } else if (state is ResidentError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline_rounded,
              size: 64,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'Error: ${state.message}',
              style: TextStyle(
                color: Colors.red.shade600,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    } else if (state is ResidentOverviewLoaded) {
      final resident = state.resident;
      final budget = state.budget;
      final serviceProvider = state.serviceProvider;
      final familyInfo = state.familyInformation;
      final contactInfo = state.contactInfo;

      return TabBarView(
        controller: _tabController,
        children: [
          _buildPersonalTab(resident),
          _buildBudgetTab(budget),
          _buildProviderTab(serviceProvider),
          _buildFamilyTab(familyInfo),
          _buildEmergencyTab(contactInfo),
        ],
      );
    } else {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.person_off_rounded,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No resident data available',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildPersonalTab(dynamic resident) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          _buildInfoCard(
            'Basic Information',
            Icons.person_rounded,
            const Color(0xFF6366F1),
            [
              ('Age', '${resident.calculatedAge} years old'),
              ('Gender', _formatDisplayText(resident.gender ?? 'N/A')),
              ('Phone Number', _formatPhoneNumber(resident.phone)),
              ('Blood Type', _formatBloodType(resident.bloodType)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetTab(dynamic budget) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          if (budget != null) ...[
            _buildInfoCard(
              'Budget Details',
              Icons.account_balance_wallet_rounded,
              const Color(0xFF059669),
              [
                ('Budget Type', _formatDisplayText(budget.budgetType?.toString() ?? 'N/A')),
                ('Budget Amount', _formatCurrency(budget.budgetAmount?.toString())),
                ('Account Manager', _formatDisplayText(budget.accountManager?.toString() ?? 'N/A')),
                ('Email Address', budget.emailAddress?.toString() ?? 'Not Available'),
                ('Unlimited Budget', _formatDisplayText(budget.unlimited?.toString() ?? 'N/A')),
                ('Review Date', _formatDate(budget.reviewDate?.toString())),
              ],
            ),
            if (budget.attachments != null && budget.attachments.isNotEmpty) ...[
              const SizedBox(height: 20),
              _buildAttachmentsCard('Budget Attachments', budget.attachments),
            ],
          ] else ...[
            _buildEmptyState(
              'No Budget Information',
              'Budget details are not available for this resident.',
              Icons.account_balance_wallet_outlined,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProviderTab(dynamic serviceProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          if (serviceProvider != null) ...[
            _buildInfoCard(
              'Service Provider Details',
              Icons.medical_services_rounded,
              const Color(0xFFDC2626),
              [
                ('Provider Type ID', serviceProvider.svcMaster!.name?? 'Not Available'),
                ('Provider Description', serviceProvider.svcMaster!.description?? 'Not Available'),
                ('Email Address', serviceProvider.emailAddress?.toString() ?? 'Not Available'),
                ('Phone Number', _formatPhoneNumber(serviceProvider.phoneNumber?.toString())),
                ('Address', _formatAddress(serviceProvider.address?.toString())),
                ('Notes', _formatDisplayText(serviceProvider.note?.toString() ?? 'N/A')),
              ],
            ),
            if (serviceProvider.svcProviderAttachments != null && serviceProvider.svcProviderAttachments.isNotEmpty) ...[
              const SizedBox(height: 20),
              _buildAttachmentsCard('Provider Attachments', serviceProvider.svcProviderAttachments),
            ],
          ] else ...[
            _buildEmptyState(
              'No Service Provider',
              'Service provider information is not available for this resident.',
              Icons.medical_services_outlined,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFamilyTab(List<dynamic>? familyInfo) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          if (familyInfo != null && familyInfo.isNotEmpty) ...[
            ...familyInfo.asMap().entries.map((entry) {
              final index = entry.key;
              final member = entry.value;
              return Column(
                children: [
                  _buildInfoCard(
                    'Family Member ${index + 1}',
                    Icons.family_restroom_rounded,
                    const Color(0xFF7C3AED),
                    [
                      ('Full Name', _formatName(member.firstName, member.lastName, member.secondName)),
                      ('Phone Number', _formatPhoneNumber(member.phoneNumber?.toString())),
                      ('Email Address', member.emailAddress?.toString() ?? 'Not Available'),
                      ('Address', _formatAddress(member.address?.toString())),
                    ],
                  ),
                  if (index < familyInfo.length - 1) const SizedBox(height: 20),
                ],
              );
            }).toList(),
          ] else ...[
            _buildEmptyState(
              'No Family Members',
              'No family member information is available for this resident.',
              Icons.family_restroom_outlined,
            ),
          ],
        ],
      ),
    );
  }

 Widget _buildEmergencyTab(List<EmergencyContact>? contactInfo) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          if (contactInfo != null && contactInfo.isNotEmpty) ...[
            ...contactInfo.asMap().entries.map((entry) {
              final index = entry.key;
              final contact = entry.value;
              return Column(
                children: [
                  _buildInfoCard(
                    'Emergency Contact ${index + 1}',
                    Icons.emergency_rounded,
                    const Color(0xFFEA580C),
                    [
                      ('Full Name', _formatName(contact.firstName, contact.lastName, contact.secondName)),
                      ('Phone Number', _formatPhoneNumber(contact.phoneNumber?.toString())),
                      ('Email Address', contact.emailAddress?.toString() ?? 'Not Available'),
                      ('Relationship', _formatDisplayText(contact.relation?.toString() ?? 'N/A')),
                      ('Address', _formatAddress(contact.address?.toString())),
                    ],
                  ),
                  if (index < contactInfo.length - 1) const SizedBox(height: 20),
                ],
              );
            }).toList(),
          ] else ...[
            _buildEmptyState(
              'No Emergency Contact',
              'Emergency contact information is not available for this resident.',
              Icons.contact_emergency_rounded,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoCard(String title, IconData icon, Color color, List<(String, String)> items) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1E293B),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildInfoGrid(items),
        ],
      ),
    );
  }

  Widget _buildAttachmentsCard(String title, List<dynamic> attachments) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF6B7280).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.attach_file_rounded,
                  color: Color(0xFF6B7280),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1E293B),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...attachments.map<Widget>((attachment) => Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFF8FAFC),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFFE2E8F0)),
            ),
            child: Row(
              children: [
                const Icon(Icons.description_rounded, size: 20, color: Color(0xFF6B7280)),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _formatDisplayText(attachment.attachmentName?.toString() ?? 'Unknown Document'),
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF1E293B),
                        ),
                      ),
                        //IF ATTACHMENT HAS AN EXPIRY DATE FIELD

                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  size: 16,
                  color: Colors.grey.shade400,
                ),
              ],
            ),
          )).toList(),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey.shade300,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF64748B),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoGrid(List<(String, String)> items) {
    return Column(
      children: items.map((item) => Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: const Color(0xFFF8FAFC),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: const Color(0xFFE2E8F0), width: 0.5),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: 110,
              child: Text(
                item.$1,
                style: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF475569),
                  letterSpacing: 0.5,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                _formatDisplayText(item.$2),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF1E293B),
                  height: 1.4,
                ),
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }

  // Helper method to format text for better display
  String _formatDisplayText(String text) {
    if (text == 'N/A' || text.isEmpty) return 'Not Available';

    // Handle boolean values
    if (text.toLowerCase() == 'true') return 'Yes';
    if (text.toLowerCase() == 'false') return 'No';

    // Handle null or empty strings
    if (text.toLowerCase() == 'null') return 'Not Specified';

    // Capitalize first letter of each word for names and general text
    if (text.length > 50) {
      // For long text, just capitalize first letter
      return text[0].toUpperCase() + text.substring(1);
    }

    // For shorter text, apply title case
    return text.split(' ')
        .map((word) => word.isNotEmpty
        ? word[0].toUpperCase() + word.substring(1).toLowerCase()
        : word)
        .join(' ');
  }

  // Helper method to format names properly
  String _formatName(String? firstName, String? lastName, [String? middleName]) {
    List<String> nameParts = [];

    if (firstName?.isNotEmpty == true) {
      nameParts.add(_capitalizeWord(firstName!));
    }
    if (middleName?.isNotEmpty == true) {
      nameParts.add(_capitalizeWord(middleName!));
    }
    if (lastName?.isNotEmpty == true) {
      nameParts.add(_capitalizeWord(lastName!));
    }

    return nameParts.isNotEmpty ? nameParts.join(' ') : 'Name Not Available';
  }

  String _capitalizeWord(String word) {
    if (word.isEmpty) return word;
    return word[0].toUpperCase() + word.substring(1).toLowerCase();
  }

  // Helper method to format phone numbers
  String _formatPhoneNumber(String? phone) {
    if (phone == null || phone.isEmpty || phone.toLowerCase() == 'null') {
      return 'Not Available';
    }

    // Remove any non-digit characters for processing
    String digitsOnly = phone.replaceAll(RegExp(r'[^\d]'), '');

    if (digitsOnly.length == 10) {
      // Format as (XXX) XXX-XXXX
      return '(${digitsOnly.substring(0, 3)}) ${digitsOnly.substring(3, 6)}-${digitsOnly.substring(6)}';
    } else if (digitsOnly.length == 11 && digitsOnly.startsWith('1')) {
      // Format as +1 (XXX) XXX-XXXX
      return '+1 (${digitsOnly.substring(1, 4)}) ${digitsOnly.substring(4, 7)}-${digitsOnly.substring(7)}';
    }

    // Return original if it doesn't match standard formats
    return phone;
  }

  // Helper method to format blood type
  String _formatBloodType(String? bloodType) {
    if (bloodType == null || bloodType.isEmpty || bloodType.toLowerCase() == 'null') {
      return 'Not Available';
    }
    return bloodType.toUpperCase();
  }

  // Helper method to format currency
  String _formatCurrency(String? amount) {
    if (amount == null || amount.isEmpty || amount.toLowerCase() == 'null') {
      return 'Not Available';
    }

    // Try to parse as number and format as currency
    try {
      double value = double.parse(amount.replaceAll(RegExp(r'[^\d.]'), ''));
      return '\$${value.toStringAsFixed(2)}';
    } catch (e) {
      return _formatDisplayText(amount);
    }
  }

  // Helper method to format dates
  String _formatDate(String? date) {
    if (date == null || date.isEmpty || date.toLowerCase() == 'null') {
      return 'Not Set';
    }

    try {
      DateTime parsedDate = DateTime.parse(date);
      return '${parsedDate.day}/${parsedDate.month}/${parsedDate.year}';
    } catch (e) {
      return _formatDisplayText(date);
    }
  }

  // Helper method to format addresses
  String _formatAddress(String? address) {
    if (address == null || address.isEmpty || address.toLowerCase() == 'null') {
      return 'Not Available';
    }

    // Capitalize each word in the address
    return address.split(' ')
        .map((word) => word.isNotEmpty
        ? word[0].toUpperCase() + word.substring(1).toLowerCase()
        : word)
        .join(' ');
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  _SliverAppBarDelegate(this._tabBar);

  final TabBar _tabBar;

  @override
  double get minExtent => _tabBar.preferredSize.height;
  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Colors.white,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return false;
  }
}