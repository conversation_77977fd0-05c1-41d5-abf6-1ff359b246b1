import 'package:carerez/blocs/staff/staff_bloc.dart' show StaffBloc;
import 'package:carerez/screens/subscreens/activites/activites_widgets.dart';
import 'package:carerez/screens/subscreens/activites/communication_widgets.dart';
import 'package:carerez/screens/subscreens/activites/personalcare_widgets.dart';
import 'package:carerez/screens/subscreens/activites/toileting_widgets.dart';
import 'package:carerez/services/staff_service.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_icon_class/font_awesome_icon_class.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';
import '../../../blocs/staff/staff_event.dart';
import '../../../blocs/staff/staff_state.dart';
import '../../../components/drawer.dart';
import '../../../models/staff.dart';
import '../../../routes/router_constants.dart';
import '../../../utils/helpers.dart';
import '../../../utils/screen.dart';
import '../../utils/snackbar_utils.dart';
import '../activites/emotionalsupport_widgets.dart';
import '../activites/medication_widgets.dart';
import '../activites/mobility_widgets.dart';
import '../activites/nutritioneating_widgets.dart';
import '../activites/personalsafety_widgets.dart';
import '../activites/sleep_widgets.dart';
import '../../../models/activity.dart';
import '../../../services/activity_service.dart';

class ResidentCareActivities extends StatefulWidget {
  final String residentId;
  final String residentUUID;
  const ResidentCareActivities(
      {super.key, required this.residentId, required this.residentUUID});

  @override
  State<ResidentCareActivities> createState() => _ResidentCareActivitiesState();
}

class _ResidentCareActivitiesState extends State<ResidentCareActivities> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final Map<String, bool> _isExpanded = {};
  final TextEditingController searchController = TextEditingController();
  String _searchQuery = '';
  bool _selectionMode = false;
  final Set<String> _selectedActivities = {};

  late List<Staff> staffList = [];

  Future<void> fetchStaffList() async {
    StaffService staffService = StaffService();
    final fetchedList = await staffService.getStaff();

    if (fetchedList.isEmpty) {
      showSnackBar(
        context, 'No staff members found. Please add staff members first.',
      );
      staffList = [];
      return;
    }
    setState(() {
      staffList = fetchedList;
    });
  }

  @override
  void initState() {
    super.initState();
    fetchStaffList();
  }



  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      key: _scaffoldKey,
      drawer: buildDrawer(scaffoldKey: _scaffoldKey),
      appBar: AppBar(
        backgroundColor: const Color.fromRGBO(90, 38, 101, 0.9),
        leading: IconButton(
          onPressed: () {
            _scaffoldKey.currentState!.openDrawer();
          },
          icon: const Icon(HugeIcons.strokeRoundedMenu01, color: Colors.white),
        ),
        actions: [
          if (_selectionMode) ...[
            IconButton(
              onPressed: _clearSelection,
              icon: const Icon(Icons.clear, color: Colors.white),
            ),
            IconButton(
              onPressed: _openSelectedActivities,
              icon: const Icon(Icons.open_in_new, color: Colors.white),
            ),
          ],
          IconButton(
            onPressed: () {
              GoRouter.of(context).goNamed(RouteConstants.homescreenRouteName);
            },
            icon: const Icon(Icons.arrow_back, color: Colors.white),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSearchBar(searchController),
              _buildCategory('Personal Care', _getPersonalCareActivities()),
              _buildCategory('Personal Safety and Environment',
                  _getPersonalSafetyActivities()),
              _buildCategory(
                  'Nutrition and Eating', _getNutritionEatingActivities()),
              _buildCategory('Activites', _getActivities()),
              _buildCategory('Communication', _getCommunicationActivities()),
              _buildCategory('Toileting', _getToiletingActivities()),
              _buildCategory('Mobility', _getMobilityActivities()),
              _buildCategory('Medication', _getMedicationActivities()),
              _buildCategory('Sleep', _getSleepActivities()),
              _buildCategory('Emotional Support', _getEmotionalActivities())
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _getPersonalCareActivities() {
    List<Widget> allActivities = [
      _buildActivityIcon(Icons.local_laundry_service, 'Wash Clothes',
          onTap: () => showDialog(
              context: context,
              builder: (context) => WashClothes(
                    onSave: (activity) async {
                      await ActivityService()
                          .sendResidentCareActivity(activity);
                      // Optionally show a snackbar or update state
                    },
                staffList: staffList, residentUUID: widget.residentUUID
                  ))),
      _buildActivityIcon(Icons.change_circle, 'Change Clothes', onTap: () {
        showDialog(
          context: context,
          builder: (context) => ChangeClothes(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedHandPointingLeft03, 'Nail care',
          onTap: () {
        showDialog(
            context: context,
            builder: (context) => NailCare(
                onSave: (activity) => _saveResidentCareActivity(activity),
              staffList: staffList, residentUUID: widget.residentUUID
            ));
      }),
      _buildActivityIcon(Icons.brush, 'Oral Hygiene', onTap: () {
        showDialog(
          context: context,
          builder: (context) => OralHygiene(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedIceCream04, 'Cream', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Cream(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.cleaning_services, 'Hair wash', onTap: () {
        showDialog(
          context: context,
          builder: (context) => HairWash(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(FontAwesomeIcons.handsBubbles, 'Wash Hands',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => WashHands(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.bathtub, 'Bath', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Bath(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.content_cut, 'Shave', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Shave(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.face_retouching_natural_sharp, 'Makeup',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => MakeUp(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.cleaning_services_sharp, 'Cleaning', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Cleaning(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.shower, 'Shower', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Shower(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.remove_red_eye_sharp, 'Check Eye', onTap: () {
        showDialog(
          context: context,
          builder: (context) => CheckEye(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedRunningShoes, 'Footwear',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => Footwear(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedInjection, 'Catheter Care',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => CatheterCare(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedEar, 'Check \eEars', onTap: () {
        showDialog(
          context: context,
          builder: (context) => CheckEars(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedBandage, 'Menstrual Care',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => MenstrualCycle(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.earbuds, 'Check Hearing Aid', onTap: () {
        showDialog(
          context: context,
          builder: (context) => CheckHearingAid(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedGlasses, 'Check Glasses',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => CheckGlasses(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
    ];

    if (_searchQuery.isNotEmpty) {
      allActivities = allActivities.where((activity) {
        String label = (activity as GestureDetector).child is Column
            ? ((activity.child as Column).children.last as Text)
                .data!
                .toLowerCase()
            : '';
        return label.contains(_searchQuery);
      }).toList();
    }

    return allActivities;
  }

  List<Widget> _getPersonalSafetyActivities() {
    List<Widget> allActivities = [
      _buildActivityIcon(Icons.check, 'Check OK', onTap: () {
        showDialog(
          context: context,
          builder: (context) => CheckOK(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.bed, 'Change Bed', onTap: () {
        showDialog(
          context: context,
          builder: (context) => ChangeBed(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.chair, 'Check Chair', onTap: () {
        showDialog(
          context: context,
          builder: (context) => CheckChair(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedBed, 'Check Mattress',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => CheckMattress(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.wheelchair_pickup, 'Check Wheelchair',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => CheckWheelChair(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.curtains, 'Check Curtains', onTap: () {
        showDialog(
          context: context,
          builder: (context) => AdjustCurtains(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.room_preferences, 'Check Room', onTap: () {
        showDialog(
          context: context,
          builder: (context) => CheckRoom(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.thermostat, 'Fridge Temperature', onTap: () {
        showDialog(
          context: context,
          builder: (context) => FridgeTemperature(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.thermostat_auto, 'Room Temperature', onTap: () {
        showDialog(
          context: context,
          builder: (context) => RoomTemperature(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(
        HugeIcons.strokeRoundedRunningShoes,
        'Check Footwear',
        onTap: () {
          showDialog(
            context: context,
            builder: (context) => Footwear(
              onSave: (activity) async {
                await ActivityService().sendResidentCareActivity(activity);

                // Optionally show a snackbar or update state
              }, staffList: staffList, residentUUID: widget.residentUUID
            ),
          );
        },
      ),
      _buildActivityIcon(Icons.settings, 'Check Equipment', onTap: () {
        showDialog(
          context: context,
          builder: (context) => CheckEquipment(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.call_to_action, 'Check Action Mat', onTap: () {
        showDialog(
          context: context,
          builder: (context) => CheckActionMat(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedNecklace, 'Check Pendant Alarm',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => CheckPendantAlarm(
                        onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedWardrobe01, 'Tidied Wardrobe',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => TidiedWardrobe(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(
          HugeIcons.strokeRoundedWheelchair, 'Check WheelChair Belt',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => CheckWheelChairBelt(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.sensors, 'Check Sensor', onTap: () {
        showDialog(
          context: context,
          builder: (context) => CheckSensor(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedBedBunk, 'Check Bedrails',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => CheckBedRails(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedRestoreBin, 'Check Bin Bags',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => CheckBinBags(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
    ];

    if (_searchQuery.isNotEmpty) {
      allActivities = allActivities.where((activity) {
        String label = (activity as GestureDetector).child is Column
            ? ((activity.child as Column).children.last as Text)
                .data!
                .toLowerCase()
            : '';
        return label.contains(_searchQuery);
      }).toList();
    }

    return allActivities;
  }

  List<Widget> _getNutritionEatingActivities() {
    List<Widget> allActivities = [
      _buildActivityIcon(HugeIcons.strokeRoundedTea, 'Tea', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Tea(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.lunch_dining, 'Lunch', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Lunch(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedVegetarianFood, 'Supper',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => Supper(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.breakfast_dining, 'Breakfast', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Breakfast(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.soup_kitchen, 'Soup', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Soup(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(CupertinoIcons.bubble_middle_top_fill, 'Jelly',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => Jelly(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.icecream_outlined, 'Ice Lolly', onTap: () {
        showDialog(
          context: context,
          builder: (context) => IceLolly(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedNaturalFood, 'Pudding',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => Pudding(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedDrink, 'Drink', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Drink(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.water_drop, 'Water', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Water(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.local_drink_outlined, 'Juice', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Juice(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedSoftDrink01, 'Alcoholic Drink',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => AlcoholicDrink(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedSoftDrink02, 'Wine', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Wine(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.fastfood, 'Food Order', onTap: () {
        showDialog(
          context: context,
          builder: (context) => FoodOrder(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedMilkCarton, 'Milk', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Milk(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.food_bank, 'Snack', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Snack(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedMilkOat, 'Milkshake',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => MilkShake(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.coffee, 'Coffee', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Coffee(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.local_drink_sharp, 'Thick Drink', onTap: () {
        showDialog(
          context: context,
          builder: (context) => ThickDrink(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
    ];

    if (_searchQuery.isNotEmpty) {
      allActivities = allActivities.where((activity) {
        String label = (activity as GestureDetector).child is Column
            ? ((activity.child as Column).children.last as Text)
                .data!
                .toLowerCase()
            : '';
        return label.contains(_searchQuery);
      }).toList();
    }

    return allActivities;
  }

  List<Widget> _getActivities() {
    List<Widget> allActivities = [
      _buildActivityIcon(Icons.chair_alt_rounded, 'Armchair Exercise',
          onTap: () => showDialog(
                context: context,
                builder: (context) => ArmChairExersice(
                  onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
                ),
              )),
      _buildActivityIcon(Icons.palette, 'Art and Craft', onTap: () {
        showDialog(
          context: context,
          builder: (context) => ArtandCraft(
           onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.sports_basketball, 'Ball Games', onTap: () {
        showDialog(
          context: context,
          builder: (context) => BallGames(
           onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedChickenThighs, 'BBQ',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => BBQ(
           onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.cake, 'Birthday', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Birthday(
           onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(FontAwesomeIcons.bowlingBall, 'Bowling', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Bowling(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.restaurant, 'Cooking', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Cooking(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.book, 'Crossword', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Crossword(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.directions_car, 'Driving', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Driving(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.tv, 'Entertainment', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Entertainment(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.directions_run, 'Exercise', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Exercise(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.pool, 'Swimming', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Swimming(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.games, 'Board Games', onTap: () {
        showDialog(
          context: context,
          builder: (context) => BoardGames(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.movie, 'Film', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Film(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.church, 'Church', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Church(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.home_work, 'Day Centre', onTap: () {
        showDialog(
          context: context,
          builder: (context) => DayCentre(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.gamepad, 'Dominoes', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Dominoes(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.flag, 'Fete', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Fete(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.videogame_asset, 'Game Console', onTap: () {
        showDialog(
          context: context,
          builder: (context) => GameConsole(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.theater_comedy, 'Drama', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Drama(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.games, 'Played Games', onTap: () {
        showDialog(
          context: context,
          builder: (context) => PlayedGames(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.kitchen, 'Knitting', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Knitting(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.spa, 'Massage', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Massage(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.music_note, 'Music', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Music(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.cleaning_services, 'House Work', onTap: () {
        showDialog(
          context: context,
          builder: (context) => HouseWork(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.grass, 'Gardening', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Gardening(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.party_mode, 'Partying', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Partying(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(FontAwesomeIcons.horse, 'Horse Riding', onTap: () {
        showDialog(
          context: context,
          builder: (context) => HorseRiding(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.directions_walk, 'Outing', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Outing(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.pets, 'Pets', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Pets(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.quiz, 'Quiz', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Quiz(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.local_bar, 'Pub', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Pub(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.self_improvement, 'Relaxation', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Relaxation(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.menu_book, 'Reading', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Reading(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.receipt, 'Reminiscence', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Reminiscence(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.location_city, 'Community', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Community(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.shopping_cart, 'Shopping', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Shopping(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.healing, 'Therapeutic', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Therapeutic(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.spa, 'Sensory', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Sensory(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.music_note, 'Singing', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Singing(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.theaters, 'Theatre', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Theatre(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.family_restroom, 'Visit Relatives', onTap: () {
        showDialog(
          context: context,
          builder: (context) => VisitRelatives(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.people, 'Visitor', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Visitor(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.directions_walk, 'Walked Outside', onTap: () {
        showDialog(
          context: context,
          builder: (context) => WalkedOutside(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.airline_seat_individual_suite, 'Stayed in Room',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => StayedInRoom(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.fastfood, 'Eat Outside', onTap: () {
        showDialog(
          context: context,
          builder: (context) => EatOutside(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedHairClips, 'Hair Dresser',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => HairDresser(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedMeetingRoom, 'Sitting Room',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => SittingRoom(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.local_cafe, 'Cafe', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Cafe(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
    ];

    if (_searchQuery.isNotEmpty) {
      allActivities = allActivities.where((activity) {
        String label = (activity as GestureDetector).child is Column
            ? ((activity.child as Column).children.last as Text)
                .data!
                .toLowerCase()
            : '';
        return label.contains(_searchQuery);
      }).toList();
    }

    return allActivities;
  }

  List<Widget> _getCommunicationActivities() {
    List<Widget> allActivities = [
      _buildActivityIcon(Icons.chat, 'Chat', onTap: () {
        showDialog(
          context: context,
          builder: (context) => ChatWidget(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.read_more, 'Read Letter', onTap: () {
        showDialog(
          context: context,
          builder: (context) => ReadLetter(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(FontAwesomeIcons.newspaper, 'Newspaper', onTap: () {
        showDialog(
          context: context,
          builder: (context) => NewsPaper(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.email, 'Email', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Email(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.notifications_active, 'Bell', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Bell(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.phone, 'Phone', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Phone(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.speaker_notes_off, 'Can\'t Communicate',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => CantCommunicate(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.school, 'Mentoring', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Mentoring(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.edit, 'Write Letter', onTap: () {
        showDialog(
          context: context,
          builder: (context) => WriteLetter(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
    ];

    if (_searchQuery.isNotEmpty) {
      allActivities = allActivities.where((activity) {
        String label = (activity as GestureDetector).child is Column
            ? ((activity.child as Column).children.last as Text)
                .data!
                .toLowerCase()
            : '';
        return label.contains(_searchQuery);
      }).toList();
    }

    return allActivities;
  }

  List<Widget> _getToiletingActivities() {
    List<Widget> allActivities = [
      _buildActivityIcon(HugeIcons.strokeRoundedClothes, 'Wet Clothes',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => WetClothes(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedClothes, 'Soiled Clothes',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => SoiledClothes(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedToilet01, 'Toilet Help',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => ToiletHelp(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedBedSingle01, 'Bed Pan',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => BedPan(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(
          HugeIcons.strokeRoundedMedicineBottle01, 'Urine Bottle', onTap: () {
        showDialog(
          context: context,
          builder: (context) => UrineBottle(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.bathtub, 'Commode', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Commode(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedDiaper, 'Pad Check', onTap: () {
        showDialog(
          context: context,
          builder: (context) => PadCheck(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
    ];

    if (_searchQuery.isNotEmpty) {
      allActivities = allActivities.where((activity) {
        String label = (activity as GestureDetector).child is Column
            ? ((activity.child as Column).children.last as Text)
                .data!
                .toLowerCase()
            : '';
        return label.contains(_searchQuery);
      }).toList();
    }

    return allActivities;
  }

  List<Widget> _getMobilityActivities() {
    List<Widget> allActivities = [
      _buildActivityIcon(Icons.bed, 'Into Bed', onTap: () {
        showDialog(
          context: context,
          builder: (context) => IntoBed(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.bed, 'Out of Bed', onTap: () {
        showDialog(
          context: context,
          builder: (context) => OutOfBed(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.directions_walk, 'Walk', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Walk(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.elevator, 'Elevate Legs', onTap: () {
        showDialog(
          context: context,
          builder: (context) => ElevateLegs(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.stairs, 'Stairs', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Stairs(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.chair, 'Into Chair', onTap: () {
        showDialog(
          context: context,
          builder: (context) => IntoChair(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.chair, 'Up from Chair', onTap: () {
        showDialog(
          context: context,
          builder: (context) => UpFromChair(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.person, 'Standing Hoist', onTap: () {
        showDialog(
          context: context,
          builder: (context) => StandingHoist(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.conveyor_belt, 'Handing Belt', onTap: () {
        showDialog(
          context: context,
          builder: (context) => HandingBelt(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.warning, 'Fall', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Fall(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.people_outline, 'Hoist 2 People', onTap: () {
        showDialog(
          context: context,
          builder: (context) => HoistTwoPeople(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.move_to_inbox, 'Moved', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Moved(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
    ];

    if (_searchQuery.isNotEmpty) {
      allActivities = allActivities.where((activity) {
        String label = (activity as GestureDetector).child is Column
            ? ((activity.child as Column).children.last as Text)
                .data!
                .toLowerCase()
            : '';
        return label.contains(_searchQuery);
      }).toList();
    }

    return allActivities;
  }

  List<Widget> _getMedicationActivities() {
    List<Widget> allActivities = [
      _buildActivityIcon(Icons.medication, 'Medication', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Medication(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(FontAwesomeIcons.userDoctor, 'Doctor', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Doctor(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(FontAwesomeIcons.userNurse, 'Nurse', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Nurse(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(FontAwesomeIcons.chalkboardUser, 'Professor',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => Professor(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.sick, 'Sick', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Sick(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.bedroom_child_outlined, 'Massage', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Massage(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.sick, 'In Pain', onTap: () {
        showDialog(
          context: context,
          builder: (context) => InPain(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.call, 'Call Doctor', onTap: () {
        showDialog(
          context: context,
          builder: (context) => CallDoctor(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(FontAwesomeIcons.truckMedical, 'Ambulance', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Ambulance(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(FontAwesomeIcons.bandage, 'Wound', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Wound(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedBodyPartLeg, 'Foot Splint',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => FootSplint(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.bloodtype_sharp, 'Blood Sugar', onTap: () {
        showDialog(
          context: context,
          builder: (context) => BloodSugar(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedBloodPressure, 'Blood Pressure',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => BloodPressure(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.coronavirus, 'Corona Virus', onTap: () {
        showDialog(
          context: context,
          builder: (context) => CoronaVirus(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.favorite, 'Pulse', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Pulse(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.medication, 'PRN Medication', onTap: () {
        showDialog(
          context: context,
          builder: (context) => PRNMedication(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(FontAwesomeIcons.hand, 'Hand Splint', onTap: () {
        showDialog(
          context: context,
          builder: (context) => HandSplint(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.bloodtype, 'Blood INR', onTap: () {
        showDialog(
          context: context,
          builder: (context) => BloodINR(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.medical_services, 'Specialist', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Specialist(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.whatshot, 'Heat Pack', onTap: () {
        showDialog(
          context: context,
          builder: (context) => HeatPack(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.compress, 'Compress', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Compress(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.healing, 'Skin Integrity', onTap: () {
        showDialog(
          context: context,
          builder: (context) => SkinIntegrity(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.favorite, 'Vitals', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Vitals(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.sentiment_satisfied_alt, 'Conscious', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Conscious(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.new_releases, 'News', onTap: () {
        showDialog(
          context: context,
          builder: (context) => News(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.hearing, 'Ear Syringe', onTap: () {
        showDialog(
          context: context,
          builder: (context) => EarSyringe(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedInjection, 'Insulin',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => Insulin(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(FontAwesomeIcons.linesLeaning, 'IV Line Flush',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => IVLineFlush(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(FontAwesomeIcons.pills, 'Multistrix', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Multistrix(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedGiveBlood, 'Blood Test',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => BloodTest(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedNeuralNetwork, 'Neuro Obs',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => NeuroObs(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.sign_language, 'Soft Sign', onTap: () {
        showDialog(
          context: context,
          builder: (context) => SoftSign(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(FontAwesomeIcons.houseMedical, 'Pap Therapy',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => PAPTherapy(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.air_sharp, 'Oxygen Fever', onTap: () {
        showDialog(
          context: context,
          builder: (context) => OxygenFever(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedInjection, 'New Catheter',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => NewCatheter(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.wash_outlined, 'Washout', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Washout(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(CupertinoIcons.bag, 'Add Bag', onTap: () {
        showDialog(
          context: context,
          builder: (context) => AddBag(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedClean, 'Cleaned', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Cleaned(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(CupertinoIcons.square_pencil_fill, 'PEF', onTap: () {
        showDialog(
          context: context,
          builder: (context) => PEF(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(FontAwesomeIcons.disease, 'Epileptic Seizure',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => EpilepticSeizure(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.stop_circle, 'Stoma', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Stoma(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.stop_circle_outlined, 'Tracheostoma', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Tracheostoma(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(FontAwesomeIcons.hospital, 'Hospital', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Hospital(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(FontAwesomeIcons.clinicMedical, 'Clinic', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Clinic(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(CupertinoIcons.eyedropper, 'Enema', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Enema(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedAuction, 'Suction', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Suction(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(FontAwesomeIcons.creativeCommonsSampling, 'Sample',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => Sample(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
    ];

    if (_searchQuery.isNotEmpty) {
      allActivities = allActivities.where((activity) {
        String label = (activity as GestureDetector).child is Column
            ? ((activity.child as Column).children.last as Text)
                .data!
                .toLowerCase()
            : '';
        return label.contains(_searchQuery);
      }).toList();
    }

    return allActivities;
  }

  List<Widget> _getSleepActivities() {
    List<Widget> allActivities = [
      _buildActivityIcon(HugeIcons.strokeRoundedDiaper, 'Pad Change',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => PadChange(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.bedtime, 'Check Sleep', onTap: () {
        showDialog(
          context: context,
          builder: (context) => CheckSleep(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
    ];

    if (_searchQuery.isNotEmpty) {
      allActivities = allActivities.where((activity) {
        String label = (activity as GestureDetector).child is Column
            ? ((activity.child as Column).children.last as Text)
                .data!
                .toLowerCase()
            : '';
        return label.contains(_searchQuery);
      }).toList();
    }

    return allActivities;
  }

  List<Widget> _getEmotionalActivities() {
    List<Widget> allActivities = [
      _buildActivityIcon(Icons.mood_bad, 'Upset', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Upset(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedAngry, 'Agitated', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Agitated(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.sentiment_neutral_sharp, 'Confused', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Confused(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.directions_walk_outlined, 'Pacing', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Pacing(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(
          Icons.sentiment_very_dissatisfied_outlined, 'Hallucination',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => Hallucination(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.repeat, 'Repetitive', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Repetitive(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.directions_walk, 'Wandering', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Wandering(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedWorkAlert, 'Social Worker',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => SocialWorker(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.people_alt, 'Staff Supported', onTap: () {
        showDialog(
          context: context,
          builder: (context) => StaffSupported(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(Icons.warning, 'Challenging', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Challenging(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedBrain01, 'Paranoid', onTap: () {
        showDialog(
          context: context,
          builder: (context) => Paranoid(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
      _buildActivityIcon(HugeIcons.strokeRoundedBrain02, 'Disinhibited',
          onTap: () {
        showDialog(
          context: context,
          builder: (context) => Disinhibited(
            onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
          ),
        );
      }),
    ];

    if (_searchQuery.isNotEmpty) {
      allActivities = allActivities.where((activity) {
        String label = (activity as GestureDetector).child is Column
            ? ((activity.child as Column).children.last as Text)
                .data!
                .toLowerCase()
            : '';
        return label.contains(_searchQuery);
      }).toList();
    }

    return allActivities;
  }

  Widget _buildCategory(String categoryName, List<Widget> activities) {
    bool isExpanded = _isExpanded[categoryName] ?? false;
    int visibleItemCount = (activities.length < 7)
        ? activities.length
        : (isExpanded ? activities.length : 7);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Center(
          child: Text(
            categoryName,
            style: const TextStyle(
              fontSize: 20,
            ),
          ),
        ),
        SizedBox(height: SizeConfig.screenH! * 0.02),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 4,
            crossAxisSpacing: 10.0,
            mainAxisSpacing: 20.0,
          ),
          itemCount: visibleItemCount + 1, // Add 1 for "More" icon
          itemBuilder: (context, index) {
            if (index == visibleItemCount) {
              return _buildActivityIcon(
                isExpanded ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                isExpanded ? 'Show Less' : 'More',
                isMore: true,
                onTap: () {
                  setState(() {
                    _isExpanded[categoryName] = !isExpanded;
                  });
                },
              );
            }
            return activities[index];
          },
        ),
        SizedBox(height: SizeConfig.screenH! * 0.02),
      ],
    );
  }

  Widget _buildActivityIcon(IconData icon, String label,
      {bool isMore = false, VoidCallback? onTap}) {
    bool isSelected = _selectedActivities.contains(label);
    return GestureDetector(
      onLongPress: () {
        setState(() {
          _selectionMode = true;
          _selectedActivities.add(label);
        });
      },
      onTap: _selectionMode ? () => _toggleSelection(label) : onTap,
      child: Column(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                width: SizeConfig.screenW! * 0.14,
                height: SizeConfig.screenH! * 0.065,
                decoration: BoxDecoration(
                  color: isSelected
                      ? Colors.green.withOpacity(0.5)
                      : const Color.fromRGBO(90, 38, 101, 0.7),
                  borderRadius: BorderRadius.circular(16.0),
                ),
                child: Icon(
                  icon,
                  size: 32.0,
                  color: Colors.white,
                ),
              ),
              if (isSelected)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 20.0,
                  ),
                ),
            ],
          ),
          SizedBox(height: SizeConfig.screenH! * 0.002),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Text(
              label,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.black, fontSize: 12.0),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(TextEditingController _searchController) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24.0),
      child: TextField(
        controller: _searchController,
        autofocus: false,
        onChanged: (query) {
          setState(() {
            _searchQuery = query.toLowerCase();
          });
        },
        decoration: InputDecoration(
          hintText: 'Search activities',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
          ),
        ),
      ),
    );
  }

  void _toggleSelection(String label) {
    setState(() {
      if (_selectedActivities.contains(label)) {
        _selectedActivities.remove(label);
        if (_selectedActivities.isEmpty) _selectionMode = false;
      } else {
        _selectedActivities.add(label);
      }
    });
  }

  void _clearSelection() {
    setState(() {
      _selectionMode = false;
      _selectedActivities.clear();
    });
  }

  Future<void> _saveResidentCareActivity(ResidentCareActivity activity) async {
    try {
      activity.residentId = widget.residentId;
      await ActivityService().sendResidentCareActivity(activity);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Activity saved successfully!')),
      );
    } on AccessDeniedException {
      showAccessDeniedSnackBar(context);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to save activity: ${e.toString()}')),
      );
    }
  }

  Map<String, Widget> get activityDialogs => {
        //Personal Care
        'Wash Clothes': WashClothes(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Change Clothes': ChangeClothes(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Nail Care': NailCare(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Oral Hygiene': OralHygiene(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Cream': Cream(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Hair Wash': HairWash(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Wash Hands': WashHands(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Bath': Bath(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Shave': Shave(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Makeup': MakeUp(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Cleaning': Cleaning(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Shower': Shower(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Check Eye': CheckEye(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Footwear': Footwear(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Catheter Care': CatheterCare(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Check Ears': CheckEars(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Menstrual Care': MenstrualCycle(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Check Hearing Aid': CheckHearingAid(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Check Glasses': CheckGlasses(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        //Personal Safety

        'Check OK': CheckOK(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Change Bed': ChangeBed(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Check Chair': CheckChair(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Check Mattress': CheckMattress(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Check Wheelchair': CheckWheelChair(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Check Curtains': AdjustCurtains(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Check Room': CheckRoom(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Fridge Temperature': FridgeTemperature(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Room Temperature': RoomTemperature(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Check Footwear': Footwear(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Check Equipment': CheckEquipment(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Check Action Mat': CheckActionMat(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Check Pendant Alarm': CheckPendantAlarm(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Tidied Wardrobe': TidiedWardrobe(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Check WheelChair Belt': CheckWheelChairBelt(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Check Sensor': CheckSensor(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Check Bedrails': CheckBedRails(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Check Bin Bags': CheckBinBags(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),

        //Nutrition Eating
        'Tea': Tea(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Lunch': Lunch(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Supper': Supper(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Breakfast': Breakfast(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Soup': Soup(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Jelly': Jelly(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Ice Lolly': IceLolly(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Pudding': Pudding(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Drink': Drink(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Water': Water(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Juice': Juice(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Alcoholic Drink': AlcoholicDrink(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Wine': Wine(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Food Order': FoodOrder(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Milk': Milk(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Snack': Snack(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Milkshake': MilkShake(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Coffee': Coffee(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Thick Drink': ThickDrink(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),

        //Activities
        'Armchair Exercise': ArmChairExersice(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Art and Craft': ArtandCraft(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Ball Games': BallGames(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'BBQ': BBQ(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Birthday': Birthday(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Bowling': Bowling(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Cooking': Cooking(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Crossword': Crossword(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Driving': Driving(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Entertainment': Entertainment(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Exercise': Exercise(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Swimming': Swimming(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Board Games': BoardGames(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Film': Film(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Church': Church(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Day Centre': DayCentre(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Dominoes': Dominoes(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Fete': Fete(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Game Console': GameConsole(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Drama': Drama(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Played Games': PlayedGames(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Knitting': Knitting(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Massage': Massage(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Music': Music(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'House Work': HouseWork(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Gardening': Gardening(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Partying': Partying(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Horse Riding': HorseRiding(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Outing': Outing(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Pets': Pets(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Quiz': Quiz(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Pub': Pub(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Relaxation': Relaxation(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Reading': Reading(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Reminiscence': Reminiscence(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Community': Community(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Shopping': Shopping(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Therapeutic': Therapeutic(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Sensory': Sensory(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Singing': Singing(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Theatre': Theatre(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Visit Relatives': VisitRelatives(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Visitor': Visitor(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Walked Outside': WalkedOutside(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Stayed in Room': StayedInRoom(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Eat Outside': EatOutside(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Hair Dresser': HairDresser(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Sitting Room': SittingRoom(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Cafe': Cafe(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),

        //Communication
        'Chat': ChatWidget(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Read Letter': ReadLetter(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Newspaper': NewsPaper(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Email': Email(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Bell': Bell(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Phone': Phone(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Can\'t Communicate': CantCommunicate(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Mentoring': Mentoring(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Write Letter': WriteLetter(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),

        //Toileting
        'Wet Clothes': WetClothes(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Soiled Clothes': SoiledClothes(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Toilet Help': ToiletHelp(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Bed Pan': BedPan(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Urine Bottle': UrineBottle(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Commode': Commode(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Pad Check': PadCheck(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),

        //Mobility
        'Into Bed': IntoBed(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Out of Bed': OutOfBed(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Walk': Walk(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Elevate Legs': ElevateLegs(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Stairs': Stairs(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Into Chair': IntoChair(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Up from Chair': UpFromChair(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Standing Hoist': StandingHoist(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Handing Belt': HandingBelt(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Fall': Fall(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Hoist 2 People': HoistTwoPeople(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Moved': Moved(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),

        //Medication
        'Medication': Medication(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Doctor': Doctor(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Nurse': Nurse(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Professor': Professor(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Sick': Sick(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Massage': Massage(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'In Pain': InPain(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Call Doctor': CallDoctor(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Ambulance': Ambulance(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Wound': Wound(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Foot Splint': FootSplint(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Blood Sugar': BloodSugar(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Blood Pressure': BloodPressure(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Corona Virus': CoronaVirus(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Pulse': Pulse(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'PRN Medication': PRNMedication(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Hand Splint': HandSplint(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Blood INR': BloodINR(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Specialist': Specialist(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Heat Pack': HeatPack(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Compress': Compress(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Skin Integrity': SkinIntegrity(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Vitals': Vitals(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Conscious': Conscious(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'News': News(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Ear Syringe': EarSyringe(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Insulin': Insulin(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'IV Line Flush': IVLineFlush(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Multistrix': Multistrix(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Blood Test': BloodTest(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Neuro Obs': NeuroObs(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Soft Sign': SoftSign(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Pap Therapy': PAPTherapy(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Oxygen Fever': OxygenFever(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'New Catheter': NewCatheter(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Washout': Washout(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Add Bag': AddBag(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Cleaned': Cleaned(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'PEF': PEF(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Epileptic Seizure': EpilepticSeizure(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Stoma': Stoma(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Tracheostoma': Tracheostoma(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Hospital': Hospital(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Clinic': Clinic(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Enema': Enema(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Suction': Suction(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Sample': Sample(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),

        //Sleep
        'Pad Change': PadChange(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Check Sleep': CheckSleep(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),

        //Emotional
        'Upset': Upset(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Agitated': Agitated(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Confused': Confused(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Pacing': Pacing(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Hallucination': Hallucination(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Repetitive': Repetitive(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Wandering': Wandering(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Social Worker': SocialWorker(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Staff Supported': StaffSupported(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Challenging': Challenging(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Paranoid': Paranoid(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
        'Disinhibited': Disinhibited(
          onSave: (activity) async {
                    await ActivityService().sendResidentCareActivity(activity);
                    // Optionally show a snackbar or update state
                  }, staffList: staffList, residentUUID: widget.residentUUID
        ),
      };

  void _openSelectedActivities() {
    for (String activityName in _selectedActivities) {
      // Check if the activity name exists in the map
      if (activityDialogs.containsKey(activityName)) {
        showDialog(
          context: context,
          builder: (context) => activityDialogs[activityName]!,
        );
      }
    }
    _clearSelection();
  }
}
