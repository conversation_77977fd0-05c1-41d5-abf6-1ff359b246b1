import 'package:carerez/components/drawer.dart';
import 'package:carerez/utils/screen.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../routes/router_constants.dart';
import '../../../blocs/resident/resident_bloc.dart';
import '../../../blocs/resident/resident_event.dart';
import '../../../blocs/resident/resident_state.dart';
import '../../../services/resident_service.dart';
import '../../../models/diet_plan.dart';
import '../../../models/meal.dart';

class ResidentDietPlan extends StatefulWidget {
  final String residentId;
  final String residentUUID;
  const ResidentDietPlan({super.key, required this.residentId, required this.residentUUID});

  @override
  State<ResidentDietPlan> createState() => _ResidentDietPlanState();
}

class _ResidentDietPlanState extends State<ResidentDietPlan> with TickerProviderStateMixin {
  final _scaffoldKey = GlobalKey<ScaffoldState>();
  late ResidentBloc _residentBloc;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _residentBloc = ResidentBloc(ResidentService());
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    Future.microtask(() {
      _residentBloc.add(FetchResidentDietPlan(widget.residentId));
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _residentBloc.close();
    super.dispose();
  }

  // Map meal names to icons and colors
  Map<String, dynamic> _getMealIconAndColor(String mealName) {
    final mealLower = mealName.toLowerCase();
    if (mealLower.contains('breakfast')) {
      return {'img':'assets/breakfast.png', 'color': const Color(0xFFFF9500), 'gradient': [const Color(0xFFFF9500), const Color(0xFFFFB347)]};
    } else if (mealLower.contains('lunch')) {
      return {'img':'assets/lunch.png', 'color': const Color(0xFFFF9500), 'gradient': [const Color(0xFFFF9500), const Color(0xFFFFB347)]};
    } else if (mealLower.contains('dinner')) {
      return {'img':'assets/dinner.png', 'color': const Color(0xFFFF9500), 'gradient': [const Color(0xFFFF9500), const Color(0xFFFFB347)]};
    } else if (mealLower.contains('snack')) {
      return {'img': 'assets/snack.png', 'color': const Color(0xFFFF9500), 'gradient': [const Color(0xFFFF9500), const Color(0xFFFFB347)]};
    } else {
      return {'img':'assets/diet.png', 'color': const Color(0xFFFF9500), 'gradient': [const Color(0xFFFF9500), const Color(0xFFFFB347)]};
    }
  }

  Widget _buildMealCard(DietPlan plan, List<Meal> meals, int index) {
    final mealData = _getMealIconAndColor(plan.name);
    final bool isEmpty = meals.isEmpty;

    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - _fadeAnimation.value)),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              margin: EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 8,
              ).copyWith(
                top: index == 0 ? 20 : 8,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),

              ),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: mealData['color'].withOpacity(0.9),
                    width: 1,
                  ),
                ),
                child: Theme(
                  data: Theme.of(context).copyWith(
                    dividerColor: Colors.transparent,
                    expansionTileTheme: const ExpansionTileThemeData(
                      tilePadding: EdgeInsets.zero,
                      childrenPadding: EdgeInsets.zero,
                    ),
                  ),
                  child: ExpansionTile(
                    tilePadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                    childrenPadding: const EdgeInsets.only(bottom: 16),
                    leading: Image.asset(
                      mealData['img'],

                      width: 50,
                      height: 50,
                      fit: BoxFit.cover,
                    ),
                    title: Text(
                      plan.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                        color: Color(0xFF1D1D1F),
                      ),
                    ),
                    subtitle: Container(
                      margin: const EdgeInsets.only(top: 4),
                      child: Row(
                        children: [
                          Icon(
                            HugeIcons.strokeRoundedClock01,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 6),
                          Text(
                            '${plan.startTime.substring(0, 5)} - ${plan.endTime.substring(0, 5)}',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: isEmpty ? Colors.red.withOpacity(0.1) : mealData['color'].withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              isEmpty ? 'No meals' : '${meals.length} meal${meals.length > 1 ? 's' : ''}',
                              style: TextStyle(
                                color: isEmpty ? Colors.red : mealData['color'],
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    trailing: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: mealData['color'].withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.keyboard_arrow_down,
                        color: mealData['color'],
                        size: 20,
                      ),
                    ),
                    children: [
                      if (isEmpty)
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 24),
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(color: Colors.grey[200]!),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                HugeIcons.strokeRoundedInformationCircle,
                                color: Colors.grey[500],
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  'No meals planned for this time slot',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      else
                        ...meals.asMap().entries.map((entry) {
                          final mealIndex = entry.key;
                          final meal = entry.value;
                          return Container(
                            margin: EdgeInsets.symmetric(horizontal: 24).copyWith(
                              bottom: mealIndex == meals.length - 1 ? 0 : 12,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(color: Colors.grey[200]!),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.04),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    width: 48,
                                    height: 48,
                                    decoration: BoxDecoration(
                                      color: mealData['color'].withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Icon(
                                      HugeIcons.strokeRoundedNaturalFood,
                                      color: mealData['color'],
                                      size: 24,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Text(
                                                meal.name,
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 16,
                                                  color: Color(0xFF1D1D1F),
                                                ),
                                              ),
                                            ),
                                            if (meal.isActive)
                                              Container(
                                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                                decoration: BoxDecoration(
                                                  color: Colors.green.withOpacity(0.1),
                                                  borderRadius: BorderRadius.circular(8),
                                                ),
                                                child: const Row(
                                                  mainAxisSize: MainAxisSize.min,
                                                  children: [
                                                    Icon(
                                                      Icons.check_circle,
                                                      color: Colors.green,
                                                      size: 16,
                                                    ),
                                                    SizedBox(width: 4),
                                                    Text(
                                                      'Active',
                                                      style: TextStyle(
                                                        color: Colors.green,
                                                        fontSize: 12,
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                          ],
                                        ),
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            _buildInfoChip(
                                              icon: HugeIcons.strokeRoundedFire,
                                              label: '${meal.totalCal} cal',
                                              color: Colors.orange,
                                            ),
                                            const SizedBox(width: 8),
                                            _buildInfoChip(
                                              icon: HugeIcons.strokeRoundedClock01,
                                              label: meal.time,
                                              color: Colors.blue,
                                            ),
                                          ],
                                        ),
                                        if (meal.note.isNotEmpty) ...[
                                          const SizedBox(height: 8),
                                          Container(
                                            padding: const EdgeInsets.all(12),
                                            decoration: BoxDecoration(
                                              color: Colors.grey[50],
                                              borderRadius: BorderRadius.circular(8),
                                            ),
                                            child: Row(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Icon(
                                                  HugeIcons.strokeRoundedNote,
                                                  size: 16,
                                                  color: Colors.grey[600],
                                                ),
                                                const SizedBox(width: 8),
                                                Expanded(
                                                  child: Text(
                                                    meal.note,
                                                    style: TextStyle(
                                                      color: Colors.grey[700],
                                                      fontSize: 14,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoChip({required IconData icon, required String label, required Color color}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _residentBloc,
      child: Scaffold(
        backgroundColor: const Color(0xFFF8F9FA),
        key: _scaffoldKey,
        drawer: buildDrawer(scaffoldKey: _scaffoldKey),
        appBar: AppBar(
          elevation: 0,
          // backgroundColor: Colors.white,
          backgroundColor: const Color.fromRGBO(90, 38, 101, 0.9),
          leading: IconButton(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                HugeIcons.strokeRoundedMenu01,
                color: Colors.white,
                size: 20,
              ),
            ),
            onPressed: () {
              _scaffoldKey.currentState!.openDrawer();
            },
          ),
          title: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Diet Plan',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
              ),
              Text(
                'Resident Meal Schedule',
                style: TextStyle(
                  color: Color(0xFF8E8E93),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          actions: [
            IconButton(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  HugeIcons.strokeRoundedArrowLeft01,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              onPressed: () {
                GoRouter.of(context).goNamed(RouteConstants.residentPersonalRouteName,
                  pathParameters: {'residentId': widget.residentId, 'residentUUID': widget.residentUUID},
                );
              },
            ),
            const SizedBox(width: 16),
          ],
        ),
        body: BlocBuilder<ResidentBloc, ResidentState>(
          builder: (context, state) {
            if (state is ResidentLoading) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Color.fromRGBO(90, 38, 101, 1)),
                      ),
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'Loading diet plans...',
                      style: TextStyle(
                        color: Color(0xFF8E8E93),
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              );
            } else if (state is ResidentDietPlanLoaded) {
              final List<DietPlan> plans = state.dietPlan;
              final Map<String, List<Meal>> mealsByPlan = state.mealsByDietPlan;

              if (plans.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(30),
                        ),
                        child: Icon(
                          HugeIcons.strokeRoundedNaturalFood,
                          size: 60,
                          color: Colors.grey[400],
                        ),
                      ),
                      const SizedBox(height: 24),
                      const Text(
                        'No Diet Plans Found',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1D1D1F),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'There are no diet plans available for this resident.',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.only(bottom: 20),
                itemCount: plans.length,
                itemBuilder: (context, index) {
                  final plan = plans[index];
                  final meals = mealsByPlan[plan.dietPlanId] ?? [];
                  return _buildMealCard(plan, meals, index);
                },
              );
            } else if (state is ResidentError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.red[50],
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Icon(
                        HugeIcons.strokeRoundedAlert02,
                        size: 60,
                        color: Colors.red[300],
                      ),
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'Something went wrong',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1D1D1F),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 40),
                      child: Text(
                        state.message,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: () {
                        _residentBloc.add(FetchResidentDietPlan(widget.residentId));
                      },
                      icon: const Icon(HugeIcons.strokeRoundedRefresh),
                      label: const Text('Try Again'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color.fromRGBO(90, 38, 101, 1),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            } else {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Icon(
                        HugeIcons.strokeRoundedInformationCircle,
                        size: 60,
                        color: Colors.grey[400],
                      ),
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'No Data Available',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1D1D1F),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'No diet plan data is currently available.',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            }
          },
        ),
      ),
    );
  }
}