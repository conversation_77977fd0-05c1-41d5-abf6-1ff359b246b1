import 'package:flutter/material.dart';
import '../../../models/assigned_medicine.dart';

class MedicineDetailPage extends StatelessWidget {
  final ResidentAssignedMedicine assignedMedicine;
  const MedicineDetailPage({Key? key, required this.assignedMedicine}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final medicine = assignedMedicine.medicine;
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(medicine.medicineName),
        elevation: 0,
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Header Card with Medicine Name and Key Info
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withOpacity(0.8),
                  ],
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      medicine.medicineName,
                      style: theme.textTheme.headlineMedium?.copyWith(
                        color: theme.colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        _buildInfoChip(
                          context,
                          Icons.medication,
                          '${medicine.dosage} ${medicine.dosageUnit?.name ?? ''}',
                        ),
                        const SizedBox(width: 12),
                        _buildInfoChip(
                          context,
                          Icons.category,
                          medicine.medicineType?.name ?? 'Unknown',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Content Cards
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // Basic Information Card
                  _buildCard(
                    context,
                    title: 'Medicine Information',
                    icon: Icons.info_outline,
                    children: [
                      _buildInfoRow(context, 'Usage', medicine.usage),
                      const SizedBox(height: 16),
                      _buildInfoRow(context, 'Side Effects', medicine.sideEffects),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Schedule Card
                  _buildCard(
                    context,
                    title: 'Schedule',
                    icon: Icons.schedule,
                    children: [
                      _buildDateRow(
                        context,
                        'Start Date',
                        assignedMedicine.startDate,
                        Colors.green,
                      ),
                      const SizedBox(height: 12),
                      _buildDateRow(
                        context,
                        'End Date',
                        assignedMedicine.endDate,
                        Colors.orange,
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Times Card
                  if (assignedMedicine.selectTimes.isNotEmpty)
                    _buildCard(
                      context,
                      title: 'Timing Schedule',
                      icon: Icons.access_time,
                      children: [
                        ...assignedMedicine.selectTimes.map((time) =>
                            _buildTimeScheduleItem(context, time)
                        ).toList(),
                      ],
                    ),

                  const SizedBox(height: 16),

                  // Notes Card
                  if (assignedMedicine.notes != null && assignedMedicine.notes!.isNotEmpty)
                    _buildCard(
                      context,
                      title: 'Notes',
                      icon: Icons.note,
                      children: [
                        Text(
                          assignedMedicine.notes!,
                          style: theme.textTheme.bodyMedium,
                        ),
                      ],
                    ),

                  const SizedBox(height: 16),

                  // Attachments Card
                  if (assignedMedicine.medicineAttachments.isNotEmpty)
                    _buildCard(
                      context,
                      title: 'Attachments',
                      icon: Icons.attach_file,
                      children: [
                        ...assignedMedicine.medicineAttachments.map((attachment) =>
                            _buildAttachmentItem(context, attachment)
                        ).toList(),
                      ],
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCard(
      BuildContext context, {
        required String title,
        required IconData icon,
        required List<Widget> children,
      }) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(BuildContext context, IconData icon, String label) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.onPrimary.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: theme.colorScheme.onPrimary.withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: theme.colorScheme.onPrimary,
          ),
          const SizedBox(width: 6),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onPrimary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.primary,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: theme.textTheme.bodyMedium,
        ),
      ],
    );
  }

  Widget _buildDateRow(BuildContext context, String label, DateTime date, Color color) {
    final theme = Theme.of(context);
    final formattedDate = date.toLocal().toString().split(' ')[0];

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.calendar_today,
            color: color,
            size: 20,
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                formattedDate,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTimeScheduleItem(BuildContext context, dynamic time) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.access_time,
              color: theme.colorScheme.primary,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  time.name,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${time.startTime} - ${time.endTime}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                if (time.assignMedicineSelectTime?.dosage != null)
                  Text(
                    'Dosage: ${time.assignMedicineSelectTime!.dosage}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttachmentItem(BuildContext context, Map<String, dynamic> attachment) {
    final theme = Theme.of(context);
    final attachmentName = attachment['attachmentName'] ?? 'Unknown file';

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.attachment,
            color: theme.colorScheme.primary,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              attachmentName,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            color: theme.colorScheme.onSurfaceVariant,
            size: 16,
          ),
        ],
      ),
    );
  }
}