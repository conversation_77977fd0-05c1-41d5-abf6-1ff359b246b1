import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';
import '../../../components/drawer.dart';
import '../../../routes/router_constants.dart';
import '../../../utils/screen.dart';

class MedicineFeedbackPage extends StatefulWidget {
  final String residentId;
  const MedicineFeedbackPage({Key? key, required this.residentId}) : super(key: key);

  @override
  _MedicineFeedbackPageState createState() => _MedicineFeedbackPageState();
}

class _MedicineFeedbackPageState extends State<MedicineFeedbackPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  
  // Sample feedback data - replace with actual data from your backend
  final List<Map<String, dynamic>> feedbackList = [
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        backgroundColor: const Color.fromRGBO(90, 38, 101, 0.9),
        leading: IconButton(
          onPressed: () => _scaffoldKey.currentState!.openDrawer(),
          icon: Icon(HugeIcons.strokeRoundedMenu01, color: Colors.white),
        ),
        title: const Text(
          'Medicine Feedback',
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          IconButton(
            onPressed: () => GoRouter.of(context).goNamed(RouteConstants.homescreenRouteName),
            icon: const Icon(Icons.arrow_back, color: Colors.white),
          ),
        ],
      ),
      drawer: buildDrawer(scaffoldKey: _scaffoldKey),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: feedbackList.length,
        itemBuilder: (context, index) => _buildFeedbackCard(feedbackList[index]),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddFeedbackDialog(context),
        backgroundColor: const Color.fromRGBO(90, 38, 101, 0.9),
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildFeedbackCard(Map<String, dynamic> feedback) {
    return Card(
      color: Colors.white,
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  feedback['medicineName'],
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${feedback['date']} ${feedback['time']}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildRatingBar(feedback['effectiveness']),
            const SizedBox(height: 12),
            if (feedback['sideEffects'].isNotEmpty) ...[
              const Text(
                'Side Effects:',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              Wrap(
                spacing: 8,
                children: (feedback['sideEffects'] as List).map((effect) {
                  return Chip(
                    label: Text(effect),
                    backgroundColor: Colors.red[100],
                  );
                }).toList(),
              ),
            ],
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.mood, size: 20),
                const SizedBox(width: 8),
                Text('Mood: ${feedback['mood']}'),
              ],
            ),
            if (feedback['notes'].isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Notes: ${feedback['notes']}',
                style: const TextStyle(fontStyle: FontStyle.italic),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRatingBar(int rating) {
    return Row(
      children: [
        const Text('Effectiveness: '),
        ...List.generate(5, (index) {
          return Icon(
            index < rating ? Icons.star : Icons.star_border,
            color: Colors.amber,
            size: 20,
          );
        }),
      ],
    );
  }

  void _showAddFeedbackDialog(BuildContext context) {
    String? selectedMedicine;
    int effectiveness = 3;
    List<String> selectedSideEffects = [];
    String mood = 'Neutral';
    final notesController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        title: const Text('Add Medicine Feedback'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(labelText: 'Medicine'),
                value: selectedMedicine,
                items: ['Aspirin', 'Metformin', 'Atorvastatin']
                    .map((medicine) => DropdownMenuItem(
                          value: medicine,
                          child: Text(medicine),
                        ))
                    .toList(),
                onChanged: (value) => selectedMedicine = value,
              ),
              const SizedBox(height: 16),
              const Text('Effectiveness:'),
              Slider(
                value: effectiveness.toDouble(),
                min: 1,
                max: 5,
                divisions: 4,
                label: effectiveness.toString(),
                onChanged: (value) {
                  effectiveness = value.toInt();
                },
              ),
              const SizedBox(height: 16),
              const Text('Side Effects:'),
              Wrap(
                spacing: 8,
                children: ['Headache', 'Nausea', 'Dizziness', 'Fatigue']
                    .map((effect) => FilterChip(
                          color: MaterialStateProperty.all(Colors.grey[100]),
                          label: Text(effect),
                          selected: selectedSideEffects.contains(effect),
                          onSelected: (selected) {
                            if (selected) {
                              selectedSideEffects.add(effect);
                            } else {
                              selectedSideEffects.remove(effect);
                            }
                          },
                        ))
                    .toList(),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(labelText: 'Mood'),
                value: mood,
                items: ['Happy', 'Neutral', 'Sad']
                    .map((m) => DropdownMenuItem(
                          value: m,
                          child: Text(m),
                        ))
                    .toList(),
                onChanged: (value) => mood = value!,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: notesController,
                decoration: const InputDecoration(
                  labelText: 'Notes',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // Add feedback logic here
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color.fromRGBO(90, 38, 101, 0.9),
            ),
            child: const Text('Submit', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}