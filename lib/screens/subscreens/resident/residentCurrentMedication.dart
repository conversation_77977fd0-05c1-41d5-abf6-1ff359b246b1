import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:intl/intl.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../blocs/medication/medication_bloc.dart';
import '../../../blocs/medication/medication_event.dart';
import '../../../blocs/medication/medication_state.dart';
import '../../../models/assigned_medicine.dart';
import '../../../services/medication_service.dart';
import '../../../routes/router_constants.dart';
import '../../../utils/screen.dart'; // For time formatting
import 'medicine_detail_page.dart';

class CurrentMedicationPage extends StatefulWidget {
  final String residentId;
  final String residentUUID;
  const CurrentMedicationPage({Key? key, required this.residentId, required this.residentUUID})
      : super(key: key);
  @override
  _CurrentMedicationPageState createState() => _CurrentMedicationPageState();
}

class _CurrentMedicationPageState extends State<CurrentMedicationPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    // Fetch medications when the widget is initialized
    context.read<MedicationBloc>().add(FetchMedications(residentId: widget.residentUUID));
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => MedicationBloc(MedicationService())
        ..add(FetchMedications(residentId: widget.residentUUID)),
      child: Scaffold(
        key: _scaffoldKey,
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Color.fromRGBO(90, 38, 101, 0.9),
          leading: IconButton(
              onPressed: () {
                _scaffoldKey.currentState!.openDrawer();
              },
              icon: Icon(HugeIcons.strokeRoundedMenu01, color: Colors.white)),
          actions: [
            IconButton(
              onPressed: () {
                GoRouter.of(context).goNamed(RouteConstants.homescreenRouteName);
              },
              icon: Icon(Icons.arrow_back, color: Colors.white),
            ),
          ],
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: BlocBuilder<MedicationBloc, MedicationState>(
            builder: (context, state) {
              if (state is MedicationLoading) {
                return Center(child: CircularProgressIndicator());
              } else if (state is MedicationError) {
                return Center(child: Text('Error: \\${state.message}'));
              } else if (state is MedicationsLoaded) {
                final medicines = state.filteredMedications;
                if (medicines.isEmpty) {
                  return Center(child: Text('No medications found.'));
                }
                return ListView.builder(
                  itemCount: medicines.length,
                  itemBuilder: (context, index) {
                    return GestureDetector(
                      onTap: () {
                        GoRouter.of(context).pushNamed(
                          RouteConstants.residentMedicineDetailsRouteName,
                          pathParameters: {'residentId': widget.residentId},
                          extra: medicines[index],
                        );
                      },
                      child: _buildMedicineCard(medicines[index].medicine, index),
                    );
                  },
                );
              }
              return Center(child: Text('No data available.'));
            },
          ),
        ),
      ),
    );
  }

    Widget _buildMedicineCard(Medicine medicine, int index) {
      return Card(
        color: Colors.white,
        elevation: 3,
        margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: Colors.purple.withOpacity(0.15),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row - More compact
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.purple.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.medication,
                      size: 20,
                      color: Colors.purple,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          medicine.medicineName,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[800],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (medicine.medicineType != null) ...[
                          const SizedBox(height: 2),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.blue.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              medicine.medicineType!.name,
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                                color: Colors.blue[700],
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: Icon(Icons.edit, color: Colors.blue[600], size: 18),
                        onPressed: () {
                          // Handle edit action
                        },
                        padding: const EdgeInsets.all(4),
                        constraints: const BoxConstraints(minWidth: 28, minHeight: 28),
                      ),
                      IconButton(
                        icon: Icon(Icons.delete, color: Colors.red[600], size: 18),
                        onPressed: () {
                          // Handle delete action
                        },
                        padding: const EdgeInsets.all(4),
                        constraints: const BoxConstraints(minWidth: 28, minHeight: 28),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Medicine Details - More compact
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    _buildDetailRow(Icons.local_pharmacy, 'Dosage',
                        '${medicine.dosage}${medicine.dosageUnit != null ? ' ${medicine.dosageUnit!.name}' : ''}'),
                    const SizedBox(height: 4),
                    _buildDetailRow(Icons.info_outline, 'Usage',
                        medicine.usage, isLongText: true),
                    if (medicine.description.isNotEmpty) ...[
                      const SizedBox(height: 4),

                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    Widget _buildDetailRow(IconData icon, String label, String value, {bool isLongText = false}) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 14, color: Colors.grey[600]),
          const SizedBox(width: 6),
          Text(
            '$label: ',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[800],
                height: isLongText ? 1.2 : 1.0,
              ),
              maxLines: isLongText ? 2 : 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      );
    }

    String _formatDate(DateTime date) {
      return '${date.day}/${date.month}/${date.year}';
    }
}
