import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';
import '../../../blocs/medication/medication_bloc.dart';
import '../../../blocs/medication/medication_event.dart';
import '../../../blocs/medication/medication_state.dart';
import '../../../components/drawer.dart';
import '../../../models/assigned_medicine.dart';
import '../../../routes/router_constants.dart';
import '../../../services/medication_service.dart';

class ResidentMedicationHistoryPage extends StatefulWidget {
  final String residentId;
  final String residentUUID;
  const ResidentMedicationHistoryPage({Key? key, required this.residentId,required this.residentUUID}) : super(key: key);

  @override
  State<ResidentMedicationHistoryPage> createState() => _ResidentMedicationHistoryPageState();
}

class _ResidentMedicationHistoryPageState extends State<ResidentMedicationHistoryPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => MedicationBloc(MedicationService())
        ..add(FetchMedications(residentId: widget.residentUUID)),
      child: Scaffold(
        key: _scaffoldKey,
        drawer: buildDrawer(scaffoldKey: _scaffoldKey),
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Color.fromRGBO(90, 38, 101, 1),
          title: Row(
            children: [
              Icon(HugeIcons.strokeRoundedMedicalFile,
                  color: Colors.white, size: 24),
              SizedBox(width: 12),
              Text(
                'Medication History',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          leading: IconButton(
            onPressed: () {
              _scaffoldKey.currentState!.openDrawer();
            },
            icon: Icon(HugeIcons.strokeRoundedMenu01, color: Colors.white),
          ),
          actions: [
            IconButton(
              onPressed: () {
                GoRouter.of(context).goNamed(RouteConstants.residentPersonalRouteName,
                    pathParameters: {'residentId': widget.residentId,'residentUUID': widget.residentUUID});
              },
              icon: Icon(HugeIcons.strokeRoundedArrowTurnBackward, color: Colors.white),
            ),
            SizedBox(width: 8),
          ],
        ),
        body: BlocBuilder<MedicationBloc, MedicationState>(
          builder: (context, state) {
            if (state is MedicationLoading) {
              return const Center(child: CircularProgressIndicator());
            } else if (state is MedicationError) {
              return Center(child: Text('Error: ${state.message}'));
            } else if (state is MedicationsLoaded) {
              final medications = state.medications;
              if (medications.isEmpty) {
                return const Center(child: Text('No medication history found.'));
              }
              return ListView.builder(
                itemCount: medications.length,
                itemBuilder: (context, index) {
                  final med = medications[index];
                  return Card(
                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: ListTile(
                      title: Text(med.medicine.medicineName),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Dosage: ${med.medicine.dosage}'),
                          Text('Start: ${med.startDate.toLocal().toString().split(' ')[0]}'),
                          Text('End: ${med.endDate.toLocal().toString().split(' ')[0]}'),
                          if (med.notes != null && med.notes!.isNotEmpty)
                            Text('Notes: ${med.notes!}'),
                        ],
                      ),
                      trailing: med.isActive
                          ? const Icon(Icons.check_circle, color: Colors.green)
                          : const Icon(Icons.history, color: Colors.grey),
                    ),
                  );
                },
              );
            }
            return const Center(child: Text('No data available.'));
          },
        ),
      ),
    );
  }
}

