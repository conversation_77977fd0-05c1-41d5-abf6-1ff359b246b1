import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../blocs/finance/resident_finance_event.dart';
import '../../../blocs/finance/resident_finance_state.dart';
import '../../../components/drawer.dart';
import '../../../blocs/finance/resident_finance_basic_bloc.dart';
import '../../../blocs/finance/resident_budget_transaction_bloc.dart';
import '../../../models/resident_finance_basic.dart';
import '../../../models/resident_budget_transaction.dart';
import '../../../routes/router_constants.dart';
import 'dart:convert';

import '../../../utils/screen.dart'; // For JSON decoding

class Residentfinance extends StatefulWidget {
  final String residentId;
  final String residentUUID;
  const Residentfinance({super.key, required this.residentId, required this.residentUUID});

  @override
  State<Residentfinance> createState() => _ResidentfinanceState();
}

class _ResidentfinanceState extends State<Residentfinance>
    with SingleTickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    // Fetch data from blocs
    context.read<ResidentFinanceBasicBloc>().add(FetchResidentFinanceBasic(widget.residentUUID));
    context.read<ResidentBudgetTransactionBloc>().add(FetchResidentBudgetTransactions(widget.residentUUID));
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      key: _scaffoldKey,
      drawer: buildDrawer(scaffoldKey: _scaffoldKey),
      appBar: AppBar(
        backgroundColor: const Color.fromRGBO(90, 38, 101, 0.9),
        leading: IconButton(
          onPressed: () {
            _scaffoldKey.currentState!.openDrawer();
          },
          icon: const Icon(HugeIcons.strokeRoundedMenu01, color: Colors.white),
        ),
        actions: [
          IconButton(
            onPressed: () {
              GoRouter.of(context).goNamed(RouteConstants.residentPersonalRouteName,
                  pathParameters: {'residentId': widget.residentId, 'residentUUID': widget.residentUUID});
            },
            icon: const Icon(Icons.arrow_back, color: Colors.white),
          ),
        ],
      ),
      body: BlocBuilder<ResidentFinanceBasicBloc, ResidentFinanceBasicState>(
        builder: (context, financeState) {
          return BlocBuilder<ResidentBudgetTransactionBloc, ResidentBudgetTransactionState>(
            builder: (context, transactionState) {
              // Handle loading and error states
              if (financeState is ResidentFinanceBasicLoading || transactionState is ResidentBudgetTransactionLoading) {
                return Center(child: CircularProgressIndicator());
              }
              if (financeState is ResidentFinanceBasicError) {
                return Center(child: Text('Error: ${financeState.message}'));
              }
              if (transactionState is ResidentBudgetTransactionError) {
                return Center(child: Text('Error: ${transactionState.message}'));
              }
              // Loaded states
              ResidentFinanceBasic? financeBasic;
              List<ResidentBudgetTransaction> transactions = [];
              if (financeState is ResidentFinanceBasicLoaded) {
                financeBasic = financeState.financeBasic;
              }
              if (transactionState is ResidentBudgetTransactionLoaded) {
                transactions = transactionState.transactions;
              }
              // UI with real data
              return SingleChildScrollView(
                child: IntrinsicHeight(
                  child: Column(
                    children: [
                      Container(
                        height: SizeConfig.screenH! * 0.28,
                        child: Stack(alignment: Alignment.topCenter, children: [
                          Container(
                              width: double.infinity,
                              height: SizeConfig.screenH! * 0.20,
                              color: Color(0xFF3E3546),
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.stretch,
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(16.0),
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          SizedBox(height: 16),
                                          Text(
                                            financeBasic != null ? '\$${financeBasic.balance}' : '-',
                                            style: TextStyle(
                                              fontSize: 40,
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          SizedBox(height: 4),
                                          Text(
                                            'Cash Balance',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 16,
                                            ),
                                          ),
                                          SizedBox(height: 16),
                                        ],
                                      ),
                                    ),
                                  ])),
                          Positioned(
                            top: SizeConfig.screenH! * 0.15,
                            child: Container(
                              margin: EdgeInsets.all(8),
                              height: SizeConfig.screenH! * 0.1,
                              width: SizeConfig.screenW! * 0.95,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.all(Radius.circular(20)),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.withOpacity(0.2),
                                    spreadRadius: 2,
                                    blurRadius: 5,
                                    offset: Offset(0, 3),
                                  ),
                                ],
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                children: [
                                  _buildBalanceInfo('Reserve Fund', financeBasic != null ? '\$${financeBasic.reservedFunds}' : '-', Colors.black, Colors.black),
                                  _buildBalanceInfo('Voucher Held', financeBasic != null ? '\$${financeBasic.voucherAmount}' : '-', Colors.black, Colors.black),
                                  _buildBalanceInfo('Budget', financeBasic != null ? '\$${financeBasic.budgetAmount}' : '-', Colors.black, Colors.black),
                                ],
                              ),
                            ),
                          ),
                        ]),
                      ),
                      Container(
                        margin: EdgeInsets.all(8),
                        height: SizeConfig.screenH! * 0.1,
                        width: SizeConfig.screenW! * 0.95,
                        decoration: BoxDecoration(
                          color: Color.fromRGBO(90, 38, 101, 0.7),
                          borderRadius: BorderRadius.all(Radius.circular(20)),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.2),
                              spreadRadius: 2,
                              blurRadius: 5,
                              offset: Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _buildBalanceInfo('Total Expenses', financeBasic != null ? '\$${financeBasic.totalExpenses}' : '-', Colors.white, Colors.white),
                            _buildBalanceInfo('Budget', financeBasic != null ? '\$${financeBasic.budgetAmount}' : '-', Colors.white, Colors.white),
                            _buildBalanceInfo('Balance', financeBasic != null ? '\$${financeBasic.balance}' : '-', Colors.white, Colors.white),
                          ],
                        ),
                      ),
                      SizedBox(height: 16),
                      TabBar(
                          dividerColor: Color(0xFF3E3546),
                          isScrollable: false,
                          unselectedLabelColor: Color(0xFF3E3546),
                          labelColor: Color(0xFF3E3546),
                          indicatorColor: Color(0xFF3E3546),
                          tabs: [
                            Tab(text: 'Active Reserve Funds'),
                            Tab(text: 'All Transactions'),
                          ],
                          controller: _tabController),
                      Container(
                        height: MediaQuery.of(context).size.height - 300,
                        child: TabBarView(
                          controller: _tabController,
                          children: [
                            buildReserveFundsTable(transactions),
                            buildAllTransactionsTable(transactions),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget buildReserveFundsTable(List<ResidentBudgetTransaction> transactions) {
    // Show only transactions with category name containing 'reserve' (case-insensitive)
    final reserveFundTxs = transactions.where((tx) =>
        tx.category.name.toLowerCase().contains('reserve')).toList();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header Section
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF1E88E5),
                  const Color(0xFF42A5F5),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    HugeIcons.strokeRoundedSafe,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Reserve Funds',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      '${reserveFundTxs.length} transactions',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withOpacity(0.8),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Reserved',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Table Content
          if (reserveFundTxs.isEmpty)
            Container(
              padding: const EdgeInsets.all(40),
              child: Column(
                children: [
                  Icon(
                    HugeIcons.strokeRoundedInformationCircle,
                    size: 48,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No Reserve Fund Transactions',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'No transactions found for reserve funds',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            )
          else
            Column(
              children: [
                // Table Header
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    border: Border(
                      bottom: BorderSide(color: Colors.grey[200]!, width: 1),
                    ),
                  ),
                  child: const Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Date',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            color: Color(0xFF374151),
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Amount',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            color: Color(0xFF374151),
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Category',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            color: Color(0xFF374151),
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Type',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            color: Color(0xFF374151),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Table Rows
                ...reserveFundTxs.asMap().entries.map((entry) {
                  final index = entry.key;
                  final tx = entry.value;
                  final isEven = index % 2 == 0;

                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    decoration: BoxDecoration(
                      color: isEven ? Colors.white : Colors.grey[25],
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.grey[100]!,
                          width: 0.5,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Row(
                            children: [
                              Icon(
                                HugeIcons.strokeRoundedCalendar03,
                                size: 16,
                                color: Colors.grey[500],
                              ),
                              const SizedBox(width: 8),
                              // FIXED: Removed Flexible wrapper, just use Text with overflow
                              Text(
                                _formatDate(tx.transactionDate),
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Color(0xFF374151),
                                  fontWeight: FontWeight.w500,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            '\$${tx.amount.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: tx.isAdd ? Colors.green[700] : Colors.red[700],
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: const Color(0xFF1E88E5).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              tx.category.name,
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: _getCategoryColor(tx.category.name),
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: tx.isAdd
                                  ? Colors.green.withOpacity(0.1)
                                  : Colors.red.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  tx.isAdd
                                      ? HugeIcons.strokeRoundedArrowUp01
                                      : HugeIcons.strokeRoundedArrowDown01,
                                  size: 14,
                                  color: tx.isAdd ? Colors.green[700] : Colors.red[700],
                                ),
                                const SizedBox(width: 4),
                                // FIXED: Removed Flexible wrapper, just use Text with overflow
                                Text(
                                  tx.isAdd ? 'Credit' : 'Debit',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: tx.isAdd ? Colors.green[700] : Colors.red[700],
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ],
            ),
        ],
      ),
    );
  }

  Widget buildAllTransactionsTable(List<ResidentBudgetTransaction> transactions) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.purple.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header Section
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF7B1FA2),
                  const Color(0xFF9C27B0),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    HugeIcons.strokeRoundedTransaction,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'All Transactions',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      '${transactions.length} total transactions',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withOpacity(0.8),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Complete',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Summary Stats
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(
                bottom: BorderSide(color: Colors.grey[200]!, width: 1),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Credits',
                    '\$${transactions.where((tx) => tx.isAdd).fold(0.0, (sum, tx) => sum + tx.amount).toStringAsFixed(2)}',
                    Colors.green,
                    HugeIcons.strokeRoundedArrowUp01,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Total Debits',
                    '\$${transactions.where((tx) => !tx.isAdd).fold(0.0, (sum, tx) => sum + tx.amount).toStringAsFixed(2)}',
                    Colors.red,
                    HugeIcons.strokeRoundedArrowDown01,
                  ),
                ),
              ],
            ),
          ),

          // Table Content
          if (transactions.isEmpty)
            Container(
              padding: const EdgeInsets.all(40),
              child: Column(
                children: [
                  Icon(
                    HugeIcons.strokeRoundedInformationCircle,
                    size: 48,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No Transactions Found',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'No transaction history available',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            )
          else
            Column(
              children: [
                // Table Header
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    border: Border(
                      bottom: BorderSide(color: Colors.grey[200]!, width: 1),
                    ),
                  ),
                  child: const Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Date',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            color: Color(0xFF374151),
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Amount',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            color: Color(0xFF374151),
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Category',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            color: Color(0xFF374151),
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Type',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            color: Color(0xFF374151),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Table Rows
                ...transactions.asMap().entries.map((entry) {
                  final index = entry.key;
                  final tx = entry.value;
                  final isEven = index % 2 == 0;

                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    decoration: BoxDecoration(
                      color: isEven ? Colors.white : Colors.grey[25],
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.grey[100]!,
                          width: 0.5,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Row(
                            children: [
                              Icon(
                                HugeIcons.strokeRoundedCalendar03,
                                size: 16,
                                color: Colors.grey[500],
                              ),
                              const SizedBox(width: 5),
                              // FIXED: Removed unnecessary Flexible wrapper
                              Text(
                                _formatDate(tx.transactionDate),
                                style: const TextStyle(
                                  fontSize: 10,
                                  color: Color(0xFF374151),
                                  fontWeight: FontWeight.w500,
                                ),
                                overflow: TextOverflow.fade,
                                maxLines: 1,
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          flex:1,
                          child: Text(
                            '\$${tx.amount.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: tx.isAdd ? Colors.green[700] : Colors.red[700],
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getCategoryColor(tx.category.name).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              tx.category.name,
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: _getCategoryColor(tx.category.name),
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: tx.isAdd
                                  ? Colors.green.withOpacity(0.1)
                                  : Colors.red.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  tx.isAdd
                                      ? HugeIcons.strokeRoundedArrowUp01
                                      : HugeIcons.strokeRoundedArrowDown01,
                                  size: 14,
                                  color: tx.isAdd ? Colors.green[700] : Colors.red[700],
                                ),
                                const SizedBox(width: 4),
                                // FIXED: Removed unnecessary Flexible wrapper
                                Text(
                                  tx.isAdd ? 'Credit' : 'Debit',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: tx.isAdd ? Colors.green[700] : Colors.red[700],
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, size: 16, color: color),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  Color _getCategoryColor(String categoryName) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
      Colors.amber,
    ];

    final hash = categoryName.hashCode;
    return colors[hash.abs() % colors.length];
  }

  Widget _buildBalanceInfo(
      String label,
      String amount,
      Color labelColor,
      Color amountColor,
      ) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          amount,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: amountColor,
          ),
        ),
        SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: labelColor,
          ),
        ),
      ],
    );
  }
}