import 'package:carerez/components/drawer.dart';
import 'package:carerez/config/env_config.dart';
import 'package:carerez/utils/screen.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../routes/router_constants.dart';
import '../../services/resident_service.dart';

class Residentpersonalscreen extends StatefulWidget {
  final String residentId;
  final String residentUUID;
  const Residentpersonalscreen(
      {super.key, required this.residentId, required this.residentUUID});

  @override
  State<Residentpersonalscreen> createState() => _ResidentpersonalscreenState();
}

class _ResidentpersonalscreenState extends State<Residentpersonalscreen>
    with SingleTickerProviderStateMixin {
  final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();

  final ResidentService residentService = ResidentService();
  Map<String, dynamic>? resident;

  void onOverviewTap() {
    GoRouter.of(context).goNamed(RouteConstants.residentOverviewRouteName,
        pathParameters: {'residentId': widget.residentId},
        queryParameters: {'residentUUID': widget.residentUUID});
  }

  void onMedicalHistoryTap() {
    GoRouter.of(context).goNamed(RouteConstants.residentMedicalHistoryRouteName,
        pathParameters: {'residentId': widget.residentId},
        queryParameters: {'residentUUID': widget.residentUUID});
  }

  void onDietPlanTap() {
    GoRouter.of(context).goNamed(RouteConstants.residentDietPlanRouteName,
        pathParameters: {'residentId': widget.residentId},
        queryParameters: {'residentUUID': widget.residentUUID});
  }

  // void onSchedulerTap() {
  //   GoRouter.of(context).goNamed(RouteConstants.);
  // }

  void onCareAndActivitiesTap() {
    GoRouter.of(context).goNamed(RouteConstants.residentCareActivitesRouteName,
        pathParameters: {'residentId': widget.residentId},
        queryParameters: {'residentUUID': widget.residentUUID});
  }

  void onMedicationIntakeTap() {
    GoRouter.of(context).goNamed(
        RouteConstants.residentMedicationIntakeRouteName,
        pathParameters: {'residentId': widget.residentId},
        queryParameters: {'residentUUID': widget.residentUUID});
  }

  void onMedicationHistoryTap() {
    GoRouter.of(context).goNamed(RouteConstants.residentMedicationHistoryRouteName,
        pathParameters: {'residentId': widget.residentId},
        queryParameters: {'residentUUID': widget.residentUUID});
  }

  void onMedicationFeedbackTap() {
    GoRouter.of(context).goNamed(
        RouteConstants.residentMedicineFeedbackRouteName,
        pathParameters: {'residentId': widget.residentId},
        queryParameters: {'residentUUID': widget.residentUUID});
  }

  void onPrescribedMedicationTap() {
    GoRouter.of(context).goNamed(
        RouteConstants.residentPrescribedMedicationRouteName,
        pathParameters: {'residentId': widget.residentId},
        queryParameters: {'residentUUID': widget.residentUUID});
  }


  void onFinanceTap() {
    GoRouter.of(context).goNamed(RouteConstants.residentFinanceRouteName,
        pathParameters: {'residentId': widget.residentId},
        queryParameters: {'residentUUID': widget.residentUUID});
  }

  static const personalInfoTabs = [
    {
      'label': 'Overview',
      'icon': Icons.person,
      'onTap': null,
    },
    {
      'label': 'Medical History',
      'icon': Icons.history,
      'onTap': null,
    },
    {
      'label': 'Diet Plan',
      'icon': Icons.restaurant,
      'onTap': null,
    },
    {
      'label': 'Scheduler',
      'icon': Icons.schedule,
      'onTap': null,
    },
    {
      'label': 'Care and Activities',
      'icon': Icons.local_activity,
      'onTap': null,
    },
    {
      'label': 'Finance',
      'icon': Icons.monetization_on_sharp,
      'onTap': null,
    }
  ];

  static const medicationTabs = [
    {
      'label': 'Medication Intake Feedback',
      'icon': Icons.medication,
      'onTap': null,
    },
    {
      'label': 'Prescribed Medication',
      'icon': Icons.medical_services,
      'onTap': null,
    },
    {
      'label': 'Medication History',
      'icon': Icons.medical_services,
      'onTap': null,
    },
  ];

  late TabController _tabController;

  void _loadResidentData() async {
    try {
      final result = await residentService.getResidentById(widget.residentId);
      setState(() {
        resident = result.toJson(); // now it will rebuild with data
      });
    } catch (e) {
      debugPrint('Error fetching resident: $e');
      setState(() {
        resident = null; // optional: handle errors better if needed
      });
    }
  }

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadResidentData();
    // Two tabs: "Personal Info" and "General Medication"
  }

  @override
  void didUpdateWidget(covariant Residentpersonalscreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.residentId != widget.residentId ||
        oldWidget.residentUUID != widget.residentUUID) {
      _loadResidentData();
    } else {
      // Always reload on widget update (for go_router pop)
      _loadResidentData();
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (resident == null) {
      return Scaffold(
        backgroundColor: Colors.white,
        key: _scaffoldKey,
        drawer: buildDrawer(scaffoldKey: _scaffoldKey),
        appBar: AppBar(
          backgroundColor: Color.fromRGBO(90, 38, 101, 0.9),
          leading: IconButton(
              onPressed: () {
                _scaffoldKey.currentState!.openDrawer();
              },
              icon: Icon(HugeIcons.strokeRoundedMenu01, color: Colors.white)),
          actions: [
            IconButton(
              onPressed: () {
                GoRouter.of(context)
                    .goNamed(RouteConstants.homescreenRouteName);
              },
              icon: Icon(Icons.arrow_back, color: Colors.white),
            ),
          ],
        ),
        body: Center(
          child: Column(
            children: [
              Text('Resident details could not be found.'),
              //reload
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Color.fromRGBO(90, 38, 101, 0.9),
                ),
                onPressed: () {
                  _loadResidentData();
                },
                child: Text('Reload'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.white,
      key: _scaffoldKey,
      drawer: buildDrawer(scaffoldKey: _scaffoldKey),
      appBar: AppBar(
        backgroundColor: Color.fromRGBO(90, 38, 101, 0.9),
        leading: IconButton(
            onPressed: () {
              _scaffoldKey.currentState!.openDrawer();
            },
            icon: Icon(HugeIcons.strokeRoundedMenu01, color: Colors.white)),
        actions: [
          IconButton(
            onPressed: () {
              GoRouter.of(context).goNamed(RouteConstants.homescreenRouteName);
            },
            icon: Icon(Icons.arrow_back, color: Colors.white),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            FutureBuilder<Widget>(
              future: _buildResidentInfoCard(resident!),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(child: CircularProgressIndicator());
                } else if (snapshot.hasError) {
                  return Center(child: Text('Error loading resident info'));
                } else {
                  return snapshot.data ?? SizedBox.shrink();
                }
              },
            ),
            SizedBox(height: SizeConfig.screenH! * 0.02),
            TabBar(tabs: [
              Tab(text: 'Personal Info'),
              Tab(text: 'General Medication'),
            ], controller: _tabController),
            SizedBox(
              height: MediaQuery.of(context).size.height - 300,
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildPersonalInfoTab(resident!),
                  _buildGeneralMedicationTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<Widget> _buildResidentInfoCard(Map<String, dynamic> resident) async {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Color.fromRGBO(90, 38, 101, 0.9),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(30),
          bottomRight: Radius.circular(30),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircleAvatar(
                radius: 60,
                backgroundImage: NetworkImage(
                  resident['profileUrl'] ??
                      'https://via.placeholder.com/150', // Fallback image
                ),
              ),
              SizedBox(width: 20),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${resident['firstName']} ${resident['lastName']}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 22,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(height: SizeConfig.screenH! * 0.01),
                  Text(
                    'Code: ${resident['userCode']}',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: SizeConfig.screenH! * 0.02),
          _buildDetailsGrid(resident),
        ],
      ),
    );
  }

  Widget _buildDetailsGrid(Map<String, dynamic> resident) {
    int _calculateAge(dynamic dob) {
      if (dob == null) return 0;
      DateTime dateOfBirth;
      if (dob is String) {
        dateOfBirth = DateTime.tryParse(dob) ?? DateTime.now();
      } else if (dob is DateTime) {
        dateOfBirth = dob;
      } else {
        return 0;
      }
      final today = DateTime.now();
      int age = today.year - dateOfBirth.year;
      if (today.month < dateOfBirth.month ||
          (today.month == dateOfBirth.month && today.day < dateOfBirth.day)) {
        age--;
      }
      return age;
    }

    final details = [
      {'label': 'Gender', 'value': resident['gender'].toString().toUpperCase()},
      {'label': 'Age', 'value': '${_calculateAge(resident['dob'])}'},
      {'label': 'Blood Type', 'value': resident['bloodType']},
      {'label': 'Date of Birth', 'value': resident['dob']},
      {'label': 'Phone', 'value': resident['phoneNumber']},
      {
        'label': 'Patient Status',
        'value': resident['status'].toString().toUpperCase()
      },
    ];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Wrap(
        spacing: 30,
        runSpacing: 20,
        children: details.map((detail) {
          return SizedBox(
            height: SizeConfig.screenH! * 0.08,
            width: MediaQuery.of(context).size.width / 2 - 50,
            child:
                _buildDetailContainer(detail['label']!, detail['value'] ?? '0'),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildDetailContainer(String label, String value) {
    return Container(
      padding: const EdgeInsets.all(12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          SizedBox(height: SizeConfig.screenH! * 0.005),
          Text(
            value,
            style: TextStyle(fontSize: 16, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildGeneralMedicationTab() {
    // Example content for General Medication
    return SingleChildScrollView(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: GridView.builder(
              shrinkWrap:
                  true, // Important to allow the grid to fit the container
              physics:
                  NeverScrollableScrollPhysics(), // Disable GridView scrolling
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4, // Change to your desired number of columns
                crossAxisSpacing: 10,
                mainAxisSpacing: 10,
              ),
              itemCount: medicationTabs.length,
              itemBuilder: (context, index) {
                final tab = medicationTabs[index];
                return GestureDetector(
                  onTap: () {
                    if (tab['label'] == 'Medication Intake Feedback') {
                      onMedicationIntakeTap();
                    } else if (tab['label'] == 'Prescribed Medication') {
                      onPrescribedMedicationTap();
                    } else if (tab['label'] == 'Medication History') {
                      onMedicationHistoryTap();
                    }
                  },
                  child: Card(
                    shadowColor: Colors.black,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15.0),
                    ),
                    color: Colors.white,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            tab['icon'] as IconData,
                            size: 32.0,
                            color: Color.fromRGBO(90, 38, 101, 0.7),
                          ),
                          SizedBox(height: SizeConfig.screenH! * 0.01),
                          Text(
                            tab['label'] as String,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                            style: TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoTab(Map<String, dynamic> resident) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: GridView.builder(
              shrinkWrap:
                  true, // Important to allow the grid to fit the container
              physics:
                  NeverScrollableScrollPhysics(), // Disable GridView scrolling
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4, // Change to your desired number of columns
                crossAxisSpacing: 10,
                mainAxisSpacing: 20,
              ),
              itemCount: personalInfoTabs.length,
              itemBuilder: (context, index) {
                final tab = personalInfoTabs[index];
                return GestureDetector(
                  onTap: () {
                    if (tab['label'] == 'Overview') {
                      onOverviewTap();
                    } else if (tab['label'] == 'Medical History') {
                      onMedicalHistoryTap();
                    } else if (tab['label'] == 'Diet Plan') {
                      onDietPlanTap();
                    } else if (tab['label'] == 'Scheduler') {
                      // onSchedulerTap();
                    } else if (tab['label'] == 'Care and Activities') {
                      onCareAndActivitiesTap();
                    } else if (tab['label'] == 'Finance') {
                      onFinanceTap();
                    }
                  },
                  child: Card(
                    shadowColor: Colors.black,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15.0),
                    ),
                    color: Colors.white,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            tab['icon'] as IconData,
                            size: 32.0,
                            color: Color.fromRGBO(90, 38, 101, 0.7),
                          ),
                          SizedBox(height: SizeConfig.screenH! * 0.01),
                          Text(
                            tab['label'] as String,
                            maxLines: 2,
                            overflow: TextOverflow.clip,
                            textAlign: TextAlign.center,
                            style: TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
