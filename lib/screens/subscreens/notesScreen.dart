import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../components/drawer.dart';
import '../../routes/router_constants.dart';
import '../../utils/screen.dart';

class Notesscreen extends StatefulWidget {
  const Notesscreen({super.key});

  @override
  State<Notesscreen> createState() => _NotesscreenState();
}

class _NotesscreenState extends State<Notesscreen> {
  final _scaffoldKey = GlobalKey<ScaffoldState>();
  final List<Map<String, dynamic>> _notifications = [
    {
      "sender": "Nurse <PERSON>",
      "message":
          "Morning rounds will begin at 8 AM. Ensure that all patient reports are ready.",
      "time": "07:00 AM",
    },
    {
      "sender": "Nurse <PERSON>",
      "message":
          "Dr<PERSON> <PERSON> will be on duty in the ICU from 10 AM to 4 PM today.",
      "time": "09:30 AM",
    },
    {
      "sender": "Nurse <PERSON>",
      "message":
          "Please remind patients in Ward C about their medication changes starting tomorrow.",
      "time": "11:00 AM",
    },
    {
      "sender": "<PERSON> <PERSON>",
      "message":
          "Team, the weekly staff meeting is at 4 PM. Let’s discuss upcoming schedules.",
      "time": "03:00 PM",
    }
  ];

  void _addNewNote(String title, String message) {
    final now = DateTime.now();
    final formattedTime =
        "${now.hour}:${now.minute.toString().padLeft(2, '0')} ${now.hour >= 12 ? 'PM' : 'AM'}";

    setState(() {
      _notifications.add({
        "sender": "Nurse Emily", // Assuming the same sender for new notes
        "message": message,
        "time": formattedTime,
      });
    });
  }

  Future<void> _showAddNoteDialog() async {
    final _titleController = TextEditingController();
    final _messageController = TextEditingController();

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0), // Rounded corners
          ),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Add New Notification',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: SizeConfig.screenH! * 0.02),
                TextField(
                  controller: _titleController,
                  decoration: InputDecoration(
                    labelText: 'Title',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                    filled: true,
                    fillColor: Colors.grey[200],
                  ),
                ),
                SizedBox(height: SizeConfig.screenH! * 0.02),
                TextField(
                  controller: _messageController,
                  decoration: InputDecoration(
                    labelText: 'Message',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                    filled: true,
                    fillColor: Colors.grey[200],
                  ),
                  maxLines: 3,
                ),
                SizedBox(height: SizeConfig.screenH! * 0.02),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    OutlinedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: Colors.grey), // Teal outline
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.0),
                        ),
                      ),
                      child: const Text(
                        'Cancel',
                        style: TextStyle(color: Colors.black),
                      ),
                    ),
                    SizedBox(height: SizeConfig.screenW! * 0.01),
                    ElevatedButton(
                      onPressed: () {
                        if (_titleController.text.isNotEmpty &&
                            _messageController.text.isNotEmpty) {
                          _addNewNote(
                              _titleController.text, _messageController.text);
                          Navigator.of(context).pop();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.0),
                        ),
                      ),
                      child: const Text('Save'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      key: _scaffoldKey,
      drawer: buildDrawer(scaffoldKey: _scaffoldKey),
      appBar: AppBar(
        backgroundColor: Colors.white,
        leading: IconButton(
          icon: Icon(Icons.menu),
          color: Colors.black,
          onPressed: () {
            _scaffoldKey.currentState!.openDrawer();
          },
        ),
        title: Text('Hand Off Notes',
            style: TextStyle(
              color: Colors.black,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.arrow_back),
            onPressed: () {
              GoRouter.of(context).goNamed(RouteConstants.homescreenRouteName);
            },
          ),
        ],
      ),
      body: SafeArea(
        child: ListView.builder(
          itemCount: _notifications.length,
          itemBuilder: (context, index) {
            final notification = _notifications[index];
            return Padding(
              padding:
                  const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
              child: Row(
                mainAxisAlignment:
                    MainAxisAlignment.start, // Align messages to the left
                children: [
                  Expanded(
                    child: Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                      color: Colors
                          .grey[100], // Light grey background for messages
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              notification['sender'],
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF333333),
                              ),
                            ),
                            SizedBox(height: SizeConfig.screenH! * 0.01),
                            Text(
                              notification['message'],
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[800],
                              ),
                            ),
                            SizedBox(height: SizeConfig.screenH! * 0.01),
                            Text(
                              notification['time'],
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddNoteDialog,
        child: const Icon(Icons.send),
        backgroundColor: Colors.white,
      ),
    );
  }
}
