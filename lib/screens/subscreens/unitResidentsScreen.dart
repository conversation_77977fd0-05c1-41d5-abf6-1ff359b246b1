import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';
import '../../blocs/resident/resident_bloc.dart';
import '../../blocs/resident/resident_event.dart';
import '../../blocs/resident/resident_state.dart';
import '../../blocs/unit/unit_bloc.dart';
import '../../blocs/unit/unit_event.dart';
import '../../blocs/unit/unit_state.dart';
import '../../components/drawer.dart';
import '../../models/resident.dart';
import '../../models/unit.dart';
import '../../routes/router_constants.dart';

class UnitResidentsScreen extends StatefulWidget {
  final String unitId;
  final Unit unit;

  const UnitResidentsScreen({Key? key, required this.unitId,required this.unit}) : super(key: key);

  @override
  State<UnitResidentsScreen> createState() => _UnitResidentsScreenState();
}

class _UnitResidentsScreenState extends State<UnitResidentsScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    context.read<UnitBloc>().add(FetchUnitById(widget.unitId));
    context.read<ResidentBloc>().add(FetchResidentsByUnit(widget.unitId));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(240, 240, 240, 1),
      key: _scaffoldKey,
      drawer: buildDrawer(scaffoldKey: _scaffoldKey),
      appBar: AppBar(
        backgroundColor: const Color.fromRGBO(90, 38, 101, 0.9),
        leading: IconButton(
          onPressed: () {
            _scaffoldKey.currentState!.openDrawer();
          },
          icon: const Icon(HugeIcons.strokeRoundedMenu01, color: Colors.white),
        ),
        title: Row(
          children: [
            //profile icon
            Icon(HugeIcons.strokeRoundedUserGroup,
                color: Colors.white, size: 24),
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Residents',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                ),
                Text(
                  '${widget.unit.name} Residents',
                  style: TextStyle(
                    color: Color(0xFF8E8E93),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () {
              GoRouter.of(context).goNamed(RouteConstants.unitsRouteName);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Unit Info Card
          BlocBuilder<UnitBloc, UnitState>(
            builder: (context, state) {
              if (state is UnitDetailLoaded) {
                return _buildUnitInfoCard(widget.unit);
              }
              return const SizedBox.shrink();
            },
          ),
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search residents...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          context
                              .read<ResidentBloc>()
                              .add(FetchResidentsByUnit(widget.unitId));
                        },
                      )
                    : null,
                filled: true,
                fillColor: Colors.white,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(30),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
              ),
              onChanged: (value) {
                context
                    .read<ResidentBloc>()
                    .add(SearchResidentsByUnit(widget.unitId, value));
              },
            ),
          ),
          // Residents List
          Expanded(
            child: BlocBuilder<ResidentBloc, ResidentState>(
              builder: (context, state) {
                if (state is ResidentLoading) {
                  return const Center(child: CircularProgressIndicator());
                } else if (state is ResidentLoaded) {
                  return _buildResidentsList(state.filteredResidents);
                } else if (state is ResidentEmpty) {
                  return _buildEmptyState();
                } else if (state is ResidentError) {
                  return Center(child: Text('Error: ${state.message}'));
                } else {
                  return const Center(child: Text('Please load residents'));
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnitInfoCard(Unit unit) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 30,
            backgroundColor: const Color.fromRGBO(90, 38, 101, 0.9),
            child: Text(
              unit.name.isNotEmpty ? unit.name[0] : '?',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  unit.name,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color.fromRGBO(90, 38, 101, 1),
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(
                      Icons.location_on_outlined,
                      size: 16,
                      color: Colors.grey,
                    ),
                    const SizedBox(width: 4),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(
                      Icons.people_outline,
                      size: 16,
                      color: Colors.grey,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Capacity: ${unit.maxResidentCount}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[700],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

        ],
      ),
    );
  }

  Widget _buildResidentsList(List<Resident> residents) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: residents.length,
      itemBuilder: (context, index) {
        final resident = residents[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  // Navigate to resident details
                  GoRouter.of(context).goNamed(
                    RouteConstants.residentPersonalRouteName,
                    pathParameters: {'residentId': resident.userCode,'residentUUID':resident.id} ,
                  );
                },
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Hero(
                        tag: 'resident-${resident.id}',
                        child: Container(
                          width: 65,
                          height: 65,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                spreadRadius: 1,
                                blurRadius: 3,
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(40),
                            child: Image.network(
                              resident.image.isNotEmpty
                                  ? resident.image
                                  : 'https://example.com/default_image.png',
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  color: Colors.grey[200],
                                  child: Icon(Icons.person,
                                      size: 40, color: Colors.grey[400]),
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              resident.name,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Code: ${resident.userCode}',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              'Phone: ${resident.phone}',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: resident.status == 'Active'
                              ? Colors.green.withOpacity(0.1)
                              : Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(30),
                        ),
                        child: Text(
                          resident.status,
                          style: TextStyle(
                            color: resident.status == 'Active'
                                ? Colors.green[700]
                                : Colors.red[700],
                            fontWeight: FontWeight.w600,
                            fontSize: 13,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No residents found in this unit',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add residents to this unit using the + button',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }
}
