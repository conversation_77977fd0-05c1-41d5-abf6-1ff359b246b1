import 'dart:io';
import 'package:carerez/blocs/auth/auth_bloc.dart';
import 'package:carerez/components/drawer.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../blocs/user/user_bloc.dart';
import '../../blocs/user/user_state.dart';
import '../../models/complaints.dart';
import 'package:intl/intl.dart';
import '../../routes/router_constants.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/complaints/complaints_bloc.dart';
import '../../blocs/complaints/complaints_event.dart';
import '../../blocs/complaints/complaints_state.dart';
import '../../blocs/resident/resident_bloc.dart';
import '../../blocs/resident/resident_event.dart';
import '../../blocs/resident/resident_state.dart';
import '../../services/complaints_service.dart';
import '../../services/resident_service.dart';

class ComplaintManagementScreen extends StatefulWidget {
  @override
  _ComplaintManagementScreenState createState() => _ComplaintManagementScreenState();
}

class _ComplaintManagementScreenState extends State<ComplaintManagementScreen> with SingleTickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late TabController _tabController;

  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  List<File> _attachments = [];
  String? selectedResident;
  String _selectedLevel = 'Neutral';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _pickAttachments() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.any,
    );

    if (result != null) {
      setState(() {
        _attachments = result.paths.map((path) => File(path!)).toList();
      });
    }
  }

  void _createComplaint() {
    if (_formKey.currentState!.validate() && selectedResident != null) {

      final data = context.read<UserBloc>().state as UserAndTasksLoaded;
      final staffId = data.user.id;
      final homeId = data.user.homeId ?? '';
      final unitId = data.user.unitId ?? '';

      final newComplaint = Complaints(
        complaintId: DateTime.now().millisecondsSinceEpoch.toString(),
        staffId: staffId,
        residentId: selectedResident,
        complaintName: _nameController.text.trim(),
        homeId: homeId,
        unitId: unitId,
        complaintLevel: _selectedLevel.toLowerCase(),
        placeName: 'Home', // Assuming the complaint is related to home
        status: 'new',
        dateTime: DateTime.now(),
        description: _descriptionController.text.trim(),
        complaintAttachments: _attachments.map((file) => ComplaintAttachment(
          attachmentId: DateTime.now().millisecondsSinceEpoch.toString(),
          attachmentURL: file.path,
          attachmentName: file.path.split('/').last,
        )).toList(),
        complaintTimelines: [],
      );

      // Dispatch event to Bloc
      context.read<ComplaintsBloc>().add(CreateComplaint(newComplaint));

      // Clear form fields and state
      _formKey.currentState!.reset();
      _nameController.clear();
      _descriptionController.clear();
      setState(() {
        _attachments.clear();
        selectedResident = null;
        _selectedLevel = 'Neutral';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Complaint Created Successfully'),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );

      _tabController.animateTo(0); // Switch to complaints list tab
    } else if (selectedResident == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Please select a resident.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'new':
        return Colors.blue;
      case 'resolved':
        return Colors.green;
      case 'closed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getLevelColor(String level) {
    switch (level.toLowerCase()) {
      case 'low':
        return Colors.green;
      case 'neutral':
        return Colors.blue;
      case 'observation':
        return Colors.orange;
      case 'emergency':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Widget _buildComplaintCard(Complaints complaint) {
    return GestureDetector(
      onTap: () {

        GoRouter.of(context).goNamed(RouteConstants.complaintDetailsRouteName, extra: complaint);
      },
      child: Card(
        color: Colors.white,
        elevation: 4,
        margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row with Status and Priority
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      complaint.complaintName,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getLevelColor(complaint.complaintLevel),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      complaint.complaintLevel.toUpperCase(),
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8),
              // Description
              Text(
                complaint.description,
                style: TextStyle(color: Colors.grey[700]),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 8),
              // Resident info
              if (complaint.residentDetails != null && complaint.residentDetails!.userDetails != null)
                Row(
                  children: [
                    Icon(Icons.person, size: 16, color: Colors.grey[600]),
                    SizedBox(width: 4),
                    Text(
                      'Resident: ${complaint.residentDetails!.userDetails!.firstName}',
                      style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    ),
                  ],
                ),
              SizedBox(height: 4),
              // Staff info
              if (complaint.staffDetails != null && complaint.staffDetails!.userDetails != null)
                Row(
                  children: [
                    Icon(Icons.person_outline, size: 16, color: Colors.grey[600]),
                    SizedBox(width: 4),
                    Text(
                      'Staff: ${complaint.staffDetails!.userDetails!.firstName}',
                      style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    ),
                  ],
                ),
              SizedBox(height: 8),
              // Footer Row with Status and Date
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getStatusColor(complaint.status),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      complaint.status.toUpperCase(),
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Text(
                    DateFormat('MMM dd, yyyy HH:mm').format(complaint.dateTime),
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              // Show attachments count if any
              if (complaint.complaintAttachments != null && complaint.complaintAttachments.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Row(
                    children: [
                      Icon(Icons.attach_file, size: 16, color: Colors.grey[600]),
                      SizedBox(width: 4),
                      Text(
                        '${complaint.complaintAttachments.length} attachment(s)',
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => ComplaintsBloc(ComplaintsService())..add(FetchComplaints()),
        ),
        BlocProvider(
          create: (context) => ResidentBloc(ResidentService())..add(FetchResidents()),
        ),
      ],
      child: Scaffold(
        backgroundColor: Colors.white,
        key: _scaffoldKey,
        drawer: buildDrawer(scaffoldKey: _scaffoldKey),
        appBar: AppBar(
          backgroundColor: Color.fromRGBO(90, 38, 101, 1),
          elevation: 0,
          leading: IconButton(
            icon: Icon(Icons.menu, color: Colors.white),
            onPressed: () => _scaffoldKey.currentState?.openDrawer(),
          ),
          actions: [
            IconButton(
              icon: Icon(Icons.arrow_back_ios, color: Colors.white),
              onPressed: () => GoRouter.of(context).goNamed(RouteConstants.homescreenRouteName),
            ),
          ],
          title: Text(
            "Complaint Management",
            style: TextStyle(color: Colors.white),
          ),
          bottom: TabBar(
            controller: _tabController,
            indicatorColor: Colors.white,
            tabs: [
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.list, color: Colors.white),
                    SizedBox(width: 8),
                    Text('All Complaints', style: TextStyle(color: Colors.white)),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.add_circle_outline, color: Colors.white),
                    SizedBox(width: 8),
                    Text('New Complaint', style: TextStyle(color: Colors.white)),
                  ],
                ),
              ),
            ],
          ),
        ),
        body: TabBarView(
          controller: _tabController,
          children: [
            // All Complaints Tab
            Column(
              children: [
                Padding(
                  padding: EdgeInsets.all(16),
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: 'Search complaints...',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                  ),
                ),
                Expanded(
                  child: RefreshIndicator(
                    onRefresh: () async {
                      await Future.delayed(Duration(seconds: 1));
                      setState(() {});
                    },
                    child: BlocBuilder<ComplaintsBloc, ComplaintsState>(
                      builder: (context, state) {
                        if (state is ComplaintsLoading) {
                          return Center(child: CircularProgressIndicator());
                        } else if (state is ComplaintsLoaded) {
                          if (state.filteredComplaints.isEmpty) {
                            return Center(child: Text('No complaints found'));
                          }
                          return ListView.builder(
                            itemCount: state.filteredComplaints.length,
                            itemBuilder: (context, index) {
                              final complaint = state.filteredComplaints[index];
                              return _buildComplaintCard(complaint);
                            },
                          );
                        } else if (state is ComplaintsError) {
                          return Center(child: Text('Error: \\${state.message}'));
                        }
                        return Center(child: Text('No data'));
                      },
                    ),
                  ),
                ),
              ],
            ),
            // Create New Complaint Tab
            _buildCreateComplaintForm(),
          ],
        ),
      ),
    );
  }

  Widget _buildCreateComplaintForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFFFF6B6B), Color(0xFFFF8E8E)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFFF6B6B).withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Icon(
                        Icons.feedback_outlined,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'Create New Complaint',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                        letterSpacing: 0.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Fill in the details below to file a complaint',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.8),
                    fontWeight: FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Form Section
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 20,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Resident Selection
                    _buildSectionTitle('Resident Information'),
                    const SizedBox(height: 16),
                    BlocBuilder<ResidentBloc, ResidentState>(
                      builder: (context, state) {
                        final mediaQuery = MediaQuery.of(context);
                        final screenWidth = mediaQuery.size.width;
                        if (state is ResidentLoading) {
                          return Container(
                            padding: EdgeInsets.all(screenWidth * 0.04),
                            decoration: BoxDecoration(
                              color: const Color(0xFFF7FAFC),
                              borderRadius: BorderRadius.circular(screenWidth * 0.03),
                              border: Border.all(
                                color: const Color(0xFFE2E8F0),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                SizedBox(
                                  width: screenWidth * 0.05,
                                  height: screenWidth * 0.05,
                                  child: const CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Color(0xFFFF6B6B),
                                  ),
                                ),
                                SizedBox(width: screenWidth * 0.03),
                                Text(
                                  'Loading residents...',
                                  style: TextStyle(
                                    color: Color(0xFF718096),
                                    fontSize: screenWidth * 0.035,
                                  ),
                                ),
                              ],
                            ),
                          );
                        } else if (state is ResidentLoaded) {
                          return _buildModernDropdown(
                            label: 'Select Resident',
                            icon: Icons.person_outline,
                            value: selectedResident,
                            items: state.filteredResidents.map((resident) =>
                                DropdownMenuItem<String>(
                                  value: resident.id,
                                  child: Text(resident.name),
                                )
                            ).toList(),
                            onChanged: (value) {
                              setState(() {
                                selectedResident = value;
                              });
                            },
                            validator: (value) => value == null ? 'Please select a resident' : null,
                          );
                        } else if (state is ResidentEmpty) {
                          return _buildErrorCard('No residents found');
                        } else if (state is ResidentError) {
                          return _buildErrorCard('Error: ${state.message}');
                        }
                        return const SizedBox();
                      },
                    ),

                    const SizedBox(height: 32),

                    // Complaint Details
                    _buildSectionTitle('Complaint Details'),
                    const SizedBox(height: 16),
                    _buildModernTextField(
                      controller: _nameController,
                      label: 'Complaint Title',
                      icon: Icons.title_rounded,
                      hint: 'Enter a brief title for your complaint',
                      validator: (value) => value == null || value.isEmpty ? 'Please enter a title' : null,
                    ),

                    const SizedBox(height: 20),

                    _buildModernTextField(
                      controller: _descriptionController,
                      label: 'Description',
                      icon: Icons.description_outlined,
                      maxLines: 4,
                      hint: 'Provide detailed description of the complaint...',
                      validator: (value) => value == null || value.isEmpty ? 'Please enter a description' : null,
                    ),

                    const SizedBox(height: 32),

                    // Priority Level
                    _buildSectionTitle('Priority Level'),
                    const SizedBox(height: 16),
                    _buildModernDropdown(
                      label: 'Priority Level',
                      icon: Icons.priority_high_rounded,
                      value: _selectedLevel,
                      items: ['Emergency', 'Neutral', 'Observation'].map((level) =>
                          DropdownMenuItem<String>(
                            value: level,
                            child: Row(
                              children: [
                                Container(
                                  width: 12,
                                  height: 12,
                                  decoration: BoxDecoration(
                                    color: _getLevelColor(level),
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(level),
                              ],
                            ),
                          )
                      ).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedLevel = value!;
                        });
                      },
                    ),

                    const SizedBox(height: 32),

                    // Attachments Section
                    _buildSectionTitle('Attachments'),
                    const SizedBox(height: 16),
                    _buildAttachmentSection(),

                    const SizedBox(height: 40),

                    // Submit Button
                    Container(
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFFFF6B6B), Color(0xFFFF8E8E)],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFFFF6B6B).withOpacity(0.4),
                            blurRadius: 15,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: ElevatedButton(
                        onPressed: _createComplaint,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          shadowColor: Colors.transparent,
                          padding: const EdgeInsets.symmetric(vertical: 18),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.send_rounded,
                              color: Colors.white,
                              size: 20,
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Submit Complaint',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Bottom spacing
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Row(
      children: [
        Container(
          width: 4,
          height: 20,
          decoration: BoxDecoration(
            color: const Color(0xFFFF6B6B),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2D3748),
          ),
        ),
      ],
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    int maxLines = 1,
    String? hint,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF7FAFC),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFE2E8F0),
          width: 1,
        ),
      ),
      child: TextFormField(
        controller: controller,
        maxLines: maxLines,
        validator: validator,
        style: const TextStyle(
          fontSize: 16,
          color: Color(0xFF2D3748),
        ),
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          prefixIcon: Icon(
            icon,
            color: const Color(0xFFFF6B6B),
            size: 20,
          ),
          labelStyle: const TextStyle(
            color: Color(0xFF718096),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          hintStyle: TextStyle(
            color: const Color(0xFF718096).withOpacity(0.7),
            fontSize: 14,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
          floatingLabelBehavior: FloatingLabelBehavior.auto,
        ),
      ),
    );
  }

  Widget _buildModernDropdown({
    required String label,
    required IconData icon,
    required String? value,
    required List<DropdownMenuItem<String>> items,
    required Function(String?) onChanged,
    String? Function(String?)? validator,
  }) {
    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(
          icon,
          color: const Color(0xFFFF6B6B),
          size: 20,
        ),
        labelStyle: const TextStyle(
          color: Color(0xFF718096),
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        filled: true,
        fillColor: const Color(0xFFF7FAFC),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFFE2E8F0),
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFFE2E8F0),
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFFFF6B6B),
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
      dropdownColor: Colors.white,
      value: value,
      style: const TextStyle(
        fontSize: 16,
        color: Color(0xFF2D3748),
      ),
      items: items,
      onChanged: onChanged,
      validator: validator,
      isExpanded: true,
    );
  }

  Widget _buildAttachmentSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: const Color(0xFFF7FAFC),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFFE2E8F0),
              width: 1,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(12),
              onTap: _pickAttachments,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Icon(
                      Icons.attach_file_rounded,
                      color: const Color(0xFFFF6B6B),
                      size: 32,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Add Attachments',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF2D3748),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Tap to select files',
                      style: TextStyle(
                        fontSize: 14,
                        color: const Color(0xFF718096).withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),

        // Attachment list
        if (_attachments.isNotEmpty) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFF0FFF4),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFFC6F6D5),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Color(0xFF48BB78),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${_attachments.length} file(s) selected',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF22543D),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _attachments.map((file) {
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: const Color(0xFFC6F6D5),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.insert_drive_file,
                            color: Color(0xFF68D391),
                            size: 16,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            file.path.split('/').last,
                            style: const TextStyle(
                              fontSize: 12,
                              color: Color(0xFF22543D),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 6),
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                _attachments.remove(file);
                              });
                            },
                            child: const Icon(
                              Icons.close,
                              color: Color(0xFF68D391),
                              size: 16,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildErrorCard(String message) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFFED7D7),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFFEB2B2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.error_outline,
            color: Color(0xFFE53E3E),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF742A2A),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
