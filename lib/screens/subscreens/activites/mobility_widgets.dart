import 'package:flutter/material.dart';

import '../../../components/careActivitesDropDown.dart';
import '../../../components/careActivitesHeader.dart';
import '../../../components/careActivitesSubmit.dart';
import '../../../components/careActivitesText.dart';
import '../../../components/timePicker.dart';
import '../../../models/activity.dart';
import '../../../models/staff.dart';

// Handler function for mobility/positioning activities
void createMobilityHandleSave(
    BuildContext context,
    String activityName,
    String residentId,
    int? selectedHour,
    int? selectedMinute,
    String? selectedSubActivity,
    TextEditingController notesController,
    Function(ResidentCareActivity) onSave,
    ) {
  final activity = ResidentCareActivity(
    residentId: residentId, // Pass from parent or get from context
    activityName: activityName,
    mood: 'neutral', // Category for mobility activities
    attachments: [], // No attachments for mobility activities
    subActivityA: 'Duration/Time',
    subActivityAValue: selectedHour != null && selectedMinute != null
        ? '${selectedHour}:${selectedMinute.toString().padLeft(2, '0')}'
        : '',
    subActivityB: 'Support Level',
    subActivityBValue: selectedSubActivity ?? 'Not specified',
    temperature: '', // Not used for mobility activities
    notes: notesController.text,
    handOffStaffId: '',
  );
  onSave(activity);
  Navigator.of(context).pop();
}

// Widgets with proper onSave implementation

class IntoBed extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const IntoBed({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<IntoBed> createState() => _IntoBedState();
}

class _IntoBedState extends State<IntoBed> {
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Into Bed',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: [
                  'Staff Supported',
                  'Independent',
                ],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMobilityHandleSave(
                  context,
                  'Into Bed',
                 widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class OutOfBed extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const OutOfBed({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<OutOfBed> createState() => _OutOfBedState();
}

class _OutOfBedState extends State<OutOfBed> {
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Out Of Bed',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: [
                  'Staff Supported',
                  'Independent',
                ],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMobilityHandleSave(
                  context,
                  'Out Of Bed',
                 widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Walk extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Walk({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Walk> createState() => _WalkState();
}

class _WalkState extends State<Walk> {
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Walk',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: [
                  'Staff Supported',
                  'Independent',
                ],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMobilityHandleSave(
                  context,
                  'Walk',
                 widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ElevateLegs extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const ElevateLegs({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<ElevateLegs> createState() => _ElevateLegsState();
}

class _ElevateLegsState extends State<ElevateLegs> {
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Elevate Legs',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: [
                  'Staff Supported',
                  'Independent',
                ],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMobilityHandleSave(
                  context,
                  'Elevate Legs',
                 widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Stairs extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Stairs({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Stairs> createState() => _StairsState();
}

class _StairsState extends State<Stairs> {
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Stairs',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: [
                  'Staff Supported',
                  'Independent',
                ],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMobilityHandleSave(
                  context,
                  'Stairs',
                 widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class IntoChair extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const IntoChair({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<IntoChair> createState() => _IntoChairState();
}

class _IntoChairState extends State<IntoChair> {
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Into Chair',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: [
                  'Staff Supported',
                  'Independent',
                ],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMobilityHandleSave(
                  context,
                  'Into Chair',
                 widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class UpFromChair extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const UpFromChair({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<UpFromChair> createState() => _UpFromChairState();
}

class _UpFromChairState extends State<UpFromChair> {
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Up From Chair',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: [
                  'Staff Supported',
                  'Independent',
                ],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMobilityHandleSave(
                  context,
                  'Up From Chair',
                 widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class StandingHoist extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const StandingHoist({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<StandingHoist> createState() => _StandingHoistState();
}

class _StandingHoistState extends State<StandingHoist> {
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Standing Hoist',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: [
                  'Staff Supported',
                  'Independent',
                ],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMobilityHandleSave(
                  context,
                  'Standing Hoist',
                 widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class HandingBelt extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const HandingBelt({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<HandingBelt> createState() => _HandingBeltState();
}

class _HandingBeltState extends State<HandingBelt> {
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Handling Belt',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: [
                  'Staff Supported',
                  'Independent',
                ],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMobilityHandleSave(
                  context,
                  'Handling Belt',
                 widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Fall extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Fall({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Fall> createState() => _FallState();
}

class _FallState extends State<Fall> {
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Fall',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: [
                  'Staff Supported',
                  'Independent',
                ],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMobilityHandleSave(
                  context,
                  'Fall',
                 widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class HoistTwoPeople extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const HoistTwoPeople({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<HoistTwoPeople> createState() => _HoistTwoPeopleState();
}

class _HoistTwoPeopleState extends State<HoistTwoPeople> {
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Hoist Two People',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: [
                  'Staff Supported',
                  'Independent',
                ],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMobilityHandleSave(
                  context,
                  'Hoist Two People',
                 widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Moved extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Moved({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Moved> createState() => _MovedState();
}

class _MovedState extends State<Moved> {
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Moved',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: [
                  'Staff Supported',
                  'Independent',
                ],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMobilityHandleSave(
                  context,
                  'Moved',
                 widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}