import 'package:carerez/components/careActivitesThumbs.dart';
import 'package:carerez/config/env_config.dart';
import 'package:carerez/models/staff.dart';
import 'package:carerez/services/http_client.dart';
import 'package:flutter/material.dart';

import '../../../components/attachmentButton.dart';
import '../../../components/careActivitesDropDown.dart';
import '../../../components/careActivitesHeader.dart';
import '../../../components/careActivitesSubmit.dart';
import '../../../components/careActivitesText.dart';
import '../../../components/moodSelection.dart';
import '../../../components/timePicker.dart';
import '../../../models/activity.dart';

// Handler function for care activities
void createCareActivityHandleSave(
    BuildContext context,
    String activityName,
    String residentId,
    int? selectedHour,
    int? selectedMinute,
    String? selectedResponse,
    TextEditingController notesController,
    TextEditingController handOffNotesController,
    String? selectedHandOff,
    List<String> attachments,
    Function(ResidentCareActivity) onSave, {
      String? temperature,
      String? mood,
    }) {
  final activity = ResidentCareActivity(
    residentId: residentId, // Pass from parent or get from context
    activityName: activityName,
    mood: mood ?? selectedResponse ?? 'neutral', // Use selectedResponse or default to 'neutral'
    attachments: attachments.map((e) => ActivityAttachment(
        fileName: e.split('/').last,
        filePath:e
    )).toList(),
    subActivityA: 'Duration/Time',
    subActivityAValue: selectedHour != null && selectedMinute != null
        ? '${selectedHour}:${selectedMinute.toString().padLeft(2, '0')}'
        : '',
    subActivityB: 'Activity Status',
    subActivityBValue: selectedResponse ?? 'Not specified',
    temperature: temperature.toString() ?? '25',
    notes: notesController.text + '\n' + handOffNotesController.text,
    handOffStaffId: selectedHandOff ?? '',
  );
  onSave(activity);
  Navigator.of(context).pop();
}

// Handler function for personal care activities
void createPersonalCareHandleSave(
    BuildContext context,
    String activityName,
    int? selectedHour,
    int? selectedMinute,
    String? selectedSubActivity,
    TextEditingController notesController,
    Function(ResidentCareActivity) onSave,
    ) {
  final activity = ResidentCareActivity(
    residentId: '', // Pass from parent or get from context
    activityName: activityName,
    mood: 'Personal Care', // Category for personal care activities
    attachments: [], // No attachments for personal care activities
    subActivityA: 'Duration/Time',
    subActivityAValue: selectedHour != null && selectedMinute != null
        ? '${selectedHour}:${selectedMinute.toString().padLeft(2, '0')}'
        : '',
    subActivityB: 'Activity Status',
    subActivityBValue: selectedSubActivity ?? 'Not specified',
    temperature: '', // Not used for personal care activities
    notes: notesController.text,
    handOffStaffId: '',
  );
  onSave(activity);
  Navigator.of(context).pop();
}

class CheckOK extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CheckOK({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CheckOK> createState() => _CheckOKState();
}

class _CheckOKState extends State<CheckOK> {
  String? selectedMood;
  int? selectedHour;
  int? selectedMinute;
  String? selectedHandOff;
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    handOffNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'How is Resident Feeling?',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              Text('Select the option that best describes the resident'),
              SizedBox(height: 10),
              MoodSelection(onMoodSelected: (mood) {
                setState(() {
                  selectedMood = mood;
                });
              }),
              SizedBox(height: 20),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 20),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand-Off Note',
                icon: Icons.notes,
                controller: handOffNotesController,
              ),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createCareActivityHandleSave(
                  context,
                  'Check OK',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedMood,
                  notesController,
                  handOffNotesController,
                  selectedHandOff,
                  attachments,
                  widget.onSave,
                  mood: selectedMood,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ChangeBed extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const ChangeBed({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<ChangeBed> createState() => _ChangeBedState();
}

class _ChangeBedState extends State<ChangeBed> {
  String? selectedResponse;
  int? selectedHour;
  int? selectedMinute;
  String? selectedHandOff;
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    handOffNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Change Bed',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 20),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 20),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand-Off Note',
                icon: Icons.notes,
                controller: handOffNotesController,
              ),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createCareActivityHandleSave(
                  context,
                  'Change Bed',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  handOffNotesController,
                  selectedHandOff,
                  attachments,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CheckChair extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CheckChair({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CheckChair> createState() => _CheckChairState();
}

class _CheckChairState extends State<CheckChair> {
  String? selectedResponse;
  int? selectedHour;
  int? selectedMinute;
  String? selectedHandOff;
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    handOffNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Check Chair',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 20),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 20),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand-Off Note',
                icon: Icons.notes,
                controller: handOffNotesController,
              ),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createCareActivityHandleSave(
                  context,
                  'Check Chair',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  handOffNotesController,
                  selectedHandOff,
                  attachments,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CheckMattress extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CheckMattress({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CheckMattress> createState() => _CheckMattressState();
}

class _CheckMattressState extends State<CheckMattress> {
  String? selectedResponse;
  int? selectedHour;
  int? selectedMinute;
  String? selectedHandOff;
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    handOffNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Check Mattress',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 20),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 20),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand-Off Note',
                icon: Icons.notes,
                controller: handOffNotesController,
              ),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createCareActivityHandleSave(
                  context,
                  'Check Mattress',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  handOffNotesController,
                  selectedHandOff,
                  attachments,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CheckWheelChair extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CheckWheelChair({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CheckWheelChair> createState() => _CheckWheelChairState();
}

class _CheckWheelChairState extends State<CheckWheelChair> {
  String? selectedResponse;
  int? selectedHour;
  int? selectedMinute;
  String? selectedHandOff;
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    handOffNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Check WheelChair',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 20),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 20),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand-Off Note',
                icon: Icons.notes,
                controller: handOffNotesController,
              ),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createCareActivityHandleSave(
                  context,
                  'Check WheelChair',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  handOffNotesController,
                  selectedHandOff,
                  attachments,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class AdjustCurtains extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const AdjustCurtains({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<AdjustCurtains> createState() => _AdjustCurtainsState();
}

class _AdjustCurtainsState extends State<AdjustCurtains> {
  String? selectedResponse;
  int? selectedHour;
  int? selectedMinute;
  String? selectedHandOff;
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    handOffNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Check Curtain',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 20),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 20),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand-Off Note',
                icon: Icons.notes,
                controller: handOffNotesController,
              ),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createCareActivityHandleSave(
                  context,
                  'Check Curtain',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  handOffNotesController,
                  selectedHandOff,
                  attachments,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CheckRoom extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CheckRoom({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CheckRoom> createState() => _CheckRoomState();
}

class _CheckRoomState extends State<CheckRoom> {
  String? selectedResponse;
  int? selectedHour;
  int? selectedMinute;
  String? selectedHandOff;
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    handOffNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Check Room',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 20),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 20),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand-Off Note',
                icon: Icons.notes,
                controller: handOffNotesController,
              ),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createCareActivityHandleSave(
                  context,
                  'Check Room',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  handOffNotesController,
                  selectedHandOff,
                  attachments,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class FridgeTemperature extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const FridgeTemperature({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<FridgeTemperature> createState() => _FridgeTemperatureState();
}

class _FridgeTemperatureState extends State<FridgeTemperature> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedHandOff;
  final TextEditingController temperatureController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();

  @override
  void dispose() {
    temperatureController.dispose();
    notesController.dispose();
    handOffNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Fridge Temperature',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Temperature',
                icon: Icons.thermostat,
                controller: temperatureController,
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: handOffNotesController,
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createCareActivityHandleSave(
                  context,
                  'Fridge Temperature',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  null,
                  notesController,
                  handOffNotesController,
                  selectedHandOff,
                  attachments,
                  widget.onSave,
                  temperature: temperatureController.text,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class RoomTemperature extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const RoomTemperature({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<RoomTemperature> createState() => _RoomTemperatureState();
}

class _RoomTemperatureState extends State<RoomTemperature> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedHandOff;
  final TextEditingController temperatureController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();

  @override
  void dispose() {
    temperatureController.dispose();
    notesController.dispose();
    handOffNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Room Temperature',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Temperature',
                icon: Icons.thermostat,
                controller: temperatureController,
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: handOffNotesController,
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createCareActivityHandleSave(
                  context,
                  'Room Temperature',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  null,
                  notesController,
                  handOffNotesController,
                  selectedHandOff,
                  attachments,
                  widget.onSave,
                  temperature: temperatureController.text,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Continuing with the remaining widgets following the same pattern...
// For brevity, I'll include a few more key ones:

class CheckEquipment extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CheckEquipment({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CheckEquipment> createState() => _CheckEquipmentState();
}

class _CheckEquipmentState extends State<CheckEquipment> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    handOffNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Check Equipment',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: handOffNotesController,
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createCareActivityHandleSave(
                  context,
                  'Check Equipment',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  handOffNotesController,
                  selectedHandOff,
                  attachments,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CheckActionMat extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CheckActionMat({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CheckActionMat> createState() => _CheckActionMatState();
}

class _CheckActionMatState extends State<CheckActionMat> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    handOffNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Check Action Mat',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: handOffNotesController,
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createCareActivityHandleSave(
                  context,
                  'Check Action Mat',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  handOffNotesController,
                  selectedHandOff,
                  attachments,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CheckPendantAlarm extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CheckPendantAlarm({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CheckPendantAlarm> createState() => _CheckPendantAlarmState();
}

class _CheckPendantAlarmState extends State<CheckPendantAlarm> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    handOffNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Check Pendant Alarm',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: handOffNotesController,
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createCareActivityHandleSave(
                  context,
                  'Check Pendant Alarm',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  handOffNotesController,
                  selectedHandOff,
                  attachments,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class TidiedWardrobe extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const TidiedWardrobe({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<TidiedWardrobe> createState() => _TidiedWardrobeState();
}

class _TidiedWardrobeState extends State<TidiedWardrobe> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    handOffNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Tidied Wardrobe',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: handOffNotesController,
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createCareActivityHandleSave(
                  context,
                  'Tidied Wardrobe',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  handOffNotesController,
                  selectedHandOff,
                  attachments,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CheckWheelChairBelt extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CheckWheelChairBelt({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CheckWheelChairBelt> createState() => _CheckWheelChairBeltState();
}

class _CheckWheelChairBeltState extends State<CheckWheelChairBelt> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    handOffNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Check WheelChair Belt',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: handOffNotesController,
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createCareActivityHandleSave(
                  context,
                  'Check WheelChair Belt',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  handOffNotesController,
                  selectedHandOff,
                  attachments,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CheckSensor extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CheckSensor({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CheckSensor> createState() => _CheckSensorState();
}

class _CheckSensorState extends State<CheckSensor> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    handOffNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Check Sensor',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: handOffNotesController,
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createCareActivityHandleSave(
                  context,
                  'Check Sensor',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  handOffNotesController,
                  selectedHandOff,
                  attachments,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CheckBedRails extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CheckBedRails({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CheckBedRails> createState() => _CheckBedRailsState();
}

class _CheckBedRailsState extends State<CheckBedRails> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    handOffNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Check Bed Rails',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: handOffNotesController,
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createCareActivityHandleSave(
                  context,
                  'Check Bed Rails',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  handOffNotesController,
                  selectedHandOff,
                  attachments,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CheckBinBags extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CheckBinBags({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CheckBinBags> createState() => _CheckBinBagsState();
}

class _CheckBinBagsState extends State<CheckBinBags> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    handOffNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Check Bin Bags',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: handOffNotesController,
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createCareActivityHandleSave(
                  context,
                  'Check Bin Bags',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  handOffNotesController,
                  selectedHandOff,
                  attachments,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}