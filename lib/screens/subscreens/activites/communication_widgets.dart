import 'package:flutter/material.dart';
import '../../../components/attachmentButton.dart';
import '../../../components/careActivitesHeader.dart';
import '../../../components/careActivitesSubmit.dart';
import '../../../components/careActivitesText.dart';
import '../../../components/careActivitesThumbs.dart';
import '../../../components/timePicker.dart';
import '../../../models/activity.dart';
import '../../../models/staff.dart';

// Common handler function for simple communication activities
void createCommunicationHandleSave(
    BuildContext context,
    String activityName,
    String residentId,
    String? selectedResponse,
    int? selectedHour,
    int? selectedMinute,
    List<String> attachments,
    TextEditingController notesController,
    Function(ResidentCareActivity) onSave,
    {String? subActivityBValue}
    ) {
  final activity = ResidentCareActivity(
    residentId: residentId, // Pass from parent or get from context
    activityName: activityName,
    mood: selectedResponse ?? 'neutral',
    attachments: attachments.map((e) => ActivityAttachment(
        fileName: e.split('/').last,
        filePath: e
    )).toList(),
    subActivityA: 'Duration',
    subActivityAValue: selectedHour != null && selectedMinute != null
        ? '${selectedHour}:${selectedMinute.toString().padLeft(2, '0')}'
        : '',
    subActivityB: 'Type',
    subActivityBValue: subActivityBValue ?? '',
    temperature: '',
    notes: notesController.text,
    handOffStaffId: '',
  );
  onSave(activity);
  Navigator.of(context).pop();
}

class ChatWidget extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const ChatWidget({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<ChatWidget> createState() => _ChatWidgetState();
}

class _ChatWidgetState extends State<ChatWidget> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Chat',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createCommunicationHandleSave(
                context,
                'Chat',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                attachments,
                notesController,
                widget.onSave,
                subActivityBValue: 'Conversation',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class ReadLetter extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const ReadLetter({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<ReadLetter> createState() => _ReadLetterState();
}

class _ReadLetterState extends State<ReadLetter> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Read Letter',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createCommunicationHandleSave(
                context,
                'Read Letter',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                attachments,
                notesController,
                widget.onSave,
                subActivityBValue: 'Reading',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class NewsPaper extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const NewsPaper({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<NewsPaper> createState() => _NewsPaperState();
}

class _NewsPaperState extends State<NewsPaper> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'News Paper',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createCommunicationHandleSave(
                context,
                'News Paper',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                attachments,
                notesController,
                widget.onSave,
                subActivityBValue: 'Reading',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Email extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Email({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Email> createState() => _EmailState();
}

class _EmailState extends State<Email> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Email',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createCommunicationHandleSave(
                context,
                'Email',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                attachments,
                notesController,
                widget.onSave,
                subActivityBValue: 'Digital Communication',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Bell extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Bell({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Bell> createState() => _BellState();
}

class _BellState extends State<Bell> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Bell',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createCommunicationHandleSave(
                context,
                'Bell',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                attachments,
                notesController,
                widget.onSave,
                subActivityBValue: 'Alert/Call',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Phone extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Phone({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Phone> createState() => _PhoneState();
}

class _PhoneState extends State<Phone> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Phone',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createCommunicationHandleSave(
                context,
                'Phone',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                attachments,
                notesController,
                widget.onSave,
                subActivityBValue: 'Phone Call',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class CantCommunicate extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CantCommunicate({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CantCommunicate> createState() => _CantCommunicateState();
}

class _CantCommunicateState extends State<CantCommunicate> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Can\'t Communicate',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createCommunicationHandleSave(
                context,
                'Can\'t Communicate',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                attachments,
                notesController,
                widget.onSave,
                subActivityBValue: 'Communication Difficulty',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Mentoring extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Mentoring({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Mentoring> createState() => _MentoringState();
}

class _MentoringState extends State<Mentoring> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Mentoring',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createCommunicationHandleSave(
                context,
                'Mentoring',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                attachments,
                notesController,
                widget.onSave,
                subActivityBValue: 'Guidance/Support',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class WriteLetter extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const WriteLetter({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<WriteLetter> createState() => _WriteLetterState();
}

class _WriteLetterState extends State<WriteLetter> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Write Letter',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createCommunicationHandleSave(
                context,
                'Write Letter',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                attachments,
                notesController,
                widget.onSave,
                subActivityBValue: 'Writing',
              )),
            ],
          ),
        ),
      ),
    );
  }
}