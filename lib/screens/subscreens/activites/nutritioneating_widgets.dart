import 'package:carerez/components/careActivitesAmountSlider.dart';
import 'package:carerez/components/careActivitesThumbs.dart';
import 'package:flutter/material.dart';

import '../../../components/attachmentButton.dart';
import '../../../components/careActivitesDropDown.dart';
import '../../../components/careActivitesHeader.dart';
import '../../../components/careActivitesSubmit.dart';
import '../../../components/careActivitesText.dart';
import '../../../components/careActivitiesDrinkDetails.dart';
import '../../../components/timePicker.dart';
import '../../../models/activity.dart';
import '../../../models/staff.dart';

// Handler function for food and drink activities
void createFoodDrinkHandleSave(
    BuildContext context,
    String activityName,
    String residentId,
    int? selectedHour,
    int? selectedMinute,
    String? selectedResponse,
    double? amountOffered,
    double? amountConsumed,
    TextEditingController notesController,
    String? subActivity,
    List<String> attachments,
    Function(ResidentCareActivity) onSave, {
      TimeOfDay? drinkTime,
      String? drinkLocation,
    }) {
  final activity = ResidentCareActivity(
    residentId: residentId, // Pass from parent or get from context
    activityName: activityName,
    mood: selectedResponse ?? 'neutral',
    attachments: attachments.map((e) => ActivityAttachment(
        fileName: e.split('/').last,
        filePath:e
    )).toList(),
    subActivityA: 'Amount Offered',
    subActivityAValue: amountOffered?.toString() ?? '0',
    subActivityB: 'Amount Consumed',
    subActivityBValue: amountConsumed?.toString() ?? '0',
    temperature: '', // Not used for food/drink activities
    notes: notesController.text +
        (drinkTime != null ? '\nDrink Time: ${drinkTime.format(context)}' : '') +
        (drinkLocation != null ? '\nDrink Location: $drinkLocation' : ''),
    handOffStaffId: subActivity ?? '',
  );
  onSave(activity);
  Navigator.of(context).pop();
}

class Tea extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Tea({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Tea> createState() => _TeaState();
}

class _TeaState extends State<Tea> {
  final List<String> attachments = [];
  double amountOffered = 0;
  double amountConsumed = 0;
  int? selectedHour;
  int? selectedMinute;
  String? subActivity;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
         width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Header(
                  title: 'Tea',
                  onBack: () => Navigator.of(context).pop(),
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Offered',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountOffered = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Consumed',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountConsumed = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                TimePicker(onTimeChanged: (int hour, int minute) {
                  setState(() {
                    selectedHour = hour;
                    selectedMinute = minute;
                  });
                }),
                SizedBox(height: 10),
                DropdownSelector(
                  label: 'Sub Activity',
                  options: ['Assisted', 'Independent', 'Declined', 'Not Available'],
                  onChanged: (value) {
                    setState(() {
                      subActivity = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                CustomTextField(
                  label: 'Notes',
                  icon: Icons.notes,
                  controller: notesController,
                ),
                SizedBox(height: 20),
                AttachmentButton(
                  attachments: attachments,
                  onAttachmentsSelected: (selectedAttachments) {
                    setState(() {
                      attachments.addAll(selectedAttachments);
                    });
                  },
                ),
                SizedBox(height: 20),
                SubmitButton(
                  onPressed: () => createFoodDrinkHandleSave(
                    context,
                    'Tea',
                    widget.residentUUID,selectedHour,
                selectedMinute,
                    null,
                    amountOffered,
                    amountConsumed,
                    notesController,
                    subActivity,
                    attachments,
                    widget.onSave,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class Lunch extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Lunch({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Lunch> createState() => _LunchState();
}

class _LunchState extends State<Lunch> {
  final List<String> attachments = [];
  double amountOffered = 0;
  double amountConsumed = 0;
  int? selectedHour;
  int? selectedMinute;
  String? subActivity;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
         width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Header(
                  title: 'Lunch',
                  onBack: () => Navigator.of(context).pop(),
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Offered',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountOffered = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Consumed',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountConsumed = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                TimePicker(onTimeChanged: (int hour, int minute) {
                  setState(() {
                    selectedHour = hour;
                    selectedMinute = minute;
                  });
                }),
                SizedBox(height: 10),
                DropdownSelector(
                  label: 'Sub Activity',
                  options: ['Assisted', 'Independent', 'Declined', 'Not Available'],
                  onChanged: (value) {
                    setState(() {
                      subActivity = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                CustomTextField(
                  label: 'Notes',
                  icon: Icons.notes,
                  controller: notesController,
                ),
                SizedBox(height: 20),
                AttachmentButton(
                  attachments: attachments,
                  onAttachmentsSelected: (selectedAttachments) {
                    setState(() {
                      attachments.addAll(selectedAttachments);
                    });
                  },
                ),
                SizedBox(height: 20),
                SubmitButton(
                  onPressed: () => createFoodDrinkHandleSave(
                    context,
                    'Lunch',
                    widget.residentUUID,selectedHour,
                selectedMinute,
                    null,
                    amountOffered,
                    amountConsumed,
                    notesController,
                    subActivity,
                    attachments,
                    widget.onSave,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class Supper extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Supper({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Supper> createState() => _SupperState();
}

class _SupperState extends State<Supper> {
  final List<String> attachments = [];
  double amountOffered = 0;
  double amountConsumed = 0;
  int? selectedHour;
  int? selectedMinute;
  String? subActivity;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
         width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Header(
                  title: 'Supper',
                  onBack: () => Navigator.of(context).pop(),
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Offered',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountOffered = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Consumed',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountConsumed = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                TimePicker(onTimeChanged: (int hour, int minute) {
                  setState(() {
                    selectedHour = hour;
                    selectedMinute = minute;
                  });
                }),
                SizedBox(height: 10),
                DropdownSelector(
                  label: 'Sub Activity',
                  options: ['Assisted', 'Independent', 'Declined', 'Not Available'],
                  onChanged: (value) {
                    setState(() {
                      subActivity = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                CustomTextField(
                  label: 'Notes',
                  icon: Icons.notes,
                  controller: notesController,
                ),
                SizedBox(height: 20),
                AttachmentButton(
                  attachments: attachments,
                  onAttachmentsSelected: (selectedAttachments) {
                    setState(() {
                      attachments.addAll(selectedAttachments);
                    });
                  },
                ),
                SizedBox(height: 20),
                SubmitButton(
                  onPressed: () => createFoodDrinkHandleSave(
                    context,
                    'Supper',
                    widget.residentUUID,selectedHour,
                selectedMinute,
                    null,
                    amountOffered,
                    amountConsumed,
                    notesController,
                    subActivity,
                    attachments,
                    widget.onSave,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class Breakfast extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Breakfast({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Breakfast> createState() => _BreakfastState();
}

class _BreakfastState extends State<Breakfast> {
  final List<String> attachments = [];
  double amountOffered = 0;
  double amountConsumed = 0;
  int? selectedHour;
  int? selectedMinute;
  String? subActivity;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
         width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Header(
                  title: 'Breakfast',
                  onBack: () => Navigator.of(context).pop(),
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Offered',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountOffered = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Consumed',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountConsumed = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                TimePicker(onTimeChanged: (int hour, int minute) {
                  setState(() {
                    selectedHour = hour;
                    selectedMinute = minute;
                  });
                }),
                SizedBox(height: 10),
                DropdownSelector(
                  label: 'Sub Activity',
                  options: ['Assisted', 'Independent', 'Declined', 'Not Available'],
                  onChanged: (value) {
                    setState(() {
                      subActivity = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                CustomTextField(
                  label: 'Notes',
                  icon: Icons.notes,
                  controller: notesController,
                ),
                SizedBox(height: 20),
                AttachmentButton(
                  attachments: attachments,
                  onAttachmentsSelected: (selectedAttachments) {
                    setState(() {
                      attachments.addAll(selectedAttachments);
                    });
                  },
                ),
                SizedBox(height: 20),
                SubmitButton(
                  onPressed: () => createFoodDrinkHandleSave(
                    context,
                    'Breakfast',
                    widget.residentUUID,selectedHour,
                selectedMinute,
                    null,
                    amountOffered,
                    amountConsumed,
                    notesController,
                    subActivity,
                    attachments,
                    widget.onSave,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class Soup extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Soup({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Soup> createState() => _SoupState();
}

class _SoupState extends State<Soup> {
  final List<String> attachments = [];
  double amountOffered = 0;
  double amountConsumed = 0;
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
         width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Header(
                  title: 'Soup',
                  onBack: () => Navigator.of(context).pop(),
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Offered',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountOffered = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Consumed',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountConsumed = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                TimePicker(onTimeChanged: (int hour, int minute) {
                  setState(() {
                    selectedHour = hour;
                    selectedMinute = minute;
                  });
                }),
                SizedBox(height: 10),
                ResponseType(onResponseSelected: (response) {
                  setState(() {
                    selectedResponse = response;
                  });
                }),
                SizedBox(height: 10),
                CustomTextField(
                  label: 'Notes',
                  icon: Icons.notes,
                  controller: notesController,
                ),
                SizedBox(height: 20),
                AttachmentButton(
                  attachments: attachments,
                  onAttachmentsSelected: (selectedAttachments) {
                    setState(() {
                      attachments.addAll(selectedAttachments);
                    });
                  },
                ),
                SizedBox(height: 20),
                SubmitButton(
                  onPressed: () => createFoodDrinkHandleSave(
                    context,
                    'Soup',
                    widget.residentUUID,selectedHour,
                selectedMinute,
                    selectedResponse,
                    amountOffered,
                    amountConsumed,
                    notesController,
                    null,
                    attachments,
                    widget.onSave,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Continue with similar implementation for other widgets...
// For brevity, I'll include a few more key ones:

class Water extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Water({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Water> createState() => _WaterState();
}

class _WaterState extends State<Water> {
  final List<String> attachments = [];
  double amountOffered = 0;
  double amountConsumed = 0;
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
         width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Header(
                  title: 'Water',
                  onBack: () => Navigator.of(context).pop(),
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Offered',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountOffered = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Consumed',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountConsumed = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                TimePicker(onTimeChanged: (int hour, int minute) {
                  setState(() {
                    selectedHour = hour;
                    selectedMinute = minute;
                  });
                }),
                SizedBox(height: 10),
                ResponseType(onResponseSelected: (response) {
                  setState(() {
                    selectedResponse = response;
                  });
                }),
                SizedBox(height: 10),
                CustomTextField(
                  label: 'Notes',
                  icon: Icons.notes,
                  controller: notesController,
                ),
                SizedBox(height: 20),
                AttachmentButton(
                  attachments: attachments,
                  onAttachmentsSelected: (selectedAttachments) {
                    setState(() {
                      attachments.addAll(selectedAttachments);
                    });
                  },
                ),
                SizedBox(height: 20),
                SubmitButton(
                  onPressed: () => createFoodDrinkHandleSave(
                    context,
                    'Water',
                    widget.residentUUID,selectedHour,
                selectedMinute,
                    selectedResponse,
                    amountOffered,
                    amountConsumed,
                    notesController,
                    null,
                    attachments,
                    widget.onSave,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class Coffee extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Coffee({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Coffee> createState() => _CoffeeState();
}

class _CoffeeState extends State<Coffee> {
  final List<String> attachments = [];
  double amountOffered = 0;
  double amountConsumed = 0;
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  TimeOfDay drinkTime = TimeOfDay(hour: 0, minute: 0);
  String drinkLocation = '';
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
         width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Header(
                  title: 'Coffee',
                  onBack: () => Navigator.of(context).pop(),
                ),
                SizedBox(height: 10),
                ResponseType(onResponseSelected: (response) {
                  setState(() {
                    selectedResponse = response;
                  });
                }),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Offered',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountOffered = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Consumed',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountConsumed = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                TimePicker(onTimeChanged: (int hour, int minute) {
                  setState(() {
                    selectedHour = hour;
                    selectedMinute = minute;
                  });
                }),
                SizedBox(height: 10),
                DrinkDetails(
                  initialTime: drinkTime,
                  initialLocation: drinkLocation,
                  onTimeChanged: (time) {
                    setState(() {
                      drinkTime = time;
                    });
                  },
                  onLocationChanged: (location) {
                    setState(() {
                      drinkLocation = location;
                    });
                  },
                ),
                SizedBox(height: 10),
                CustomTextField(
                  label: 'Notes',
                  icon: Icons.notes,
                  controller: notesController,
                ),
                SizedBox(height: 20),
                AttachmentButton(
                  attachments: attachments,
                  onAttachmentsSelected: (selectedAttachments) {
                    setState(() {
                      attachments.addAll(selectedAttachments);
                    });
                  },
                ),
                SizedBox(height: 20),
                SubmitButton(
                  onPressed: () => createFoodDrinkHandleSave(
                    context,
                    'Coffee',
                    widget.residentUUID,selectedHour,
                selectedMinute,
                    selectedResponse,
                    amountOffered,
                    amountConsumed,
                    notesController,
                    null,
                    attachments,
                    widget.onSave,
                    drinkTime: drinkTime,
                    drinkLocation: drinkLocation,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class FoodOrder extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const FoodOrder({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<FoodOrder> createState() => _FoodOrderState();
}

class _FoodOrderState extends State<FoodOrder> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
         width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Header(
                  title: 'Food Order',
                  onBack: () => Navigator.of(context).pop(),
                ),
                SizedBox(height: 10),
                ResponseType(onResponseSelected: (response) {
                  setState(() {
                    selectedResponse = response;
                  });
                }),
                SizedBox(height: 10),
                TimePicker(onTimeChanged: (int hour, int minute) {
                  setState(() {
                    selectedHour = hour;
                    selectedMinute = minute;
                  });
                }),
                SizedBox(height: 10),
                CustomTextField(
                  label: 'Notes',
                  icon: Icons.notes,
                  controller: notesController,
                ),
                SizedBox(height: 20),
                AttachmentButton(
                  attachments: attachments,
                  onAttachmentsSelected: (selectedAttachments) {
                    setState(() {
                      attachments.addAll(selectedAttachments);
                    });
                  },
                ),
                SizedBox(height: 20),
                SubmitButton(
                  onPressed: () => createFoodDrinkHandleSave(
                    context,
                    'Food Order',
                    widget.residentUUID,selectedHour,
                selectedMinute,
                    selectedResponse,
                    null, // No amount offered for food orders
                    null, // No amount consumed for food orders
                    notesController,
                    null,
                    attachments,
                    widget.onSave,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class Jelly extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Jelly({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Jelly> createState() => _JellyState();
}

class _JellyState extends State<Jelly> {
  final List<String> attachments = [];
  double amountOffered = 0;
  double amountConsumed = 0;
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
         width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Header(
                  title: 'Jelly',
                  onBack: () => Navigator.of(context).pop(),
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Offered',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountOffered = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Consumed',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountConsumed = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                TimePicker(onTimeChanged: (int hour, int minute) {
                  setState(() {
                    selectedHour = hour;
                    selectedMinute = minute;
                  });
                }),
                SizedBox(height: 10),
                ResponseType(onResponseSelected: (response) {
                  setState(() {
                    selectedResponse = response;
                  });
                }),
                SizedBox(height: 10),
                CustomTextField(
                  label: 'Notes',
                  icon: Icons.notes,
                  controller: notesController,
                ),
                SizedBox(height: 20),
                AttachmentButton(
                  attachments: attachments,
                  onAttachmentsSelected: (selectedAttachments) {
                    setState(() {
                      attachments.addAll(selectedAttachments);
                    });
                  },
                ),
                SizedBox(height: 20),
                SubmitButton(
                  onPressed: () => createFoodDrinkHandleSave(
                    context,
                    'Jelly',
                    widget.residentUUID,selectedHour,
                selectedMinute,
                    selectedResponse,
                    amountOffered,
                    amountConsumed,
                    notesController,
                    null,
                    attachments,
                    widget.onSave,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class IceLolly extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const IceLolly({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<IceLolly> createState() => _IceLollyState();
}

class _IceLollyState extends State<IceLolly> {
  final List<String> attachments = [];
  double amountOffered = 0;
  double amountConsumed = 0;
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
         width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Header(
                  title: 'Ice Lolly',
                  onBack: () => Navigator.of(context).pop(),
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Offered',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountOffered = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Consumed',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountConsumed = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                TimePicker(onTimeChanged: (int hour, int minute) {
                  setState(() {
                    selectedHour = hour;
                    selectedMinute = minute;
                  });
                }),
                SizedBox(height: 10),
                ResponseType(onResponseSelected: (response) {
                  setState(() {
                    selectedResponse = response;
                  });
                }),
                SizedBox(height: 10),
                CustomTextField(
                  label: 'Notes',
                  icon: Icons.notes,
                  controller: notesController,
                ),
                SizedBox(height: 20),
                AttachmentButton(
                  attachments: attachments,
                  onAttachmentsSelected: (selectedAttachments) {
                    setState(() {
                      attachments.addAll(selectedAttachments);
                    });
                  },
                ),
                SizedBox(height: 20),
                SubmitButton(
                  onPressed: () => createFoodDrinkHandleSave(
                    context,
                    'Ice Lolly',
                    widget.residentUUID,selectedHour,
                selectedMinute,
                    selectedResponse,
                    amountOffered,
                    amountConsumed,
                    notesController,
                    null,
                    attachments,
                    widget.onSave,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class Pudding extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Pudding({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Pudding> createState() => _PuddingState();
}

class _PuddingState extends State<Pudding> {
  final List<String> attachments = [];
  double amountOffered = 0;
  double amountConsumed = 0;
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
         width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Header(
                  title: 'Pudding',
                  onBack: () => Navigator.of(context).pop(),
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Offered',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountOffered = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Consumed',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountConsumed = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                TimePicker(onTimeChanged: (int hour, int minute) {
                  setState(() {
                    selectedHour = hour;
                    selectedMinute = minute;
                  });
                }),
                SizedBox(height: 10),
                ResponseType(onResponseSelected: (response) {
                  setState(() {
                    selectedResponse = response;
                  });
                }),
                SizedBox(height: 10),
                CustomTextField(
                  label: 'Notes',
                  icon: Icons.notes,
                  controller: notesController,
                ),
                SizedBox(height: 20),
                AttachmentButton(
                  attachments: attachments,
                  onAttachmentsSelected: (selectedAttachments) {
                    setState(() {
                      attachments.addAll(selectedAttachments);
                    });
                  },
                ),
                SizedBox(height: 20),
                SubmitButton(
                  onPressed: () => createFoodDrinkHandleSave(
                    context,
                    'Pudding',
                    widget.residentUUID,selectedHour,
                selectedMinute,
                    selectedResponse,
                    amountOffered,
                    amountConsumed,
                    notesController,
                    null,
                    attachments,
                    widget.onSave,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class Drink extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Drink({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Drink> createState() => _DrinkState();
}

class _DrinkState extends State<Drink> {
  final List<String> attachments = [];
  double amountOffered = 0;
  double amountConsumed = 0;
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
         width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Header(
                  title: 'Drink',
                  onBack: () => Navigator.of(context).pop(),
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Offered',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountOffered = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Consumed',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountConsumed = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                TimePicker(onTimeChanged: (int hour, int minute) {
                  setState(() {
                    selectedHour = hour;
                    selectedMinute = minute;
                  });
                }),
                SizedBox(height: 10),
                ResponseType(onResponseSelected: (response) {
                  setState(() {
                    selectedResponse = response;
                  });
                }),
                SizedBox(height: 10),
                CustomTextField(
                  label: 'Notes',
                  icon: Icons.notes,
                  controller: notesController,
                ),
                SizedBox(height: 20),
                AttachmentButton(
                  attachments: attachments,
                  onAttachmentsSelected: (selectedAttachments) {
                    setState(() {
                      attachments.addAll(selectedAttachments);
                    });
                  },
                ),
                SizedBox(height: 20),
                SubmitButton(
                  onPressed: () => createFoodDrinkHandleSave(
                    context,
                    'Drink',
                    widget.residentUUID,selectedHour,
                selectedMinute,
                    selectedResponse,
                    amountOffered,
                    amountConsumed,
                    notesController,
                    null,
                    attachments,
                    widget.onSave,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class Juice extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Juice({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Juice> createState() => _JuiceState();
}

class _JuiceState extends State<Juice> {
  final List<String> attachments = [];
  double amountOffered = 0;
  double amountConsumed = 0;
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
         width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Header(
                  title: 'Juice',
                  onBack: () => Navigator.of(context).pop(),
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Offered',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountOffered = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Consumed',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountConsumed = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                TimePicker(onTimeChanged: (int hour, int minute) {
                  setState(() {
                    selectedHour = hour;
                    selectedMinute = minute;
                  });
                }),
                SizedBox(height: 10),
                ResponseType(onResponseSelected: (response) {
                  setState(() {
                    selectedResponse = response;
                  });
                }),
                SizedBox(height: 10),
                CustomTextField(
                  label: 'Notes',
                  icon: Icons.notes,
                  controller: notesController,
                ),
                SizedBox(height: 20),
                AttachmentButton(
                  attachments: attachments,
                  onAttachmentsSelected: (selectedAttachments) {
                    setState(() {
                      attachments.addAll(selectedAttachments);
                    });
                  },
                ),
                SizedBox(height: 20),
                SubmitButton(
                  onPressed: () => createFoodDrinkHandleSave(
                    context,
                    'Juice',
                    widget.residentUUID,selectedHour,
                selectedMinute,
                    selectedResponse,
                    amountOffered,
                    amountConsumed,
                    notesController,
                    null,
                    attachments,
                    widget.onSave,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class AlcoholicDrink extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const AlcoholicDrink({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<AlcoholicDrink> createState() => _AlcoholicDrinkState();
}

class _AlcoholicDrinkState extends State<AlcoholicDrink> {
  final List<String> attachments = [];
  double amountOffered = 0;
  double amountConsumed = 0;
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
         width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Header(
                  title: 'Alcoholic Drink',
                  onBack: () => Navigator.of(context).pop(),
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Offered',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountOffered = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Consumed',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountConsumed = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                TimePicker(onTimeChanged: (int hour, int minute) {
                  setState(() {
                    selectedHour = hour;
                    selectedMinute = minute;
                  });
                }),
                SizedBox(height: 10),
                ResponseType(onResponseSelected: (response) {
                  setState(() {
                    selectedResponse = response;
                  });
                }),
                SizedBox(height: 10),
                CustomTextField(
                  label: 'Notes',
                  icon: Icons.notes,
                  controller: notesController,
                ),
                SizedBox(height: 20),
                AttachmentButton(
                  attachments: attachments,
                  onAttachmentsSelected: (selectedAttachments) {
                    setState(() {
                      attachments.addAll(selectedAttachments);
                    });
                  },
                ),
                SizedBox(height: 20),
                SubmitButton(
                  onPressed: () => createFoodDrinkHandleSave(
                    context,
                    'Alcoholic Drink',
                    widget.residentUUID,selectedHour,
                selectedMinute,
                    selectedResponse,
                    amountOffered,
                    amountConsumed,
                    notesController,
                    null,
                    attachments,
                    widget.onSave,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class Wine extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Wine({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Wine> createState() => _WineState();
}

class _WineState extends State<Wine> {
  final List<String> attachments = [];
  double amountOffered = 0;
  double amountConsumed = 0;
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
         width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Header(
                  title: 'Wine',
                  onBack: () => Navigator.of(context).pop(),
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Offered',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountOffered = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Consumed',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountConsumed = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                TimePicker(onTimeChanged: (int hour, int minute) {
                  setState(() {
                    selectedHour = hour;
                    selectedMinute = minute;
                  });
                }),
                SizedBox(height: 10),
                ResponseType(onResponseSelected: (response) {
                  setState(() {
                    selectedResponse = response;
                  });
                }),
                SizedBox(height: 10),
                CustomTextField(
                  label: 'Notes',
                  icon: Icons.notes,
                  controller: notesController,
                ),
                SizedBox(height: 20),
                AttachmentButton(
                  attachments: attachments,
                  onAttachmentsSelected: (selectedAttachments) {
                    setState(() {
                      attachments.addAll(selectedAttachments);
                    });
                  },
                ),
                SizedBox(height: 20),
                SubmitButton(
                  onPressed: () => createFoodDrinkHandleSave(
                    context,
                    'Wine',
                    widget.residentUUID,selectedHour,
                selectedMinute,
                    selectedResponse,
                    amountOffered,
                    amountConsumed,
                    notesController,
                    null,
                    attachments,
                    widget.onSave,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class Milk extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Milk({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Milk> createState() => _MilkState();
}

class _MilkState extends State<Milk> {
  final List<String> attachments = [];
  double amountOffered = 0;
  double amountConsumed = 0;
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
         width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Header(
                  title: 'Milk',
                  onBack: () => Navigator.of(context).pop(),
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Offered',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountOffered = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Consumed',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountConsumed = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                TimePicker(onTimeChanged: (int hour, int minute) {
                  setState(() {
                    selectedHour = hour;
                    selectedMinute = minute;
                  });
                }),
                SizedBox(height: 10),
                ResponseType(onResponseSelected: (response) {
                  setState(() {
                    selectedResponse = response;
                  });
                }),
                SizedBox(height: 10),
                CustomTextField(
                  label: 'Notes',
                  icon: Icons.notes,
                  controller: notesController,
                ),
                SizedBox(height: 20),
                AttachmentButton(
                  attachments: attachments,
                  onAttachmentsSelected: (selectedAttachments) {
                    setState(() {
                      attachments.addAll(selectedAttachments);
                    });
                  },
                ),
                SizedBox(height: 20),
                SubmitButton(
                  onPressed: () => createFoodDrinkHandleSave(
                    context,
                    'Milk',
                    widget.residentUUID,selectedHour,
                selectedMinute,
                    selectedResponse,
                    amountOffered,
                    amountConsumed,
                    notesController,
                    null,
                    attachments,
                    widget.onSave,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class Snack extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Snack({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Snack> createState() => _SnackState();
}

class _SnackState extends State<Snack> {
  final List<String> attachments = [];
  double amountOffered = 0;
  double amountConsumed = 0;
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? subActivity;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
         width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Header(
                  title: 'Snack',
                  onBack: () => Navigator.of(context).pop(),
                ),
                SizedBox(height: 10),
                ResponseType(onResponseSelected: (response) {
                  setState(() {
                    selectedResponse = response;
                  });
                }),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Offered',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountOffered = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Consumed',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountConsumed = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                TimePicker(onTimeChanged: (int hour, int minute) {
                  setState(() {
                    selectedHour = hour;
                    selectedMinute = minute;
                  });
                }),
                SizedBox(height: 10),
                DropdownSelector(
                  label: 'Sub Activity',
                  options: ['Biscuits', 'Chocolate', 'Fruit', 'Nuts', 'Other'],
                  onChanged: (value) {
                    setState(() {
                      subActivity = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                CustomTextField(
                  label: 'Notes',
                  icon: Icons.notes,
                  controller: notesController,
                ),
                SizedBox(height: 20),
                AttachmentButton(
                  attachments: attachments,
                  onAttachmentsSelected: (selectedAttachments) {
                    setState(() {
                      attachments.addAll(selectedAttachments);
                    });
                  },
                ),
                SizedBox(height: 20),
                SubmitButton(
                  onPressed: () => createFoodDrinkHandleSave(
                    context,
                    'Snack',
                    widget.residentUUID,selectedHour,
                selectedMinute,
                    selectedResponse,
                    amountOffered,
                    amountConsumed,
                    notesController,
                    subActivity,
                    attachments,
                    widget.onSave,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class MilkShake extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const MilkShake({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<MilkShake> createState() => _MilkShakeState();
}

class _MilkShakeState extends State<MilkShake> {
  final List<String> attachments = [];
  double amountOffered = 0;
  double amountConsumed = 0;
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  TimeOfDay drinkTime = TimeOfDay(hour: 0, minute: 0);
  String drinkLocation = '';
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
         width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Header(
                  title: 'Milk Shake',
                  onBack: () => Navigator.of(context).pop(),
                ),
                SizedBox(height: 10),
                ResponseType(onResponseSelected: (response) {
                  setState(() {
                    selectedResponse = response;
                  });
                }),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Offered',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountOffered = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Consumed',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountConsumed = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                DrinkDetails(
                  initialTime: drinkTime,
                  initialLocation: drinkLocation,
                  onTimeChanged: (time) {
                    setState(() {
                      drinkTime = time;
                    });
                  },
                  onLocationChanged: (location) {
                    setState(() {
                      drinkLocation = location;
                    });
                  },
                ),
                SizedBox(height: 10),
                CustomTextField(
                  label: 'Notes',
                  icon: Icons.notes,
                  controller: notesController,
                ),
                SizedBox(height: 20),
                AttachmentButton(
                  attachments: attachments,
                  onAttachmentsSelected: (selectedAttachments) {
                    setState(() {
                      attachments.addAll(selectedAttachments);
                    });
                  },
                ),
                SizedBox(height: 20),
                SubmitButton(
                  onPressed: () => createFoodDrinkHandleSave(
                    context,
                    'Milk Shake',
                    widget.residentUUID,selectedHour,
                selectedMinute,
                    selectedResponse,
                    amountOffered,
                    amountConsumed,
                    notesController,
                    null,
                    attachments,
                    widget.onSave,
                    drinkTime: drinkTime,
                    drinkLocation: drinkLocation,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ThickDrink extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const ThickDrink({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<ThickDrink> createState() => _ThickDrinkState();
}

class _ThickDrinkState extends State<ThickDrink> {
  final List<String> attachments = [];
  double amountOffered = 0;
  double amountConsumed = 0;
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  TimeOfDay drinkTime = TimeOfDay(hour: 0, minute: 0);
  String drinkLocation = '';
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
         width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Header(
                  title: 'Thick Drink',
                  onBack: () => Navigator.of(context).pop(),
                ),
                SizedBox(height: 10),
                ResponseType(onResponseSelected: (response) {
                  setState(() {
                    selectedResponse = response;
                  });
                }),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Offered',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountOffered = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                AmountSlider(
                  label: 'Amount Consumed',
                  initialValue: 0,
                  onChanged: (value) {
                    setState(() {
                      amountConsumed = value;
                    });
                  },
                ),
                SizedBox(height: 10),
                TimePicker(onTimeChanged: (int hour, int minute) {
                  setState(() {
                    selectedHour = hour;
                    selectedMinute = minute;
                  });
                }),
                SizedBox(height: 10),
                DrinkDetails(
                  initialTime: drinkTime,
                  initialLocation: drinkLocation,
                  onTimeChanged: (time) {
                    setState(() {
                      drinkTime = time;
                    });
                  },
                  onLocationChanged: (location) {
                    setState(() {
                      drinkLocation = location;
                    });
                  },
                ),
                SizedBox(height: 10),
                CustomTextField(
                  label: 'Notes',
                  icon: Icons.notes,
                  controller: notesController,
                ),
                SizedBox(height: 20),
                AttachmentButton(
                  attachments: attachments,
                  onAttachmentsSelected: (selectedAttachments) {
                    setState(() {
                      attachments.addAll(selectedAttachments);
                    });
                  },
                ),
                SizedBox(height: 20),
                SubmitButton(
                  onPressed: () => createFoodDrinkHandleSave(
                    context,
                    'Thick Drink',
                    widget.residentUUID,selectedHour,
                selectedMinute,
                    selectedResponse,
                    amountOffered,
                    amountConsumed,
                    notesController,
                    null,
                    attachments,
                    widget.onSave,
                    drinkTime: drinkTime,
                    drinkLocation: drinkLocation,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}