import 'package:flutter/material.dart';
import '../../../components/careActivitesHeader.dart';
import '../../../components/careActivitesSubmit.dart';
import '../../../components/careActivitesText.dart';
import '../../../components/timePicker.dart';
import '../../../models/activity.dart';
import '../../../models/staff.dart';

// Common handler function for medical activities and observations
void createMedicalHandleSave(
    BuildContext context,
    String activityName,
    String residentId,
    int? selectedHour,
    int? selectedMinute,
    TextEditingController notesController,
    Function(ResidentCareActivity) onSave,
    {String? medicalType,
      String? additionalValue}
    ) {
  final activity = ResidentCareActivity(
    residentId: residentId, // Pass from parent or get from context
    activityName: activityName,
    mood: medicalType ?? 'neutral', // Use medical type as mood indicator
    attachments: [], // No attachments for most medical observations
    subActivityA: 'Duration/Time',
    subActivityAValue: selectedHour != null && selectedMinute != null
        ? '${selectedHour}:${selectedMinute.toString().padLeft(2, '0')}'
        : '',
    subActivityB: 'Medical Type',
    subActivityBValue: medicalType ?? 'Medical Observation',
    temperature: additionalValue ?? '', // Use for readings/values
    notes: notesController.text,
    handOffStaffId: '',
  );
  onSave(activity);
  Navigator.of(context).pop();
}

// Special handler for complex medical forms with multiple fields
void createComplexMedicalHandleSave(
    BuildContext context,
    String activityName,
    String residentId,
    int? selectedHour,
    int? selectedMinute,
    Map<String, TextEditingController> controllers,
    Function(ResidentCareActivity) onSave,
    {String? medicalType}
    ) {
  // Combine all field values into notes
  String combinedNotes = '';
  controllers.forEach((key, controller) {
    if (controller.text.isNotEmpty) {
      combinedNotes += '$key: ${controller.text}\n';
    }
  });

  final activity = ResidentCareActivity(
    residentId: residentId, // Pass from parent or get from context
    activityName: activityName,
    mood: medicalType ?? 'neutral', // Use medical type as mood indicator
    attachments: [],
    subActivityA: 'Duration/Time',
    subActivityAValue: selectedHour != null && selectedMinute != null
        ? '${selectedHour}:${selectedMinute.toString().padLeft(2, '0')}'
        : '',
    subActivityB: 'Medical Type',
    subActivityBValue: medicalType ?? 'Medical Procedure',
    temperature: '',
    notes: combinedNotes,
    handOffStaffId: '',
  );
  onSave(activity);
  Navigator.of(context).pop();
}

class Medication extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Medication({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Medication> createState() => _MedicationState();
}

class _MedicationState extends State<Medication> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Medication',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Medication',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Medication Administration',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Doctor extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Doctor({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Doctor> createState() => _DoctorState();
}

class _DoctorState extends State<Doctor> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Doctor',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Doctor',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Doctor Visit',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Nurse extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Nurse({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Nurse> createState() => _NurseState();
}

class _NurseState extends State<Nurse> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Nurse',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Nurse',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Nursing Care',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Professor extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Professor({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Professor> createState() => _ProfessorState();
}

class _ProfessorState extends State<Professor> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Professor',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Professor',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Medical Consultation',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Sick extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Sick({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Sick> createState() => _SickState();
}

class _SickState extends State<Sick> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Sick',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Sick',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Illness Observation',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class InPain extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const InPain({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<InPain> createState() => _InPainState();
}

class _InPainState extends State<InPain> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'In Pain',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'In Pain',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Pain Assessment',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class CallDoctor extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CallDoctor({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CallDoctor> createState() => _CallDoctorState();
}

class _CallDoctorState extends State<CallDoctor> {
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Call Doctor',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Phone Number', icon: Icons.phone, controller: phoneController),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createComplexMedicalHandleSave(
                context,
                'Call Doctor',
                widget.residentUUID,
                selectedHour,
                selectedMinute,
                {
                  'Phone Number': phoneController,
                  'Notes': notesController,
                },
                widget.onSave,
                medicalType: 'Emergency Call',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Ambulance extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Ambulance({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Ambulance> createState() => _AmbulanceState();
}

class _AmbulanceState extends State<Ambulance> {
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Ambulance',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Phone Number', icon: Icons.phone, controller: phoneController),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createComplexMedicalHandleSave(
                context,
                'Ambulance',
                widget.residentUUID,selectedHour,
                selectedMinute,
                {
                  'Phone Number': phoneController,
                  'Emergency Notes': notesController,
                },
                widget.onSave,
                medicalType: 'Emergency Response',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Wound extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Wound({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Wound> createState() => _WoundState();
}

class _WoundState extends State<Wound> {
  final TextEditingController woundTypeController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Wound',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Wound Type', icon: Icons.notes, controller: woundTypeController),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createComplexMedicalHandleSave(
                context,
                'Wound',
                widget.residentUUID,selectedHour,
                selectedMinute,
                {
                  'Wound Type': woundTypeController,
                  'Care Notes': notesController,
                },
                widget.onSave,
                medicalType: 'Wound Care',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class FootSplint extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const FootSplint({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<FootSplint> createState() => _FootSplintState();
}

class _FootSplintState extends State<FootSplint> {
  final TextEditingController splintTypeController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Foot Splint',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Foot Splint Type', icon: Icons.notes, controller: splintTypeController),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createComplexMedicalHandleSave(
                context,
                'Foot Splint',
                widget.residentUUID,selectedHour,
                selectedMinute,
                {
                  'Splint Type': splintTypeController,
                  'Application Notes': notesController,
                },
                widget.onSave,
                medicalType: 'Medical Device',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class BloodSugar extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const BloodSugar({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<BloodSugar> createState() => _BloodSugarState();
}

class _BloodSugarState extends State<BloodSugar> {
  final TextEditingController levelController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Blood Sugar',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Blood Sugar Level', icon: Icons.notes, controller: levelController),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Blood Sugar',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Vital Signs',
                additionalValue: levelController.text,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class BloodPressure extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const BloodPressure({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<BloodPressure> createState() => _BloodPressureState();
}

class _BloodPressureState extends State<BloodPressure> {
  final TextEditingController levelController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Blood Pressure',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Blood Pressure Level', icon: Icons.notes, controller: levelController),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Blood Pressure',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Vital Signs',
                additionalValue: levelController.text,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class CoronaVirus extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CoronaVirus({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CoronaVirus> createState() => _CoronaVirusState();
}

class _CoronaVirusState extends State<CoronaVirus> {
  final TextEditingController levelController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Corona Virus',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Test Result/Status', icon: Icons.notes, controller: levelController),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Corona Virus',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Health Screening',
                additionalValue: levelController.text,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Pulse extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Pulse({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Pulse> createState() => _PulseState();
}

class _PulseState extends State<Pulse> {
  final TextEditingController rateController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Pulse',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Pulse Rate', icon: Icons.notes, controller: rateController),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Pulse',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Vital Signs',
                additionalValue: rateController.text,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class PRNMedication extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const PRNMedication({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<PRNMedication> createState() => _PRNMedicationState();
}

class _PRNMedicationState extends State<PRNMedication> {
  final TextEditingController typeController = TextEditingController();
  final TextEditingController dosageController = TextEditingController();
  final TextEditingController frequencyController = TextEditingController();
  final TextEditingController durationController = TextEditingController();
  final TextEditingController reasonController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'PRN Medication',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Medication Type', icon: Icons.notes, controller: typeController),
              SizedBox(height: 10),
              CustomTextField(label: 'Medication Dosage', icon: Icons.notes, controller: dosageController),
              SizedBox(height: 10),
              CustomTextField(label: 'Medication Frequency', icon: Icons.notes, controller: frequencyController),
              SizedBox(height: 10),
              CustomTextField(label: 'Medication Duration', icon: Icons.notes, controller: durationController),
              SizedBox(height: 10),
              CustomTextField(label: 'Medication Reason', icon: Icons.notes, controller: reasonController),
              SizedBox(height: 10),
              CustomTextField(label: 'Medication Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createComplexMedicalHandleSave(
                context,
                'PRN Medication',
                widget.residentUUID,selectedHour,
                selectedMinute,
                {
                  'Medication Type': typeController,
                  'Dosage': dosageController,
                  'Frequency': frequencyController,
                  'Duration': durationController,
                  'Reason': reasonController,
                  'Additional Notes': notesController,
                },
                widget.onSave,
                medicalType: 'PRN Medication Administration',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class HandSplint extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const HandSplint({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<HandSplint> createState() => _HandSplintState();
}

class _HandSplintState extends State<HandSplint> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Hand Splint',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Hand Splint',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Medical Device',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class BloodINR extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const BloodINR({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<BloodINR> createState() => _BloodINRState();
}

class _BloodINRState extends State<BloodINR> {
  final TextEditingController levelController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Blood INR',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Blood INR Level', icon: Icons.notes, controller: levelController),
              SizedBox(height: 10),
              CustomTextField(label: 'Blood INR Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Blood INR',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Laboratory Test',
                additionalValue: levelController.text,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Specialist extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Specialist({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Specialist> createState() => _SpecialistState();
}

class _SpecialistState extends State<Specialist> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Specialist',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Specialist Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Specialist',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Specialist Consultation',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class HeatPack extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const HeatPack({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<HeatPack> createState() => _HeatPackState();
}

class _HeatPackState extends State<HeatPack> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Heat Pack',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Heat Pack Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Heat Pack',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Therapeutic Treatment',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Compress extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Compress({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Compress> createState() => _CompressState();
}

class _CompressState extends State<Compress> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Compress',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Compress Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Compress',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Therapeutic Treatment',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class SkinIntegrity extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const SkinIntegrity({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<SkinIntegrity> createState() => _SkinIntegrityState();
}

class _SkinIntegrityState extends State<SkinIntegrity> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Skin Integrity',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Skin Integrity Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Skin Integrity',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Skin Assessment',
              )),
            ],
          ),
        ),
      ),
    );
  }
}


class Vitals extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Vitals({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Vitals> createState() => _VitalsState();
}

class _VitalsState extends State<Vitals> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Vitals',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Vitals Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Vitals',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Vital Signs Assessment',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Conscious extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Conscious({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Conscious> createState() => _ConsciousState();
}

class _ConsciousState extends State<Conscious> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Conscious',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Conscious Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Conscious',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Consciousness Assessment',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class News extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const News({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<News> createState() => _NewsState();
}

class _NewsState extends State<News> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'News',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'News Description', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'News',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'NEWS Score Assessment',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class EarSyringe extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const EarSyringe({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<EarSyringe> createState() => _EarSyringeState();
}

class _EarSyringeState extends State<EarSyringe> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Ear Syringe',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Ear Syringe Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Ear Syringe',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Medical Procedure',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Insulin extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Insulin({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Insulin> createState() => _InsulinState();
}

class _InsulinState extends State<Insulin> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Insulin',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Insulin',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Insulin Administration',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class IVLineFlush extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const IVLineFlush({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<IVLineFlush> createState() => _IVLineFlushState();
}

class _IVLineFlushState extends State<IVLineFlush> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'IV Line Flush',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'IV Line Flush',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'IV Care',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Multistrix extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Multistrix({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Multistrix> createState() => _MultistrixState();
}

class _MultistrixState extends State<Multistrix> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Multistrix',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Multistrix',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Urine Test',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class BloodTest extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const BloodTest({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<BloodTest> createState() => _BloodTestState();
}

class _BloodTestState extends State<BloodTest> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Blood Test',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Blood Test',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Laboratory Test',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class NeuroObs extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const NeuroObs({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<NeuroObs> createState() => _NeuroObsState();
}

class _NeuroObsState extends State<NeuroObs> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Neuro Obs',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Neuro Obs',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Neurological Assessment',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class SoftSign extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const SoftSign({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<SoftSign> createState() => _SoftSignState();
}

class _SoftSignState extends State<SoftSign> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Soft Sign',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Soft Sign',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Clinical Observation',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class PAPTherapy extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const PAPTherapy({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<PAPTherapy> createState() => _PAPTherapyState();
}

class _PAPTherapyState extends State<PAPTherapy> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'PAP Therapy',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'PAP Therapy',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Respiratory Therapy',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class OxygenFever extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const OxygenFever({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<OxygenFever> createState() => _OxygenFeverState();
}

class _OxygenFeverState extends State<OxygenFever> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Oxygen Fever',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Oxygen Fever',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Oxygen Therapy',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class NewCatheter extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const NewCatheter({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<NewCatheter> createState() => _NewCatheterState();
}

class _NewCatheterState extends State<NewCatheter> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'New Catheter',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'New Catheter',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Catheter Care',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Washout extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Washout({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Washout> createState() => _WashoutState();
}

class _WashoutState extends State<Washout> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Washout',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Washout',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Bladder Care',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class AddBag extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const AddBag({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<AddBag> createState() => _AddBagState();
}

class _AddBagState extends State<AddBag> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Add Bag',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createMedicalHandleSave(
                context,
                'Add Bag',
                widget.residentUUID,selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                medicalType: 'Equipment Management',
              )),
            ],
          ),
        ),
      ),
    );
  }
}


//

class Cleaned extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Cleaned({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Cleaned> createState() => _CleanedState();
}

class _CleanedState extends State<Cleaned> {
  int? selectedHour;
  int? selectedMinute;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Cleaned',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMedicalHandleSave(
                  context,
                  'Cleaned',
                  widget.residentUUID,
                  selectedHour,
                  selectedMinute,
                  notesController,
                  widget.onSave,
                  medicalType: 'Personal Care',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class PEF extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const PEF({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<PEF> createState() => _PEFState();
}

class _PEFState extends State<PEF> {
  int? selectedHour;
  int? selectedMinute;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'PEF',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMedicalHandleSave(
                  context,
                  'PEF',
                  widget.residentUUID,
                  selectedHour,
                  selectedMinute,
                  notesController,
                  widget.onSave,
                  medicalType: 'Respiratory Care',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class EpilepticSeizure extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const EpilepticSeizure({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<EpilepticSeizure> createState() => _EpilepticSeizureState();
}

class _EpilepticSeizureState extends State<EpilepticSeizure> {
  int? selectedHour;
  int? selectedMinute;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Epileptic Seizure',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMedicalHandleSave(
                  context,
                  'Epileptic Seizure',widget.residentUUID,selectedHour,
                selectedMinute,
                  notesController,
                  widget.onSave,
                  medicalType: 'Neurological Care',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Stoma extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Stoma({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Stoma> createState() => _StomaState();
}

class _StomaState extends State<Stoma> {
  int? selectedHour;
  int? selectedMinute;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Stoma',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMedicalHandleSave(
                  context,
                  'Stoma',widget.residentUUID,selectedHour,
                selectedMinute,
                  notesController,
                  widget.onSave,
                  medicalType: 'Stoma Care',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Tracheostoma extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Tracheostoma({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Tracheostoma> createState() => _TracheostomaState();
}

class _TracheostomaState extends State<Tracheostoma> {
  int? selectedHour;
  int? selectedMinute;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Tracheostoma',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMedicalHandleSave(
                  context,
                  'Tracheostoma',widget.residentUUID,selectedHour,
                selectedMinute,
                  notesController,
                  widget.onSave,
                  medicalType: 'Respiratory Care',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Hospital extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Hospital({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Hospital> createState() => _HospitalState();
}

class _HospitalState extends State<Hospital> {
  int? selectedHour;
  int? selectedMinute;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Hospital',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMedicalHandleSave(
                  context,
                  'Hospital',widget.residentUUID,selectedHour,
                selectedMinute,
                  notesController,
                  widget.onSave,
                  medicalType: 'Hospital Visit',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Clinic extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Clinic({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Clinic> createState() => _ClinicState();
}

class _ClinicState extends State<Clinic> {
  int? selectedHour;
  int? selectedMinute;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Clinic',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMedicalHandleSave(
                  context,
                  'Clinic',widget.residentUUID,selectedHour,
                selectedMinute,
                  notesController,
                  widget.onSave,
                  medicalType: 'Clinic Visit',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Enema extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Enema({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Enema> createState() => _EnemaState();
}

class _EnemaState extends State<Enema> {
  int? selectedHour;
  int? selectedMinute;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Enema',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMedicalHandleSave(
                  context,
                  'Enema',widget.residentUUID,selectedHour,
                selectedMinute,
                  notesController,
                  widget.onSave,
                  medicalType: 'Bowel Care',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Suction extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Suction({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Suction> createState() => _SuctionState();
}

class _SuctionState extends State<Suction> {
  int? selectedHour;
  int? selectedMinute;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Suction',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMedicalHandleSave(
                  context,
                  'Suction',widget.residentUUID,selectedHour,
                selectedMinute,
                  notesController,
                  widget.onSave,
                  medicalType: 'Respiratory Care',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Sample extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Sample({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Sample> createState() => _SampleState();
}

class _SampleState extends State<Sample> {
  int? selectedHour;
  int? selectedMinute;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Sample',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createMedicalHandleSave(
                  context,
                  'Sample',widget.residentUUID,selectedHour,
                selectedMinute,
                  notesController,
                  widget.onSave,
                  medicalType: 'Sample Collection',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}