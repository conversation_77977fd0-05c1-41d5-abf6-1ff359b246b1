import 'package:carerez/components/careActivitesThumbs.dart';
import 'package:carerez/components/timePicker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../../../components/attachmentButton.dart';
import '../../../components/careActivitesDropDown.dart';
import '../../../components/careActivitesHeader.dart';
import '../../../components/careActivitesSubmit.dart';
import '../../../components/careActivitesText.dart';
import '../../../models/activity.dart';
import '../../../models/staff.dart';

// Handler function for hygiene/personal care activities with full fields
void createHygieneHandleSave(
    BuildContext context,
    String activityName,
    String residentId,
    int? selectedHour,
    int? selectedMinute,
    String? selectedSubActivity,
    String? temperature,
    String? selectedResponse,
    List<String> attachments,
    String? selectedHandOff,
    Map<String, TextEditingController> controllers,
    Function(ResidentCareActivity) onSave,
    ) {
  final activity = ResidentCareActivity(
    residentId: residentId, // Pass from parent or get from context
    activityName: activityName,
    mood: selectedResponse ?? 'neutral',
    attachments: attachments.map((e) => ActivityAttachment(
        fileName: e.split('/').last,
        filePath:e
    )).toList(),
    subActivityA: 'Duration/Time',
    subActivityAValue: selectedHour != null && selectedMinute != null
        ? '${selectedHour}:${selectedMinute.toString().padLeft(2, '0')}'
        : '',
    subActivityB: 'Sub Activity',
    subActivityBValue: selectedSubActivity ?? 'Not specified',
    temperature: temperature.toString() ?? '',
    notes: controllers['notes']?.text ?? '',
    handOffStaffId: selectedHandOff ?? '',
  );
  onSave(activity);
  Navigator.of(context).pop();
}

// Simplified handler for widgets without time picker
void createSimpleHygieneHandleSave(
    BuildContext context,
    String activityName,
    String residentId,
    String? selectedSubActivity,
    String? temperature,
    String? selectedResponse,
    List<String> attachments,
    String? selectedHandOff,
    Map<String, TextEditingController> controllers,
    Function(ResidentCareActivity) onSave,
    ) {
  final activity = ResidentCareActivity(
    residentId: residentId, // Pass from parent or get from context
    activityName: activityName,
    mood: selectedResponse ?? 'neutral',
    attachments: attachments.map((e) => ActivityAttachment(
        fileName: e.split('/').last,
        filePath:e
    )).toList(),
    subActivityA: 'Sub Activity',
    subActivityAValue: selectedSubActivity ?? 'Not specified',
    subActivityB: 'Hand Off Staff',
    subActivityBValue: selectedHandOff ?? 'Not specified',
    temperature: temperature ?? '',
    notes: controllers['notes']?.text ?? '',
    handOffStaffId: selectedHandOff ?? '',
  );
  onSave(activity);
  Navigator.of(context).pop();
}

// Widgets with proper onSave implementation

class WashClothes extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;

  WashClothes({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<WashClothes> createState() => _WashClothesState();
}

class _WashClothesState extends State<WashClothes> {
  final List<String> attachments = [];
  String? selectedSubActivity;
  String? selectedHandOff;
  final Map<String, TextEditingController> controllers = {
    'temperature': TextEditingController(),
    'notes': TextEditingController(),
    'handOffNotes': TextEditingController(),
  };

  @override
  void dispose() {
    controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Wash Clothes',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: ['Wash Only', 'Wash & Dry', 'Emergency Wash'],
                onChanged: (value) {
                  setState(() {
                    selectedSubActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Temperature',
                icon: Icons.thermostat,
                controller: controllers['temperature'],
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: controllers['notes'],
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: controllers['handOffNotes'],
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createSimpleHygieneHandleSave(
                  context,
                  'Wash Clothes',
                  widget.residentUUID,
                  selectedSubActivity,
                  controllers['temperature']?.text,
                  null,
                  attachments,
                  selectedHandOff,
                  controllers,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ChangeClothes extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const ChangeClothes({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<ChangeClothes> createState() => _ChangeClothesState();
}

class _ChangeClothesState extends State<ChangeClothes> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedSubActivity;
  String? selectedHandOff;
  final Map<String, TextEditingController> controllers = {
    'notes': TextEditingController(),
    'handOffNotes': TextEditingController(),
  };

  @override
  void dispose() {
    controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Change Clothes',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: ['Full Change', 'Partial Change', 'Emergency Change'],
                onChanged: (value) {
                  setState(() {
                    selectedSubActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: controllers['notes'],
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: controllers['handOffNotes'],
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createHygieneHandleSave(
                  context,
                  'Change Clothes',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedSubActivity,
                  null,
                  null,
                  attachments,
                  selectedHandOff,
                  controllers,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class NailCare extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const NailCare({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<NailCare> createState() => _NailCareState();
}

class _NailCareState extends State<NailCare> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedSubActivity;
  String? selectedHandOff;
  final Map<String, TextEditingController> controllers = {
    'notes': TextEditingController(),
    'handOffNotes': TextEditingController(),
  };

  @override
  void dispose() {
    controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Nail Care',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: ['Trim Nails', 'File Nails', 'Clean Nails'],
                onChanged: (value) {
                  setState(() {
                    selectedSubActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: controllers['notes'],
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: controllers['handOffNotes'],
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createHygieneHandleSave(
                  context,
                  'Nail Care',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedSubActivity,
                  null,
                  null,
                  attachments,
                  selectedHandOff,
                  controllers,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class OralHygiene extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const OralHygiene({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<OralHygiene> createState() => _OralHygieneState();
}

class _OralHygieneState extends State<OralHygiene> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedSubActivity;
  String? selectedHandOff;
  final Map<String, TextEditingController> controllers = {
    'notes': TextEditingController(),
    'handOffNotes': TextEditingController(),
  };

  @override
  void dispose() {
    controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Oral Hygiene',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: ['Brush Teeth', 'Denture Care', 'Mouth Wash'],
                onChanged: (value) {
                  setState(() {
                    selectedSubActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: controllers['notes'],
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: controllers['handOffNotes'],
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createHygieneHandleSave(
                  context,
                  'Oral Hygiene',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedSubActivity,
                  null,
                  null,
                  attachments,
                  selectedHandOff,
                  controllers,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Cream extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Cream({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Cream> createState() => _CreamState();
}

class _CreamState extends State<Cream> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedSubActivity;
  String? selectedHandOff;
  final Map<String, TextEditingController> controllers = {
    'notes': TextEditingController(),
    'handOffNotes': TextEditingController(),
  };

  @override
  void dispose() {
    controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Cream',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: ['Moisturizing', 'Medicated', 'Barrier Cream'],
                onChanged: (value) {
                  setState(() {
                    selectedSubActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: controllers['notes'],
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: controllers['handOffNotes'],
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createHygieneHandleSave(
                  context,
                  'Cream',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedSubActivity,
                  null,
                  null,
                  attachments,
                  selectedHandOff,
                  controllers,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class HairWash extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const HairWash({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<HairWash> createState() => _HairWashState();
}

class _HairWashState extends State<HairWash> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedSubActivity;
  String? selectedHandOff;
  final Map<String, TextEditingController> controllers = {
    'notes': TextEditingController(),
    'handOffNotes': TextEditingController(),
  };

  @override
  void dispose() {
    controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Hair Wash',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: ['Shampoo Only', 'Shampoo & Condition', 'Dry Shampoo'],
                onChanged: (value) {
                  setState(() {
                    selectedSubActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: controllers['notes'],
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: controllers['handOffNotes'],
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createHygieneHandleSave(
                  context,
                  'Hair Wash',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedSubActivity,
                  null,
                  null,
                  attachments,
                  selectedHandOff,
                  controllers,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class WashHands extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const WashHands({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<WashHands> createState() => _WashHandsState();
}

class _WashHandsState extends State<WashHands> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedSubActivity;
  String? selectedHandOff;
  final Map<String, TextEditingController> controllers = {
    'notes': TextEditingController(),
    'handOffNotes': TextEditingController(),
  };

  @override
  void dispose() {
    controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Wash Hands',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: ['Basic Wash', 'Antibacterial Wash', 'Deep Clean'],
                onChanged: (value) {
                  setState(() {
                    selectedSubActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: controllers['notes'],
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: controllers['handOffNotes'],
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createHygieneHandleSave(
                  context,
                  'Wash Hands',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedSubActivity,
                  null,
                  null,
                  attachments,
                  selectedHandOff,
                  controllers,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Bath extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Bath({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Bath> createState() => _BathState();
}

class _BathState extends State<Bath> {
  final List<String> attachments = [];
  String? selectedSubActivity;
  String? selectedHandOff;
  final Map<String, TextEditingController> controllers = {
    'temperature': TextEditingController(),
    'notes': TextEditingController(),
    'handOffNotes': TextEditingController(),
  };

  @override
  void dispose() {
    controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Bath',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: ['Full Bath', 'Partial Bath', 'Assisted Bath'],
                onChanged: (value) {
                  setState(() {
                    selectedSubActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Temperature',
                icon: Icons.thermostat,
                controller: controllers['temperature'],
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: controllers['notes'],
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: controllers['handOffNotes'],
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createSimpleHygieneHandleSave(
                  context,
                  'Bath',
                  widget.residentUUID,
                  selectedSubActivity,
                  controllers['temperature']?.text,
                  null,
                  attachments,
                  selectedHandOff,
                  controllers,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Shower extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Shower({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Shower> createState() => _ShowerState();
}

class _ShowerState extends State<Shower> {
  final List<String> attachments = [];
  String? selectedSubActivity;
  String? selectedHandOff;
  final Map<String, TextEditingController> controllers = {
    'temperature': TextEditingController(),
    'notes': TextEditingController(),
    'handOffNotes': TextEditingController(),
  };

  @override
  void dispose() {
    controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Shower',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: ['Full Shower', 'Quick Shower', 'Assisted Shower'],
                onChanged: (value) {
                  setState(() {
                    selectedSubActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Temperature',
                icon: Icons.thermostat,
                controller: controllers['temperature'],
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: controllers['notes'],
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: controllers['handOffNotes'],
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createSimpleHygieneHandleSave(
                  context,
                  'Shower',
                  widget.residentUUID,
                  selectedSubActivity,
                  controllers['temperature']?.text,
                  null,
                  attachments,
                  selectedHandOff,
                  controllers,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Continue with remaining widgets following the same pattern...
// For brevity, I'll show one more complex widget with ResponseType

class CheckEye extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CheckEye({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CheckEye> createState() => _CheckEyeState();
}

class _CheckEyeState extends State<CheckEye> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;
  final Map<String, TextEditingController> controllers = {
    'notes': TextEditingController(),
    'handOffNotes': TextEditingController(),
  };

  @override
  void dispose() {
    controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Check Eye',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: controllers['notes'],
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: controllers['handOffNotes'],
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createHygieneHandleSave(
                  context,
                  'Check Eye',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  null,
                  null,
                  selectedResponse,
                  attachments,
                  selectedHandOff,
                  controllers,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Complete implementation of remaining hygiene widgets

class Shave extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Shave({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Shave> createState() => _ShaveState();
}

class _ShaveState extends State<Shave> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedSubActivity;
  String? selectedHandOff;
  final Map<String, TextEditingController> controllers = {
    'notes': TextEditingController(),
    'handOffNotes': TextEditingController(),
  };

  @override
  void dispose() {
    controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Shave',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: ['Electric Shave', 'Wet Shave', 'Beard Trim'],
                onChanged: (value) {
                  setState(() {
                    selectedSubActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: controllers['notes'],
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: controllers['handOffNotes'],
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createHygieneHandleSave(
                  context,
                  'Shave',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedSubActivity,
                  null,
                  null,
                  attachments,
                  selectedHandOff,
                  controllers,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class MakeUp extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const MakeUp({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<MakeUp> createState() => _MakeUpState();
}

class _MakeUpState extends State<MakeUp> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedSubActivity;
  String? selectedHandOff;
  final Map<String, TextEditingController> controllers = {
    'notes': TextEditingController(),
    'handOffNotes': TextEditingController(),
  };

  @override
  void dispose() {
    controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Make Up',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: ['Full Make Up', 'Light Make Up', 'Special Occasion'],
                onChanged: (value) {
                  setState(() {
                    selectedSubActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: controllers['notes'],
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: controllers['handOffNotes'],
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createHygieneHandleSave(
                  context,
                  'Make Up',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedSubActivity,
                  null,
                  null,
                  attachments,
                  selectedHandOff,
                  controllers,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Cleaning extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Cleaning({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Cleaning> createState() => _CleaningState();
}

class _CleaningState extends State<Cleaning> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedSubActivity;
  String? selectedHandOff;
  final Map<String, TextEditingController> controllers = {
    'notes': TextEditingController(),
    'handOffNotes': TextEditingController(),
  };

  @override
  void dispose() {
    controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Cleaning',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: ['Room Clean', 'Deep Clean', 'Sanitization'],
                onChanged: (value) {
                  setState(() {
                    selectedSubActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: controllers['notes'],
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: controllers['handOffNotes'],
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createHygieneHandleSave(
                  context,
                  'Cleaning',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedSubActivity,
                  null,
                  null,
                  attachments,
                  selectedHandOff,
                  controllers,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Footwear extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Footwear({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Footwear> createState() => _FootwearState();
}

class _FootwearState extends State<Footwear> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedSubActivity;
  String? selectedHandOff;
  final Map<String, TextEditingController> controllers = {
    'notes': TextEditingController(),
    'handOffNotes': TextEditingController(),
  };

  @override
  void dispose() {
    controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Footwear',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: ['Put On Shoes', 'Remove Shoes', 'Foot Care'],
                onChanged: (value) {
                  setState(() {
                    selectedSubActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: controllers['notes'],
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: controllers['handOffNotes'],
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createHygieneHandleSave(
                  context,
                  'Footwear',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedSubActivity,
                  null,
                  null,
                  attachments,
                  selectedHandOff,
                  controllers,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CatheterCare extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CatheterCare({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CatheterCare> createState() => _CatheterCareState();
}

class _CatheterCareState extends State<CatheterCare> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedSubActivity;
  String? selectedHandOff;
  final Map<String, TextEditingController> controllers = {
    'notes': TextEditingController(),
    'handOffNotes': TextEditingController(),
  };

  @override
  void dispose() {
    controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Catheter Care',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: ['Catheter Cleaning', 'Bag Change', 'Position Check'],
                onChanged: (value) {
                  setState(() {
                    selectedSubActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: controllers['notes'],
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: controllers['handOffNotes'],
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createHygieneHandleSave(
                  context,
                  'Catheter Care',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedSubActivity,
                  null,
                  null,
                  attachments,
                  selectedHandOff,
                  controllers,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CheckEars extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CheckEars({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CheckEars> createState() => _CheckEarsState();
}

class _CheckEarsState extends State<CheckEars> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;
  final Map<String, TextEditingController> controllers = {
    'notes': TextEditingController(),
    'handOffNotes': TextEditingController(),
  };

  @override
  void dispose() {
    controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Check Ears',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: controllers['notes'],
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: controllers['handOffNotes'],
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createHygieneHandleSave(
                  context,
                  'Check Ears',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  null,
                  null,
                  selectedResponse,
                  attachments,
                  selectedHandOff,
                  controllers,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class MenstrualCycle extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const MenstrualCycle({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<MenstrualCycle> createState() => _MenstrualCycleState();
}

class _MenstrualCycleState extends State<MenstrualCycle> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedSubActivity;
  String? selectedHandOff;
  final Map<String, TextEditingController> controllers = {
    'notes': TextEditingController(),
    'handOffNotes': TextEditingController(),
  };

  @override
  void dispose() {
    controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Menstrual Cycle',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: ['Pad Change', 'Hygiene Support', 'Comfort Care'],
                onChanged: (value) {
                  setState(() {
                    selectedSubActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: controllers['notes'],
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: controllers['handOffNotes'],
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createHygieneHandleSave(
                  context,
                  'Menstrual Cycle',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedSubActivity,
                  null,
                  null,
                  attachments,
                  selectedHandOff,
                  controllers,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CheckHearingAid extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CheckHearingAid({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CheckHearingAid> createState() => _CheckHearingAidState();
}

class _CheckHearingAidState extends State<CheckHearingAid> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;
  final Map<String, TextEditingController> controllers = {
    'notes': TextEditingController(),
    'handOffNotes': TextEditingController(),
  };

  @override
  void dispose() {
    controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Check Hearing Aid',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: controllers['notes'],
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: controllers['handOffNotes'],
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createHygieneHandleSave(
                  context,
                  'Check Hearing Aid',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  null,
                  null,
                  selectedResponse,
                  attachments,
                  selectedHandOff,
                  controllers,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CheckGlasses extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CheckGlasses({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CheckGlasses> createState() => _CheckGlassesState();
}

class _CheckGlassesState extends State<CheckGlasses> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;
  final Map<String, TextEditingController> controllers = {
    'notes': TextEditingController(),
    'handOffNotes': TextEditingController(),
  };

  @override
  void dispose() {
    controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Check Glasses',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: controllers['notes'],
              ),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Hand Off Notes',
                icon: Icons.notes,
                controller: controllers['handOffNotes'],
              ),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createHygieneHandleSave(
                  context,
                  'Check Glasses',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  null,
                  null,
                  selectedResponse,
                  attachments,
                  selectedHandOff,
                  controllers,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

