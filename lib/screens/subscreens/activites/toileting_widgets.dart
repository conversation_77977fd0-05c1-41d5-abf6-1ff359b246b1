import 'package:carerez/components/careActivitesToiletDrop.dart';
import 'package:flutter/material.dart';
import '../../../components/careActivitesDropDown.dart';
import '../../../components/careActivitesHeader.dart';
import '../../../components/careActivitesSubmit.dart';
import '../../../components/careActivitesText.dart';
import '../../../components/timePicker.dart';
import '../../../models/activity.dart';
import '../../../models/staff.dart';

// Handler function for toileting/personal care activities
void createToiletingHandleSave(
    BuildContext context,
    String activityName,
    String residentId,
    int? selectedHour,
    int? selectedMinute,
    String? selectedSubActivity,
    TextEditingController notesController,
    Function(ResidentCareActivity) onSave,
    ) {
  final activity = ResidentCareActivity(
    residentId: residentId, // Pass from parent or get from context
    activityName: activityName,
    mood: 'neutral', // Category for toileting activities
    attachments: [], // No attachments for toileting activities
    subActivityA: 'Duration/Time',
    subActivityAValue: selectedHour != null && selectedMinute != null
        ? '${selectedHour}:${selectedMinute.toString().padLeft(2, '0')}'
        : '',
    subActivityB: 'Type/Severity',
    subActivityBValue: selectedSubActivity ?? 'Not specified',
    temperature: '', // Not used for toileting activities
    notes: notesController.text,
    handOffStaffId: '',
  );
  onSave(activity);
  Navigator.of(context).pop();
}

// Widgets with proper onSave implementation

class WetClothes extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const WetClothes({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<WetClothes> createState() => _WetClothesState();
}

class _WetClothesState extends State<WetClothes> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Wet Clothes',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: ['Light', 'Heavy', 'Extra Heavy'],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createToiletingHandleSave(
                  context,
                  'Wet Clothes',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class SoiledClothes extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const SoiledClothes({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<SoiledClothes> createState() => _SoiledClothesState();
}

class _SoiledClothesState extends State<SoiledClothes> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Soiled Clothes',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: ['Light', 'Heavy', 'Extra Heavy'],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createToiletingHandleSave(
                  context,
                  'Soiled Clothes',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ToiletHelp extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const ToiletHelp({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<ToiletHelp> createState() => _ToiletHelpState();
}

class _ToiletHelpState extends State<ToiletHelp> {
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Toilet Help',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelectorToilet(
                label: 'Sub Activity',
                options: [
                  'Urinate',
                  'Bowels Opened',
                  'Catheter Empty',
                  'Catheter Change',
                  'Staff Support',
                  'Declined'
                ],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createToiletingHandleSave(
                  context,
                  'Toilet Help',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class BedPan extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const BedPan({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<BedPan> createState() => _BedPanState();
}

class _BedPanState extends State<BedPan> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Bed Pan',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelectorToilet(
                label: 'Sub Activity',
                options: [
                  'Urinate',
                  'Bowels Opened',
                  'Catheter Empty',
                  'Catheter Change',
                  'Staff Support',
                  'Declined'
                ],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createToiletingHandleSave(
                  context,
                  'Bed Pan',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class UrineBottle extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const UrineBottle({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<UrineBottle> createState() => _UrineBottleState();
}

class _UrineBottleState extends State<UrineBottle> {
  final List<String> attachments = [];
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Urine Bottle',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelectorToilet(
                label: 'Sub Activity',
                options: ['Staff Supported', 'Declined'],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createToiletingHandleSave(
                  context,
                  'Urine Bottle',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Commode extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Commode({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Commode> createState() => _CommodeState();
}

class _CommodeState extends State<Commode> {
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Commode',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelectorToilet(
                label: 'Sub Activity',
                options: [
                  'Urinate',
                  'Bowels Opened',
                  'Catheter Empty',
                  'Catheter Change',
                  'Staff Support',
                  'Declined'
                ],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createToiletingHandleSave(
                  context,
                  'Commode',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class PadCheck extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const PadCheck({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<PadCheck> createState() => _PadCheckState();
}

class _PadCheckState extends State<PadCheck> {
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Pad Check',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelectorToilet(
                label: 'Sub Activity',
                options: ['Bowels Opened', 'Urinate', 'Checked', 'Declined'],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createToiletingHandleSave(
                  context,
                  'Pad Check',
                  widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}