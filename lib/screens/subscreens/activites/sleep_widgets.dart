import 'package:flutter/material.dart';

import '../../../components/careActivitesDropDown.dart';
import '../../../components/careActivitesHeader.dart';
import '../../../components/careActivitesSubmit.dart';
import '../../../components/careActivitesText.dart';
import '../../../components/timePicker.dart';
import '../../../models/activity.dart';
import '../../../models/staff.dart';


// Handler function for personal care activities
void createPersonalCareHandleSave(
    BuildContext context,
    String activityName,
    String residentId,
    int? selectedHour,
    int? selectedMinute,
    String? selectedSubActivity,
    TextEditingController notesController,
    Function(ResidentCareActivity) onSave,
    ) {
  final activity = ResidentCareActivity(
    residentId: residentId, // Pass from parent or get from context
    activityName: activityName,
    mood: 'neutral', // Category for personal care activities
    attachments: [], // No attachments for personal care activities
    subActivityA: 'Duration/Time',
    subActivityAValue: selectedHour != null && selectedMinute != null
        ? '${selectedHour}:${selectedMinute.toString().padLeft(2, '0')}'
        : '',
    subActivityB: 'Activity Status',
    subActivityBValue: selectedSubActivity ?? 'Not specified',
    temperature: '', // Not used for personal care activities
    notes: notesController.text,
    handOffStaffId: '',
  );
  onSave(activity);
  Navigator.of(context).pop();
}

// Widgets with proper onSave implementation

class PadChange extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const PadChange({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<PadChange> createState() => _PadChangeState();
}

class _PadChangeState extends State<PadChange> {
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Pad Change',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: [
                  'Urinate',
                  'Bowels Opened',
                  'Checked',
                  'Declined',
                ],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createPersonalCareHandleSave(
                  context,
                  'Pad Change',
                 widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CheckSleep extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const CheckSleep({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<CheckSleep> createState() => _CheckSleepState();
}

class _CheckSleepState extends State<CheckSleep> {
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  final TextEditingController notesController = TextEditingController();

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Check Sleep',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Sub Activity',
                options: [
                  'Sleeping',
                  'Awake',
                  'Disturbed',
                  'Wandering',
                ],
                onChanged: (value) {
                  setState(() {
                    selectedResponse = value;
                  });
                },
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(
                label: 'Notes',
                icon: Icons.notes,
                controller: notesController,
              ),
              SizedBox(height: 20),
              SubmitButton(
                onPressed: () => createPersonalCareHandleSave(
                  context,
                  'Check Sleep',
                 widget.residentUUID,selectedHour,
                selectedMinute,
                  selectedResponse,
                  notesController,
                  widget.onSave,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
