import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../components/attachmentButton.dart';
import '../../../components/careActivitesDropDown.dart';
import '../../../components/careActivitesHeader.dart';
import '../../../components/careActivitesSubmit.dart';
import '../../../components/careActivitesText.dart';
import '../../../components/careActivitesThumbs.dart';
import '../../../components/timePicker.dart';
import '../../../models/activity.dart';
import '../../../models/staff.dart';

void createStandardHandleSave(
    BuildContext context,
    String activityName,
    String residentId,
    String? selectedResponse,
    int? selectedHour,
    int? selectedMinute,
    String? selectedHandOff,
    List<String> attachments,
    TextEditingController notesController,
    TextEditingController handOffNotesController,
    Function(ResidentCareActivity) onSave,
    {String? subActivityBValue}
    ) {
  final activity = ResidentCareActivity(
    residentId: residentId, // Pass from parent or get from context
    activityName: activityName,
    mood: selectedResponse ?? 'neutral',
    attachments: attachments.map((e) => ActivityAttachment(
        fileName: e.split('/').last,
        filePath:e
    )).toList(),
    subActivityA: 'Duration',
    subActivityAValue: selectedHour != null && selectedMinute != null
        ? '${selectedHour}:${selectedMinute.toString().padLeft(2, '0')}'
        : '',
    subActivityB: 'Hand Off',
    subActivityBValue: subActivityBValue ?? selectedHandOff ?? '',
    temperature: '',
    notes: notesController.text,
    handOffStaffId: handOffNotesController.text,
  );
  onSave(activity);
  GoRouter.of(context).pop();
}

class ArmChairExersice extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  ArmChairExersice({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<ArmChairExersice> createState() => _ArmChairExersiceState();
}

class _ArmChairExersiceState extends State<ArmChairExersice> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Arm Chair Exersice',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
              HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Arm Chair Exercise',
                widget.residentUUID,
                selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class ArtandCraft extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const ArtandCraft({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<ArtandCraft> createState() => _ArtandCraftState();
}

class _ArtandCraftState extends State<ArtandCraft> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Art and Craft',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Art and Craft',
                widget.residentUUID,
                selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class BallGames extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const BallGames({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<BallGames> createState() => _BallGamesState();
}

class _BallGamesState extends State<BallGames> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Ball Games',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Ball Games',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class BBQ extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const BBQ({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<BBQ> createState() => _BBQState();
}

class _BBQState extends State<BBQ> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'BBQ',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'BBQ',
                widget.residentUUID,
                selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Birthday extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Birthday({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Birthday> createState() => _BirthdayState();
}

class _BirthdayState extends State<Birthday> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Birthday',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Birthday',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Bowling extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Bowling({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Bowling> createState() => _BowlingState();
}

class _BowlingState extends State<Bowling> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Bowling',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Bowling',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Cooking extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Cooking({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Cooking> createState() => _CookingState();
}

class _CookingState extends State<Cooking> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Cooking',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Cooking',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}


//done

class Crossword extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Crossword({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Crossword> createState() => _CrosswordState();
}

class _CrosswordState extends State<Crossword> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Crossword',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Crossword',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Driving extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Driving({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Driving> createState() => _DrivingState();
}

class _DrivingState extends State<Driving> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Driving',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Driving',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Entertainment extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Entertainment({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Entertainment> createState() => _EntertainmentState();
}

class _EntertainmentState extends State<Entertainment> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Entertainment',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Entertainment',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Exercise extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Exercise({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Exercise> createState() => _ExerciseState();
}

class _ExerciseState extends State<Exercise> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Exercise',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Exercise',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Swimming extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Swimming({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Swimming> createState() => _SwimmingState();
}

class _SwimmingState extends State<Swimming> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Swimming',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Swimming',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class BoardGames extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const BoardGames({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<BoardGames> createState() => _BoardGamesState();
}

class _BoardGamesState extends State<BoardGames> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Board Games',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Board Games',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Film extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Film({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Film> createState() => _FilmState();
}

class _FilmState extends State<Film> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Film',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Film',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}


//done


class Church extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Church({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Church> createState() => _ChurchState();
}

class _ChurchState extends State<Church> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Church',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Church',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class DayCentre extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const DayCentre({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<DayCentre> createState() => _DayCentreState();
}

class _DayCentreState extends State<DayCentre> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Day Centre',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Day Centre',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Dominoes extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Dominoes({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Dominoes> createState() => _DominoesState();
}

class _DominoesState extends State<Dominoes> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Dominoes',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Dominoes',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Fete extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Fete({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Fete> createState() => _FeteState();
}

class _FeteState extends State<Fete> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Fete',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Fete',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class GameConsole extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const GameConsole({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<GameConsole> createState() => _GameConsoleState();
}

class _GameConsoleState extends State<GameConsole> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Game Console',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Game Console',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Drama extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Drama({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Drama> createState() => _DramaState();
}

class _DramaState extends State<Drama> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Drama',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Drama',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class PlayedGames extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const PlayedGames({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<PlayedGames> createState() => _PlayedGamesState();
}

class _PlayedGamesState extends State<PlayedGames> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Played Games',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Played Games',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Knitting extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Knitting({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Knitting> createState() => _KnittingState();
}

class _KnittingState extends State<Knitting> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Knitting',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Knitting',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Massage extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Massage({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Massage> createState() => _MassageState();
}

class _MassageState extends State<Massage> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Massage',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Massage',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}


//done

class Music extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Music({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Music> createState() => _MusicState();
}

class _MusicState extends State<Music> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Music',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Music',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class HouseWork extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const HouseWork({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<HouseWork> createState() => _HouseWorkState();
}

class _HouseWorkState extends State<HouseWork> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'House Work',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'House Work',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Gardening extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Gardening({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Gardening> createState() => _GardeningState();
}

class _GardeningState extends State<Gardening> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Gardening',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Gardening',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Partying extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Partying({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Partying> createState() => _PartyingState();
}

class _PartyingState extends State<Partying> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Partying',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Partying',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class HorseRiding extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const HorseRiding({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<HorseRiding> createState() => _HorseRidingState();
}

class _HorseRidingState extends State<HorseRiding> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Horse Riding',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Horse Riding',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Outing extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Outing({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Outing> createState() => _OutingState();
}

class _OutingState extends State<Outing> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Outing',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Outing',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Pets extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Pets({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Pets> createState() => _PetsState();
}

class _PetsState extends State<Pets> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedPet;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Pets',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Pet',
                options: ['Dog', 'Cat', 'Fish', 'Bird'],
                onChanged: (value) {
                  setState(() {
                    selectedPet = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Pets',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: selectedPet,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Quiz extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Quiz({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Quiz> createState() => _QuizState();
}

class _QuizState extends State<Quiz> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedQuiz;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Quiz',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Quiz',
                options: ['General Knowledge', 'Music', 'Film', 'Sport'],
                onChanged: (value) {
                  setState(() {
                    selectedQuiz = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Quiz',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: selectedQuiz,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Pub extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Pub({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Pub> createState() => _PubState();
}

class _PubState extends State<Pub> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedDrink;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Pub',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Drink',
                options: ['Beer', 'Wine', 'Cocktail', 'Soft Drink'],
                onChanged: (value) {
                  setState(() {
                    selectedDrink = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Pub',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: selectedDrink,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Relaxation extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Relaxation({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Relaxation> createState() => _RelaxationState();
}

class _RelaxationState extends State<Relaxation> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedActivity;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Relaxation',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Activity',
                options: ['Reading', 'Meditation', 'Yoga', 'Sleep'],
                onChanged: (value) {
                  setState(() {
                    selectedActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Relaxation',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: selectedActivity,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Reading extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Reading({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Reading> createState() => _ReadingState();
}

class _ReadingState extends State<Reading> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedBook;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Reading',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Book',
                options: ['Fiction', 'Non-Fiction', 'Biography', 'Self-Help'],
                onChanged: (value) {
                  setState(() {
                    selectedBook = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Reading',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: selectedBook,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Reminiscence extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Reminiscence({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Reminiscence> createState() => _ReminiscenceState();
}

class _ReminiscenceState extends State<Reminiscence> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedMemory;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Reminiscence',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Memory',
                options: [
                  'Childhood',
                  'Teenage Years',
                  'Adult Life',
                  'Old Age'
                ],
                onChanged: (value) {
                  setState(() {
                    selectedMemory = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Reminiscence',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: selectedMemory,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Community extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Community({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Community> createState() => _CommunityState();
}

class _CommunityState extends State<Community> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedActivity;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Community',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Activity',
                options: ['Volunteering', 'Fundraising', 'Community Event'],
                onChanged: (value) {
                  setState(() {
                    selectedActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Community',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: selectedActivity,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Shopping extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Shopping({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Shopping> createState() => _ShoppingState();
}

class _ShoppingState extends State<Shopping> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedActivity;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Shopping',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Activity',
                options: ['Volunteering', 'Fundraising', 'Community Event'],
                onChanged: (value) {
                  setState(() {
                    selectedActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Shopping',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: selectedActivity,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

// done

class Therapeutic extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Therapeutic({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Therapeutic> createState() => _TherapeuticState();
}

class _TherapeuticState extends State<Therapeutic> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedActivity;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Therapeutic',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Activity',
                options: ['Massage', 'Music', 'Art', 'Aromatherapy'],
                onChanged: (value) {
                  setState(() {
                    selectedActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Therapeutic',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: selectedActivity,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Sensory extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Sensory({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Sensory> createState() => _SensoryState();
}

class _SensoryState extends State<Sensory> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedActivity;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Sensory',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Activity',
                options: ['Sight', 'Sound', 'Touch', 'Smell'],
                onChanged: (value) {
                  setState(() {
                    selectedActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Sensory',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: selectedActivity,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Singing extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Singing({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Singing> createState() => _SingingState();
}

class _SingingState extends State<Singing> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedActivity;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Singing',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Activity',
                options: ['Solo Singing', 'Group Singing', 'Karaoke', 'Choir'],
                onChanged: (value) {
                  setState(() {
                    selectedActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Singing',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: selectedActivity,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Theatre extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Theatre({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Theatre> createState() => _TheatreState();
}

class _TheatreState extends State<Theatre> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedActivity;
  String? selectedPlay;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Theatre',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Activity',
                options: ['Watching Performance', 'Participating', 'Rehearsal'],
                onChanged: (value) {
                  setState(() {
                    selectedActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Play',
                options: ['Comedy', 'Drama', 'Musical', 'Pantomime'],
                onChanged: (value) {
                  setState(() {
                    selectedPlay = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Theatre',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: '$selectedActivity - $selectedPlay',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class VisitRelatives extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const VisitRelatives({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<VisitRelatives> createState() => _VisitRelativesState();
}

class _VisitRelativesState extends State<VisitRelatives> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedActivity;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Visit Relatives',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Activity',
                options: ['Family Visit', 'Phone Call', 'Video Call', 'Day Out'],
                onChanged: (value) {
                  setState(() {
                    selectedActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Visit Relatives',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: selectedActivity,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Visitor extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Visitor({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Visitor> createState() => _VisitorState();
}

class _VisitorState extends State<Visitor> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedActivity;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Visitor',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Activity',
                options: ['Family Visit', 'Friend Visit', 'Professional Visit'],
                onChanged: (value) {
                  setState(() {
                    selectedActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Visitor',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: selectedActivity,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class WalkedOutside extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const WalkedOutside({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<WalkedOutside> createState() => _WalkedOutsideState();
}

class _WalkedOutsideState extends State<WalkedOutside> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedActivity;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Walked Outside',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Activity',
                options: ['Garden Walk', 'Park Visit', 'Neighborhood Walk', 'Exercise Walk'],
                onChanged: (value) {
                  setState(() {
                    selectedActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Walked Outside',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: selectedActivity,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class StayedInRoom extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const StayedInRoom({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<StayedInRoom> createState() => _StayedInRoomState();
}

class _StayedInRoomState extends State<StayedInRoom> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedActivity;
  String? selectedReason;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Stayed In Room',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Activity',
                options: ['Resting', 'Personal Time', 'Room Activities', 'Quiet Time'],
                onChanged: (value) {
                  setState(() {
                    selectedActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Reason',
                options: ['Health', 'Choice', 'Weather', 'Mood'],
                onChanged: (value) {
                  setState(() {
                    selectedReason = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Stayed In Room',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: '$selectedActivity - $selectedReason',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class EatOutside extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const EatOutside({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<EatOutside> createState() => _EatOutsideState();
}

class _EatOutsideState extends State<EatOutside> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedActivity;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Eat Outside',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Activity',
                options: ['Restaurant Visit', 'Picnic', 'Cafe Visit', 'Food Event'],
                onChanged: (value) {
                  setState(() {
                    selectedActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Eat Outside',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: selectedActivity,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class HairDresser extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const HairDresser({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<HairDresser> createState() => _HairDresserState();
}

class _HairDresserState extends State<HairDresser> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedActivity;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Hair Dresser',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Activity',
                options: ['Hair Cut', 'Hair Wash', 'Styling', 'Treatment'],
                onChanged: (value) {
                  setState(() {
                    selectedActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Hair Dresser',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: selectedActivity,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class SittingRoom extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const SittingRoom({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<SittingRoom> createState() => _SittingRoomState();
}

class _SittingRoomState extends State<SittingRoom> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedActivity;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Sitting Room',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Activity',
                options: ['Socializing', 'Watching TV', 'Relaxing', 'Group Activity'],
                onChanged: (value) {
                  setState(() {
                    selectedActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Sitting Room',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: selectedActivity,
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Cafe extends StatefulWidget {
  final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Cafe({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Cafe> createState() => _CafeState();
}

class _CafeState extends State<Cafe> {
  final List<String> attachments = [];
  final TextEditingController notesController = TextEditingController();
  final TextEditingController handOffNotesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;
  String? selectedActivity;
  String? selectedHandOff;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Cafe',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              DropdownSelector(
                label: 'Activity',
                options: ['Coffee & Chat', 'Tea Time', 'Snacks', 'Social Gathering'],
                onChanged: (value) {
                  setState(() {
                    selectedActivity = value;
                  });
                },
              ),
              SizedBox(height: 10),
              ResponseType(onResponseSelected: (response) {
                setState(() {
                  selectedResponse = response;
                });
              }),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 10),
               HandOffDropDown(
                label: 'Hand Off',
                options: widget.staffList,
                onChanged: (value) {
                  setState(() {
                    selectedHandOff = value;
                  });
                },
              ),
              SizedBox(height: 10),
              CustomTextField(label: 'Hand Off Notes', icon: Icons.notes, controller: handOffNotesController),
              SizedBox(height: 20),
              AttachmentButton(
                attachments: attachments,
                onAttachmentsSelected: (selectedAttachments) {
                  setState(() {
                    attachments.addAll(selectedAttachments);
                  });
                },
              ),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createStandardHandleSave(
                context,
                'Cafe',
                widget.residentUUID,selectedResponse,
                selectedHour,
                selectedMinute,
                selectedHandOff,
                attachments,
                notesController,
                handOffNotesController,
                widget.onSave,
                subActivityBValue: selectedActivity,
              )),
            ],
          ),
        ),
      ),
    );
  }
}