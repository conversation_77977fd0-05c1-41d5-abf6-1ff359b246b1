import 'package:flutter/material.dart';
import '../../../components/careActivitesHeader.dart';
import '../../../components/careActivitesSubmit.dart';
import '../../../components/careActivitesText.dart';
import '../../../components/timePicker.dart';
import '../../../models/activity.dart';
import '../../../models/staff.dart';

// Common handler function for behavior/care observations
void createBehaviorHandleSave(
    BuildContext context,
    String activityName,
    String residentId,
    int? selectedHour,
    int? selectedMinute,
    TextEditingController notesController,
    Function(ResidentCareActivity) onSave,
    {String? behaviorType}
    ) {
  final activity = ResidentCareActivity(
    residentId: residentId, // Pass from parent or get from context
    activityName: activityName,
    mood: behaviorType ?? 'neutral', // Use behavior type as mood indicator
    attachments: [], // No attachments for behavior observations
    subActivityA: 'Duration',
    subActivityAValue: selectedHour != null && selectedMinute != null
        ? '${selectedHour}:${selectedMinute.toString().padLeft(2, '0')}'
        : '',
    subActivityB: 'Observation Type',
    subActivityBValue: behaviorType ?? 'Behavioral Observation',
    temperature: '',
    notes: notesController.text,
    handOffStaffId: '',
  );
  onSave(activity);
  Navigator.of(context).pop();
}

class Upset extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Upset({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Upset> createState() => _UpsetState();
}

class _UpsetState extends State<Upset> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Upset',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createBehaviorHandleSave(
                context,
                'Upset',
               widget.residentUUID,
                selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                behaviorType: 'Emotional Distress',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Agitated extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Agitated({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Agitated> createState() => _AgitatedState();
}

class _AgitatedState extends State<Agitated> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Agitated',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createBehaviorHandleSave(
                context,
                'Agitated',
               widget.residentUUID,
                selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                behaviorType: 'Behavioral Agitation',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Confused extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Confused({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Confused> createState() => _ConfusedState();
}

class _ConfusedState extends State<Confused> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Confused',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createBehaviorHandleSave(
                context,
                'Confused',
               widget.residentUUID,
                selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                behaviorType: 'Cognitive Confusion',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Pacing extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Pacing({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Pacing> createState() => _PacingState();
}

class _PacingState extends State<Pacing> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Pacing',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createBehaviorHandleSave(
                context,
                'Pacing',
               widget.residentUUID,
                selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                behaviorType: 'Restless Behavior',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Hallucination extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Hallucination({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Hallucination> createState() => _HallucinationState();
}

class _HallucinationState extends State<Hallucination> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Hallucination',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createBehaviorHandleSave(
                context,
                'Hallucination',
               widget.residentUUID,
                selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                behaviorType: 'Perceptual Disturbance',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Repetitive extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Repetitive({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Repetitive> createState() => _RepetitiveState();
}

class _RepetitiveState extends State<Repetitive> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Repetitive',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createBehaviorHandleSave(
                context,
                'Repetitive',
               widget.residentUUID,
                selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                behaviorType: 'Repetitive Behavior',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Wandering extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Wandering({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Wandering> createState() => _WanderingState();
}

class _WanderingState extends State<Wandering> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Wandering',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createBehaviorHandleSave(
                context,
                'Wandering',
               widget.residentUUID,
                selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                behaviorType: 'Movement Behavior',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class SocialWorker extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const SocialWorker({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<SocialWorker> createState() => _SocialWorkerState();
}

class _SocialWorkerState extends State<SocialWorker> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Social Worker',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createBehaviorHandleSave(
                context,
                'Social Worker',
               widget.residentUUID,
                selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                behaviorType: 'Professional Consultation',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class StaffSupported extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const StaffSupported({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<StaffSupported> createState() => _StaffSupportedState();
}

class _StaffSupportedState extends State<StaffSupported> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Staff Supported',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createBehaviorHandleSave(
                context,
                'Staff Supported',
               widget.residentUUID,
                selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                behaviorType: 'Staff Intervention',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Challenging extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Challenging({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Challenging> createState() => _ChallengingState();
}

class _ChallengingState extends State<Challenging> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Challenging',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createBehaviorHandleSave(
                context,
                'Challenging',
               widget.residentUUID,
                selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                behaviorType: 'Challenging Behavior',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Paranoid extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Paranoid({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Paranoid> createState() => _ParanoidState();
}

class _ParanoidState extends State<Paranoid> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Paranoid',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createBehaviorHandleSave(
                context,
                'Paranoid',
               widget.residentUUID,
                selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                behaviorType: 'Paranoid Thoughts',
              )),
            ],
          ),
        ),
      ),
    );
  }
}

class Disinhibited extends StatefulWidget {
   final void Function(ResidentCareActivity activity) onSave;
  final List<Staff> staffList;
  final String residentUUID;
  const Disinhibited({super.key, required this.onSave, required this.staffList, required this.residentUUID});

  @override
  State<Disinhibited> createState() => _DisinhibitedState();
}

class _DisinhibitedState extends State<Disinhibited> {
  final TextEditingController notesController = TextEditingController();
  int? selectedHour;
  int? selectedMinute;
  String? selectedResponse;

  @override
  Widget build(BuildContext context) {
    return Scaffold(resizeToAvoidBottomInset: false,
      backgroundColor: Colors.black.withOpacity(0.7),
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,height: MediaQuery.of(context).size.height * 0.9,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Header(
                title: 'Disinhibited',
                onBack: () => Navigator.of(context).pop(),
              ),
              SizedBox(height: 10),
              TimePicker(onTimeChanged: (int hour, int minute) {
                setState(() {
                  selectedHour = hour;
                  selectedMinute = minute;
                });
              }),
              SizedBox(height: 10),
              CustomTextField(label: 'Notes', icon: Icons.notes, controller: notesController),
              SizedBox(height: 20),
              SubmitButton(onPressed: () => createBehaviorHandleSave(
                context,
                'Disinhibited',
               widget.residentUUID,
                selectedHour,
                selectedMinute,
                notesController,
                widget.onSave,
                behaviorType: 'Disinhibited Behavior',
              )),
            ],
          ),
        ),
      ),
    );
  }
}