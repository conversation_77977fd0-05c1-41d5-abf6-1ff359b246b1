import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';
import '../../blocs/resident/resident_bloc.dart';
import '../../blocs/resident/resident_event.dart';
import '../../blocs/resident/resident_state.dart';
import '../../components/drawer.dart';
import '../../models/resident.dart';
import '../../routes/router_constants.dart';

class ResidentsScreen extends StatefulWidget {
  const ResidentsScreen({Key? key}) : super(key: key);

  @override
  State<ResidentsScreen> createState() => _ResidentsScreenState();
}

class _ResidentsScreenState extends State<ResidentsScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Fetch residents when the screen loads
    context.read<ResidentBloc>().add(FetchResidents());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      key: _scaffoldKey,
      drawer: buildDrawer(scaffoldKey: _scaffoldKey),
      appBar: AppBar(
        backgroundColor: const Color.fromRGBO(90, 38, 101, 0.9),
        leading: IconButton(
          onPressed: () {
            _scaffoldKey.currentState!.openDrawer();
          },
          icon: const Icon(HugeIcons.strokeRoundedMenu01, color: Colors.white),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.keyboard_backspace, color: Colors.white),
            onPressed: () {
              GoRouter.of(context).pushNamed(
                RouteConstants.homescreenRouteName
              );
            },
          ),
        ],
        title: Row(
          children: [
            //profile icon
            Icon(HugeIcons.strokeRoundedUserGroup02,
                color: Colors.white, size: 24),
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Residents',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                ),
                Text(
                  'Manage All Residents',
                  style: TextStyle(
                    color: Color(0xFF8E8E93),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search residents...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              onChanged: (value) {
                context.read<ResidentBloc>().add(SearchResidents(value));
              },
            ),
          ),
          Expanded(
            child: BlocBuilder<ResidentBloc, ResidentState>(
              builder: (context, state) {
                if (state is ResidentLoading) {
                  return const Center(child: CircularProgressIndicator());
                } else if (state is ResidentLoaded) {
                  return _buildResidentsList(state.filteredResidents);
                } else if (state is ResidentEmpty) {
                  return const Center(child: Text('No residents found'));
                } else if (state is ResidentError) {
                  return Center(child: Text('Error: ${state.message}'));
                } else {
                  return const Center(child: Text('Please load residents'));
                }
              },
            ),
          ),
        ],
      ),
      // floatingActionButton: FloatingActionButton(
      //   backgroundColor: const Color.fromRGBO(90, 38, 101, 0.9),
      //   onPressed: () {
      //     // Navigate to add resident screen
      //   },
      //   child: const Icon(Icons.add, color: Colors.white),
      // ),
    );
  }

  Widget _buildResidentsList(List<Resident> residents) {
    debugPrint('Residents: $residents');
    return ListView.builder(
      padding:  EdgeInsets.zero,
      itemCount: residents.length,
      itemBuilder: (context, index) {
        final resident = residents[index];
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  // Navigate to resident details
                  GoRouter.of(context).goNamed(
                    RouteConstants.residentPersonalRouteName,
                    pathParameters: {'residentId': resident.userCode,'residentUUID':resident.id},
                  );
                },
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Hero(
                        tag: 'resident-${resident.id}',
                        child: Container(
                          width: 65,
                          height: 65,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                spreadRadius: 1,
                                blurRadius: 3,
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(40),
                            child: Image.network(
                              resident.image.isNotEmpty
                                  ? resident.image
                                  : 'https://example.com/default_image.png',
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  color: Colors.grey[200],
                                  child: Icon(Icons.person, size: 40, color: Colors.grey[400]),
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              resident.name,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Code: ${resident.userCode}',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              'Phone: ${resident.phone}',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: resident.status == 'active'
                              ? Colors.green.withOpacity(0.1)
                              : Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(30),
                        ),
                        child: Text(
                          resident.status.toUpperCase(),
                          style: TextStyle(
                            color: resident.status == 'active'
                                ? Colors.green[700]
                                : Colors.red[700],
                            fontWeight: FontWeight.w600,
                            fontSize: 13,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
