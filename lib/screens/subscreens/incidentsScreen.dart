import 'dart:convert';
import 'dart:io';
import 'package:carerez/blocs/incident/incident_bloc.dart';
import 'package:carerez/blocs/incident/incident_event.dart';
import 'package:carerez/blocs/incident/incident_state.dart';
import 'package:carerez/blocs/resident/resident_bloc.dart';
import 'package:carerez/blocs/resident/resident_event.dart';
import 'package:carerez/blocs/resident/resident_state.dart';
import 'package:carerez/blocs/user/user_bloc.dart';
import 'package:carerez/blocs/user/user_state.dart';
import 'package:carerez/blocs/staff/staff_bloc.dart';
import 'package:carerez/blocs/staff/staff_event.dart';
import 'package:carerez/blocs/staff/staff_state.dart';
import 'package:carerez/components/drawer.dart';
import 'package:carerez/services/resident_service.dart';
import 'package:carerez/services/staff_service.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hugeicons/hugeicons.dart';
import '../../models/incident.dart';
import '../../models/staff.dart';
import 'package:intl/intl.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:carerez/services/incident_service.dart';

import '../../models/resident.dart';
import '../../routes/router_constants.dart';

class IncidentManagementScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => IncidentBloc(
            IncidentService(),
          )..add(FetchIncidents()),
        ),
        BlocProvider(
          create: (context) => ResidentBloc(
            ResidentService(),
          )..add(FetchResidents()),
        ),
        BlocProvider(
          create: (context) => StaffBloc(
            StaffService(),
          )..add(FetchStaff()),
        ),
      ],
      child: IncidentManagementScreenContent(),
    );
  }
}

class IncidentManagementScreenContent extends StatefulWidget {
  @override
  _IncidentManagementScreenState createState() =>
      _IncidentManagementScreenState();
}

class _IncidentManagementScreenState
    extends State<IncidentManagementScreenContent> {
  // Key for the scaffold
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // Form controllers and state
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _staffController = TextEditingController();
  List<File> _attachments = [];
  String? selectedResidentId; // Stores the selected resident ID
  String? selectedStaffId; // Stores the selected staff ID

  String _selectedLevel = 'Neutral';
  String _selectedStatus = 'New';

  @override
  void initState() {
    super.initState();
    // Fetch residents and staff when the screen loads
    context.read<ResidentBloc>().add(FetchResidents());
    context.read<StaffBloc>().add(FetchStaff());
  }

  // Method to pick attachments
  Future<void> _pickAttachments() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.any,
    );

    if (result != null) {
      setState(() {
        _attachments = result.paths.map((path) => File(path!)).toList();
      });
    }
  }

  // Method to create a new incident
  void _createIncident() {
    if (_formKey.currentState!.validate()) {
      final data = context.read<UserBloc>().state as UserAndTasksLoaded;
      final staffId = data.user.id;
      final homeId = data.user.homeId ?? '';
      final unitId = data.user.unitId ?? '';

      final newIncident = Incident(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text,
        description: _descriptionController.text,
        reportedBy: staffId,
        unitId: unitId,
        homeId: homeId,
        placeName: 'Home',
        level: _selectedLevel.toLowerCase(),
        status: _selectedStatus.toLowerCase(),
        dateTime: DateTime.now(),
        linkedResident: selectedResidentId ?? '',
        attachments: _attachments
            .map((file) => IncidentAttachment(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                  url: file.path,
                  name: file.path.split('/').last,
                ))
            .toList(),
      );

      context.read<IncidentBloc>().add(CreateIncident(newIncident));

      // Clear form
      _nameController.clear();
      _descriptionController.clear();
      setState(() {
        _attachments.clear();
        selectedResidentId = null;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Incident Created Successfully')),
      );
    }
  }

  // Helper method to get status color
  Color _getStatusColor(String status) {
    switch (status) {
      case 'Emergency':
        return Colors.red;
      case 'In Progress':
        return Colors.orange;
      case 'Resolved':
        return Colors.green;
      default:
        return Colors.blue;
    }
  }

  // Build the resident dropdown
  Widget _buildResidentDropdown() {
    return BlocBuilder<ResidentBloc, ResidentState>(
      builder: (context, state) {
        if (state is ResidentLoading) {
          return DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: 'Link Resident',
              prefixIcon: Icon(Icons.people_alt_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            hint: Text('Loading residents...'),
            items: [],
            onChanged: null,
          );
        } else if (state is ResidentLoaded) {
          return DropdownButtonFormField<String>(
            dropdownColor: Colors.white,
            decoration: InputDecoration(
              labelText: 'Link Resident',
              fillColor: Colors.white,
              labelStyle: GoogleFonts.roboto(
                color: Colors.black54,
              ),
              prefixIcon: Icon(Icons.people_alt_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            hint: Text('Select a resident'),
            value: selectedResidentId,
            items: state.residents.map((resident) {
              return DropdownMenuItem(
                value: resident.id,
                child: Text(
                  '${resident.name} (${resident.userCode})',
                  style: const TextStyle(color: Colors.black54),
                ),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                selectedResidentId = value;
              });
            },
          );
        } else if (state is ResidentEmpty) {
          return DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: 'Link Resident',
              prefixIcon: Icon(Icons.people_alt_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            hint: Text('No residents available'),
            items: [],
            onChanged: null,
          );
        } else {
          return DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: 'Link Resident',
              prefixIcon: Icon(Icons.people_alt_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            hint: Text('Failed to load residents'),
            items: [],
            onChanged: null,
          );
        }
      },
    );
  }

  // Build the staff field
  Widget _buildStaffField() {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, userState) {
        if (userState is UserLoaded) {
          // If user is loaded, use their information
          return TextFormField(
            enabled: false,
            initialValue: userState.user.tenantName,
            decoration: InputDecoration(
              labelText: 'Reported By',
              prefixIcon: Icon(Icons.person),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        } else {
          // If user is not loaded, show staff dropdown
          return BlocBuilder<StaffBloc, StaffState>(
            builder: (context, state) {
              if (state is StaffLoading) {
                return TextFormField(
                  enabled: false,
                  decoration: InputDecoration(
                    labelText: 'Loading staff...',
                    prefixIcon: Icon(Icons.person),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                );
              } else if (state is StaffLoaded) {
                return DropdownButtonFormField<String>(
                  dropdownColor: Colors.white,
                  decoration: InputDecoration(
                    labelText: 'Reported By',
                    fillColor: Colors.white,
                    labelStyle: GoogleFonts.roboto(
                      color: Colors.black54,
                    ),
                    prefixIcon: Icon(Icons.person),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  hint: Text('Select staff member'),
                  value: selectedStaffId,
                  items: state.staff.map((staff) {
                    return DropdownMenuItem(
                      value: staff.id,
                      child: Text(
                        staff.name,
                        style: const TextStyle(color: Colors.black54),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedStaffId = value;
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select a staff member';
                    }
                    return null;
                  },
                );
              } else {
                return TextFormField(
                  controller: _staffController,
                  decoration: InputDecoration(
                    labelText: 'Reported By',
                    prefixIcon: Icon(Icons.person),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter reporter name';
                    }
                    return null;
                  },
                );
              }
            },
          );
        }
      },
    );
  }

  // Incident Card Widget
  Widget _buildIncidentCard(Incident incident) {
    return GestureDetector(
      onTap: () {
        GoRouter.of(context)
            .goNamed(RouteConstants.incidentDetailsRouteName, extra: incident);
      },
      child: Card(
        color: Colors.white,
        elevation: 4,
        margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row with Status and Priority
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      incident.name,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getStatusColor(incident.level),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      incident.level.toUpperCase(),
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8),
              // Description
              Text(
                incident.description,
                style: TextStyle(color: Colors.grey[700]),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 8),
              // Location info
              Row(
                children: [
                  Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                  SizedBox(width: 4),
                  Text(
                    '${incident.getHomeName()} - ${incident.getUnitName()}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  ),
                ],
              ),
              SizedBox(height: 4),
              // Reporter info
              Row(
                children: [
                  Icon(Icons.person, size: 16, color: Colors.grey[600]),
                  SizedBox(width: 4),
                  Text(
                    'Reported by: ${incident.getReporterName()}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  ),
                ],
              ),
              SizedBox(height: 8),
              // Footer Row with Status and Date
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getStatusColor(incident.status),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      incident.status.toUpperCase(),
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Text(
                    DateFormat('MMM dd, yyyy HH:mm').format(incident.dateTime),
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              // Show attachments count if any
              if (incident.attachments.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Row(
                    children: [
                      Icon(Icons.attach_file,
                          size: 16, color: Colors.grey[600]),
                      SizedBox(width: 4),
                      Text(
                        '${incident.attachments.length} attachment(s)',
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        key: _scaffoldKey,
        drawer: buildDrawer(scaffoldKey: _scaffoldKey),
        backgroundColor: const Color(0xFFF5F5F7), // Light gray background
        appBar: AppBar(
          backgroundColor: const Color.fromRGBO(90, 38, 101, 1),
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.menu, color: Colors.white),
            onPressed: () => _scaffoldKey.currentState?.openDrawer(),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
              onPressed: () => GoRouter.of(context)
                  .goNamed(RouteConstants.homescreenRouteName),
            ),
          ],
          title: Row(
            children: [
              //profile icon
              Icon(HugeIcons.strokeRoundedTvIssue,
                  color: Colors.white, size: 24),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Incidents',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 20,
                    ),
                  ),
                  Text(
                    'Incident Management',
                    style: TextStyle(
                      color: Color(0xFF8E8E93),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
          bottom: TabBar(
            indicatorColor: Colors.white,
            indicatorWeight: 3,
            labelStyle: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
            tabs: const [
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.list_alt, color: Colors.white),
                    SizedBox(width: 8),
                    Text('Existing Incidents',
                        style: TextStyle(color: Colors.white)),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.add_circle_outline, color: Colors.white),
                    SizedBox(width: 8),
                    Text('Create New', style: TextStyle(color: Colors.white)),
                  ],
                ),
              ),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            // Existing Incidents Tab
            RefreshIndicator(
              onRefresh: () async {
                await Future.delayed(const Duration(seconds: 1));
                setState(() {});
              },
              child: _buildIncidentsList(),
            ),
            // Create New Incident Tab
            _buildCreateIncidentForm(),
          ],
        ),
      ),
    );
  }

  Widget _buildIncidentsList() {
    return BlocBuilder<IncidentBloc, IncidentState>(
      builder: (context, state) {
        if (state is IncidentInitial) {
          // Trigger fetch if not already done
          context.read<IncidentBloc>().add(FetchIncidents());
          return Center(child: CircularProgressIndicator());
        } else if (state is IncidentLoading) {
          return Center(child: CircularProgressIndicator());
        } else if (state is IncidentError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Error: ${state.message}'),
                ElevatedButton(
                  onPressed: () =>
                      context.read<IncidentBloc>().add(FetchIncidents()),
                  child: Text('Retry'),
                ),
              ],
            ),
          );
        } else if (state is IncidentEmpty) {
          return Center(
            child: Text('No incidents found. Create a new one!'),
          );
        } else if (state is IncidentLoaded) {
          return ListView.builder(
            itemCount: state.filteredIncidents.length,
            itemBuilder: (context, index) {
              return _buildIncidentCard(state.filteredIncidents[index]);
            },
          );
        } else {
          return Center(child: Text('Unknown state'));
        }
      },
    );
  }

  Widget _buildCreateIncidentForm() {
    return BlocBuilder<UserBloc, UserState>(builder: (context, state) {
      if (state is UserLoading) {
        return const Center(
          child: CircularProgressIndicator(
            strokeWidth: 3,
            color: Color(0xFF667eea),
          ),
        );
      } else if (state is UserLoaded || state is UserAndTasksLoaded) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Section
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF667eea).withOpacity(0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Icon(
                            Icons.report_problem_outlined,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                        const SizedBox(width: 16),
                        const Text(
                          'Create New Incident',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.w700,
                            color: Colors.white,
                            letterSpacing: 0.5,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Fill in the details below to report an incident',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withOpacity(0.8),
                        fontWeight: FontWeight.w400,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Form Section
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 20,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Incident Name
                        _buildSectionTitle('Basic Information'),
                        const SizedBox(height: 16),
                        _buildModernTextField(
                          controller: _nameController,
                          label: 'Incident Name',
                          icon: Icons.title_rounded,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter incident name';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 20),

                        // Staff Field
                        _buildStaffField(),

                        const SizedBox(height: 20),

                        // Description
                        _buildModernTextField(
                          controller: _descriptionController,
                          label: 'Description',
                          icon: Icons.description_outlined,
                          maxLines: 4,
                          hint: 'Provide detailed description of the incident...',
                        ),

                        const SizedBox(height: 32),

                        // Resident Selection
                        _buildSectionTitle('Resident Details'),
                        const SizedBox(height: 16),
                        _buildResidentDropdown(),

                        const SizedBox(height: 32),

                        // Incident Details
                        _buildSectionTitle('Incident Details'),
                        const SizedBox(height: 16),
                        _buildModernDropdown(
                          label: 'Incident Level',
                          icon: Icons.warning_amber_rounded,
                          value: _selectedLevel,
                          items: ['Neutral', 'Emergency', 'Observation'],
                          onChanged: (value) {
                            setState(() {
                              _selectedLevel = value!;
                            });
                          },
                        ),

                        const SizedBox(height: 20),

                        _buildModernDropdown(
                          label: 'Status',
                          icon: Icons.check_circle_outline,
                          value: _selectedStatus,
                          items: ['New', 'Resolved'],
                          onChanged: (value) {
                            setState(() {
                              _selectedStatus = value!;
                            });
                          },
                        ),

                        const SizedBox(height: 32),

                        // Attachments Section
                        _buildSectionTitle('Attachments'),
                        const SizedBox(height: 16),
                        _buildAttachmentSection(),

                        const SizedBox(height: 40),

                        // Submit Button
                        Container(
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: const Color(0xFF667eea).withOpacity(0.4),
                                blurRadius: 15,
                                offset: const Offset(0, 8),
                              ),
                            ],
                          ),
                          child: ElevatedButton(
                            onPressed: _createIncident,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.transparent,
                              shadowColor: Colors.transparent,
                              padding: const EdgeInsets.symmetric(vertical: 18),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16),
                              ),
                            ),
                            child: const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.add_circle_outline,
                                  color: Colors.white,
                                  size: 20,
                                ),
                                SizedBox(width: 8),
                                Text(
                                  'Create Incident',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Bottom spacing
              const SizedBox(height: 24),
            ],
          ),
        );
      } else if (state is UserError) {
        return _buildErrorState('Error loading user data');
      } else {
        return _buildErrorState('Unknown user state');
      }
    });
  }

  Widget _buildSectionTitle(String title) {
    return Row(
      children: [
        Container(
          width: 4,
          height: 20,
          decoration: BoxDecoration(
            color: const Color(0xFF667eea),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2D3748),
          ),
        ),
      ],
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    int maxLines = 1,
    String? hint,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF7FAFC),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFE2E8F0),
          width: 1,
        ),
      ),
      child: TextFormField(
        controller: controller,
        maxLines: maxLines,
        validator: validator,
        style: const TextStyle(
          fontSize: 16,
          color: Color(0xFF2D3748),
        ),
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          prefixIcon: Icon(
            icon,
            color: const Color(0xFF667eea),
            size: 20,
          ),
          labelStyle: const TextStyle(
            color: Color(0xFF718096),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          hintStyle: TextStyle(
            color: const Color(0xFF718096).withOpacity(0.7),
            fontSize: 14,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
          floatingLabelBehavior: FloatingLabelBehavior.auto,
        ),
      ),
    );
  }

  Widget _buildModernDropdown({
    required String label,
    required IconData icon,
    required String? value,
    required List<String> items,
    required Function(String?) onChanged,
  }) {
    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(
          icon,
          color: const Color(0xFF667eea),
          size: 20,
        ),
        labelStyle: const TextStyle(
          color: Color(0xFF718096),
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        filled: true,
        fillColor: const Color(0xFFF7FAFC),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFFE2E8F0),
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFFE2E8F0),
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFF667eea),
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
      dropdownColor: Colors.white,
      value: value,
      style: const TextStyle(
        fontSize: 16,
        color: Color(0xFF2D3748),
      ),
      items: items.map((item) => DropdownMenuItem(
        value: item,
        child: Text(item),
      )).toList(),
      onChanged: onChanged,
      isExpanded: true,
    );
  }

  Widget _buildAttachmentSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: const Color(0xFFF7FAFC),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFFE2E8F0),
              width: 1,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(12),
              onTap: _pickAttachments,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Icon(
                      Icons.attach_file_rounded,
                      color: const Color(0xFF667eea),
                      size: 32,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Add Attachments',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF2D3748),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Tap to select files',
                      style: TextStyle(
                        fontSize: 14,
                        color: const Color(0xFF718096).withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),

        // Attachment list
        if (_attachments.isNotEmpty) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFF0FFF4),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFFC6F6D5),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Color(0xFF48BB78),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${_attachments.length} file(s) selected',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF22543D),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                ...List.generate(
                  _attachments.length,
                      (index) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.insert_drive_file,
                          color: Color(0xFF68D391),
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _attachments[index].path.split('/').last,
                            style: const TextStyle(
                              fontSize: 14,
                              color: Color(0xFF22543D),
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(24),
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFFFED7D7),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: const Color(0xFFFEB2B2),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.error_outline,
              color: Color(0xFFE53E3E),
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(
                fontSize: 16,
                color: Color(0xFF742A2A),
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }


}
