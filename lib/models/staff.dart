class Staff {
  final String id;
  final String firstName;
  final String lastName;
  final String name;
  final bool active;
  final String location;
  final String role;
  final String imageUrl;
  final String email;
  final String phone;
  final String department;
  final String specialization;
  final String qualification;
  final String joinDate;
  final String shiftType;
  final String emergencyContact;
  final String userCode;
  final String status;
  final DateTime createdAt;

  Staff({
    required this.id,
    this.firstName = '',
    this.lastName = '',
    required this.name,
    required this.active,
    required this.location,
    required this.role,
    this.imageUrl = '',
    this.email = '',
    this.phone = '',
    this.department = '',
    this.specialization = '',
    this.qualification = '',
    this.joinDate = '',
    this.shiftType = '',
    this.emergencyContact = '',
    this.userCode = '',
    this.status = '',
    DateTime? createdAt,
  }) : this.createdAt = createdAt ?? DateTime.now();

  factory Staff.fromJson(Map<String, dynamic> json) {
    return Staff(
      id: json['id'] ?? json['staffId'] ?? json['userId'] ?? '',
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      name: json['name'] ??
          '${json['firstName'] ?? ''} ${json['lastName'] ?? ''}'.trim(),
      active: json['active'] ??
          json['isActive'] ??
          (json['status'] == 'active' ||
              json['status'] == 'new'), // fallback from 'status'
      location: json['location'] ?? json['workLocation'] ?? '',
      role: json['role'] ?? json['designation'] ?? json['jobTitle'] ?? '',
      imageUrl: json['profileUrl'] ?? json['profileImage'] ?? '',
      email: json['emailAddress'] ?? '',
      phone: json['phoneNumber'] ?? json['contactNumber'] ?? '',
      department: json['department'] ?? '',
      specialization: json['jobTitle'] ?? '',
      qualification: json['qualification'] ?? json['jobTitle'] ?? '',
      joinDate: json['joinDate'] ?? '',
      shiftType: json['shiftType'] ?? '',
      emergencyContact: json['emergencyContact'] ?? '',
      userCode: json['userCode'] ?? '',
      status: json['status'] ?? '',
      createdAt:
          json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': id,
      'firstName': firstName.isEmpty ? name.split(' ').first : firstName,
      'lastName': lastName.isEmpty
          ? (name.split(' ').length > 1 ? name.split(' ').last : '')
          : lastName,
      'name': name,
      'active': active,
      'location': location,
      'role': role,
      'profileUrl': imageUrl,
      'emailAddress': email,
      'phoneNumber': phone,
      'department': department,
      'jobTitle': specialization,
      'qualification': qualification,
      'joinDate': joinDate,
      'shiftType': shiftType,
      'emergencyContact': emergencyContact,
      'userCode': userCode,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // Create a copy of this Staff with the given fields replaced with new values
  Staff copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? name,
    bool? active,
    String? location,
    String? role,
    String? imageUrl,
    String? email,
    String? phone,
    String? department,
    String? specialization,
    String? qualification,
    String? joinDate,
    String? shiftType,
    String? emergencyContact,
    String? userCode,
    String? status,
    DateTime? createdAt,
  }) {
    return Staff(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      name: name ?? this.name,
      active: active ?? this.active,
      location: location ?? this.location,
      role: role ?? this.role,
      imageUrl: imageUrl ?? this.imageUrl,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      department: department ?? this.department,
      specialization: specialization ?? this.specialization,
      qualification: qualification ?? this.qualification,
      joinDate: joinDate ?? this.joinDate,
      shiftType: shiftType ?? this.shiftType,
      emergencyContact: emergencyContact ?? this.emergencyContact,
      userCode: userCode ?? this.userCode,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
