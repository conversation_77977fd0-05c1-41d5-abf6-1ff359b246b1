class AssignTask {
  final String assignTaskId;
  final String task;
  final DateTime date;
  final String taskColor;
  final bool isTenant;
  final Home home;
  final List<Staff> staff;
  final AssignedBy? assignedBy;

  AssignTask({
    required this.assignTaskId,
    required this.task,
    required this.date,
    required this.taskColor,
    required this.isTenant,
    required this.home,
    required this.staff,
    required this.assignedBy,
  });

  factory AssignTask.fromJson(Map<String, dynamic> json) {
    return AssignTask(
      assignTaskId: json['assignTaskId'] as String,
      task: json['task'] as String,
      date: DateTime.parse(json['date'] as String),
      taskColor: json['taskColor'] as String,
      isTenant: json['isTenant'] as bool,
      home: Home.fromJson(json['home'] as Map<String, dynamic>),
      staff: (json['staff'] as List<dynamic>)
          .map((e) => Staff.fromJson(e as Map<String, dynamic>))
          .toList(),
      assignedBy: json['assignedBy'] != null ? AssignedBy.fromJson(json['assignedBy'] as Map<String, dynamic>) : null,
    );
  }
}

class Home {
  final String homeId;
  final String homeName;
  final bool isActive;

  Home({
    required this.homeId,
    required this.homeName,
    required this.isActive,
  });

  factory Home.fromJson(Map<String, dynamic> json) {
    return Home(
      homeId: json['homeId'] as String,
      homeName: json['homeName'] as String,
      isActive: json['isActive'] as bool,
    );
  }
}

class Staff {
  final String userId;
  final String userCode;
  final UserDetails userDetails;

  Staff({
    required this.userId,
    required this.userCode,
    required this.userDetails,
  });

  factory Staff.fromJson(Map<String, dynamic> json) {
    return Staff(
      userId: json['userId'] as String,
      userCode: json['userCode'] as String,
      userDetails: UserDetails.fromJson(json['userDetails'] as Map<String, dynamic>),
    );
  }
}

class AssignedBy {
  final String userId;
  final String userCode;
  final UserDetails userDetails;

  AssignedBy({
    required this.userId,
    required this.userCode,
    required this.userDetails,
  });

  factory AssignedBy.fromJson(Map<String, dynamic> json) {
    return AssignedBy(
      userId: json['userId'] as String,
      userCode: json['userCode'] as String,
      userDetails: UserDetails.fromJson(json['userDetails'] as Map<String, dynamic>),
    );
  }
}

class UserDetails {
  final String firstName;
  final String lastName;
  final String profileUrl;

  UserDetails({
    required this.firstName,
    required this.lastName,
    required this.profileUrl,
  });

  factory UserDetails.fromJson(Map<String, dynamic> json) {
    return UserDetails(
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      profileUrl: json['profileUrl'] as String,
    );
  }
}
