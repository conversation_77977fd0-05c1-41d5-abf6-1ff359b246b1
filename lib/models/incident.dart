 import 'package:flutter/material.dart';

class Incident {
  final String id;
  final String name;
  final String description;
  final String reportedBy;
  final String level;
  final String status;
  final DateTime dateTime;
  final String linkedResident;
  final String notes;
  final String homeId;
  final String unitId;
  final String placeName;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<IncidentEvent> timeline;
  final List<IncidentAttachment> attachments;
  final HomeDetails? homeDetails;
  final UnitDetails? unitDetails;
  final StaffDetails? staffDetails;
  final ResidentDetails? residentDetails;

  Incident({
    required this.id,
    required this.name,
    this.description = '',
    required this.reportedBy,
    required this.level,
    required this.status,
    required this.dateTime,
    this.linkedResident = '',
    this.placeName = '',
    this.notes = '',
    this.homeId = '',
    this.unitId = '',
    DateTime? createdAt,
    DateTime? updatedAt,
    this.timeline = const [],
    this.attachments = const [],
    this.homeDetails,
    this.unitDetails,
    this.staffDetails,
    this.residentDetails,
  }) : 
    this.createdAt = createdAt ?? DateTime.now(),
    this.updatedAt = updatedAt ?? DateTime.now();

  factory Incident.fromJson(Map<String, dynamic> json) {
    return Incident(
      id: json['incidentId'] ?? '',
      name: json['incidentName'] ?? '',
      description: json['description'] ?? '',
      reportedBy: json['staffId'] ?? '',
      level: json['incidentLevel'] ?? '',
      status: json['status'] ?? '',
      dateTime: json['dateTime'] is String
          ? DateTime.parse(json['dateTime'])
          : json['dateTime'] ?? DateTime.now(),
      linkedResident: json['residentId'] ?? '',
      notes: json['note'] ?? '',
      homeId: json['homeId'] ?? '',
      unitId: json['unitId'] ?? '',
      placeName: json['placeName'] ?? '',
      createdAt: json['createdAt'] is String
          ? DateTime.parse(json['createdAt'])
          : json['createdAt'] ?? DateTime.now(),
      updatedAt: json['updatedAt'] is String
          ? DateTime.parse(json['updatedAt'])
          : json['updatedAt'] ?? DateTime.now(),
      timeline: (json['incidentTimelines'] as List?)
          ?.map((timeline) => IncidentEvent.fromJson(timeline))
          .toList() ?? [],
      attachments: (json['incidentAttachments'] as List?)
          ?.map((attachment) => IncidentAttachment.fromJson(attachment))
          .toList() ?? [],
      homeDetails: json['homeDetails'] != null 
          ? HomeDetails.fromJson(json['homeDetails']) 
          : null,
      unitDetails: json['unitDetails'] != null 
          ? UnitDetails.fromJson(json['unitDetails']) 
          : null,
      staffDetails: json['staffDetails'] != null 
          ? StaffDetails.fromJson(json['staffDetails']) 
          : null,
      residentDetails: json['residentDetails'] != null 
          ? ResidentDetails.fromJson(json['residentDetails']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'incidentId': id,
      'incidentName': name,
      'description': description,
      'staffId': reportedBy,
      'incidentLevel': level,
      'status': status,
      'placeName': placeName,
      'dateTime': dateTime.toIso8601String(),
      'residentId': linkedResident,
      'note': notes,
      'homeId': homeId,
      'unitId': unitId,
      'incidentTimelines': timeline.map((event) => event.toJson()).toList(),
      'incidentAttachments': attachments.map((attachment) => attachment.toJson()).toList(),
    };
  }
  
  // Helper method to get reporter name from staffDetails
  String getReporterName() {
    if (staffDetails != null && staffDetails!.userDetails != null) {
      return '${staffDetails!.userDetails!.firstName} ${staffDetails!.userDetails!.lastName}';
    }
    return reportedBy;
  }
  
  // Helper method to get resident name from residentDetails
  String getResidentName() {
    if (residentDetails != null && residentDetails!.userDetails != null) {
      return '${residentDetails!.userDetails!.firstName} ${residentDetails!.userDetails!.lastName}';
    }
    return linkedResident;
  }
  
  // Helper method to get home name
  String getHomeName() {
    return homeDetails?.homeName ?? homeId;
  }
  
  // Helper method to get unit name
  String getUnitName() {
    return unitDetails?.name ?? unitId;
  }
}

class IncidentEvent {
  final String id;
  final String title;
  final String description;
  final DateTime timestamp;
  final String type;

  IncidentEvent({
    required this.id,
    required this.title,
    required this.description,
    required this.timestamp,
    this.type = 'default',
  });
  
  factory IncidentEvent.fromJson(Map<String, dynamic> json) {
    return IncidentEvent(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      timestamp: json['timestamp'] is String
          ? DateTime.parse(json['timestamp'])
          : json['timestamp'] ?? DateTime.now(),
      type: json['type'] ?? 'default',
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'timestamp': timestamp.toIso8601String(),
      'type': type,
    };
  }
}

class IncidentAttachment {
  final String id;
  final String url;
  final String name;

  IncidentAttachment({
    required this.id,
    required this.url,
    required this.name,
  });

  factory IncidentAttachment.fromJson(Map<String, dynamic> json) {
    return IncidentAttachment(
      id: json['attachmentId'] ?? '',
      url: json['attachmentURL'] ?? '',
      name: json['attachmentName'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'attachmentId': id,
      'attachmentURL': url,
      'attachmentName': name,
    };
  }
}

class HomeDetails {
  final String homeId;
  final String homeName;
  
  HomeDetails({
    required this.homeId,
    required this.homeName,
  });
  
  factory HomeDetails.fromJson(Map<String, dynamic> json) {
    return HomeDetails(
      homeId: json['homeId'] ?? '',
      homeName: json['homeName'] ?? '',
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'homeId': homeId,
      'homeName': homeName,
    };
  }
}

class UnitDetails {
  final String unitId;
  final String name;
  
  UnitDetails({
    required this.unitId,
    required this.name,
  });
  
  factory UnitDetails.fromJson(Map<String, dynamic> json) {
    return UnitDetails(
      unitId: json['unitId'] ?? '',
      name: json['name'] ?? '',
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'unitId': unitId,
      'name': name,
    };
  }
}

class StaffDetails {
  final String userId;
  final UserDetails? userDetails;
  
  StaffDetails({
    required this.userId,
    this.userDetails,
  });
  
  factory StaffDetails.fromJson(Map<String, dynamic> json) {
    return StaffDetails(
      userId: json['userId'] ?? '',
      userDetails: json['userDetails'] != null 
          ? UserDetails.fromJson(json['userDetails']) 
          : null,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userDetails': userDetails?.toJson(),
    };
  }
}

class ResidentDetails {
  final String userId;
  final UserDetails? userDetails;
  
  ResidentDetails({
    required this.userId,
    this.userDetails,
  });
  
  factory ResidentDetails.fromJson(Map<String, dynamic> json) {
    return ResidentDetails(
      userId: json['userId'] ?? '',
      userDetails: json['userDetails'] != null 
          ? UserDetails.fromJson(json['userDetails']) 
          : null,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userDetails': userDetails?.toJson(),
    };
  }
}

class UserDetails {
  final String firstName;
  final String lastName;
  
  UserDetails({
    required this.firstName,
    required this.lastName,
  });
  
  factory UserDetails.fromJson(Map<String, dynamic> json) {
    return UserDetails(
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
    };
  }
}
