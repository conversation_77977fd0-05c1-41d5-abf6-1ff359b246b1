class ResidentFinanceBasic {
  final String budgetAmount;
  final String balance;
  final String voucherAmount;
  final String totalExpenses;
  final String reservedFunds;

  ResidentFinanceBasic({
    required this.budgetAmount,
    required this.balance,
    required this.voucherAmount,
    required this.totalExpenses,
    required this.reservedFunds,
  });

  factory ResidentFinanceBasic.fromJson(Map<String, dynamic> json) {
    return ResidentFinanceBasic(
      budgetAmount: json['budgetAmount'] ?? '0.00',
      balance: json['balance'] ?? '0.00',
      voucherAmount: json['voucherAmount'] ?? '0.00',
      totalExpenses: json['totalExpenses'] ?? '0.00',
      reservedFunds: json['reservedFunds'] ?? '0.00',
    );
  }
}

