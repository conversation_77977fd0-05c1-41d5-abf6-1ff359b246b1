enum TicketStatus {
  open,
  inProgress,
  completed,
  cancelled
}

class TicketModel {
  final String id;
  final String date;
  final String title;
  final String description;
  final String category;
  final String location;
  final String notes;
  final String addedBy;
  final String? residentId; // Optional, if applicable
  TicketStatus status;
  final List<String>? attachments;
  final List<TimelineEntry>? timeline;

  TicketModel({
    required this.id,
    required this.date,
    required this.title,
    required this.description,
    required this.notes,
    required this.category,
    required this.location,
    required this.residentId,
    required this.addedBy,
    required this.status,
    this.attachments,
    this.timeline,
  });

  factory TicketModel.fromJson(Map<String, dynamic> json) {
    // Map status string to enum
    TicketStatus mapStatus(String? status) {
      switch (status) {
        case 'new':
        case 'open':
          return TicketStatus.open;
        case 'inProgress':
          return TicketStatus.inProgress;
        case 'completed':
          return TicketStatus.completed;
        case 'cancelled':
          return TicketStatus.cancelled;
        default:
          return TicketStatus.open;
      }
    }
    return TicketModel(
      id: json['serviceRequestId'] ?? '',
      date: json['dateTime'] ?? '',
      title: json['serviceRequestName'] ?? '',
      description: json['description'] ?? '',
      residentId: json['residentId'] ?? '',
      notes: json['note'] ?? '',
      category: json['serviceRequestCategory'] != null ? json['serviceRequestCategory']['name'] ?? '' : '',
      location: json['location'] ?? '',
      addedBy: '${json['staffDetails']['userDetails']['firstName']} ${json['staffDetails']['userDetails']['lastName']}' ?? '',
      status: mapStatus(json['status']),
      attachments: json['serviceRequestAttachments'] != null
          ? List<String>.from((json['serviceRequestAttachments'] as List).map((a) => a['attachmentURL'] ?? ''))
          : null,
      timeline: null, // Map serviceRequestTimelines if needed
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date,
      'title': title,
      'description': description,
      'notes': notes,
      'category': category,
      'residentId': residentId,
      'location': location,
      'addedBy': addedBy,
      'status': status.toString().split('.').last,
      'attachments': attachments,
      'timeline': timeline?.map((e) => e.toJson()).toList(),
    };
  }

  TicketModel copyWith({
    String? id,
    String? date,
    String? title,
    String? description,
    String? residentId,
    String? notes,
    String? category,
    String? location,
    String? addedBy,
    TicketStatus? status,
    List<String>? attachments,
    List<TimelineEntry>? timeline,
  }) {
    return TicketModel(
      id: id ?? this.id,
      date: date ?? this.date,
      title: title ?? this.title,
      description: description ?? this.description,
      notes: notes ?? this.notes,
      category: category ?? this.category,
      residentId: this.residentId, // Resident ID is not nullable in the model
      location: location ?? this.location,
      addedBy: addedBy ?? this.addedBy,
      status: status ?? this.status,
      attachments: attachments ?? this.attachments,
      timeline: timeline ?? this.timeline,
    );
  }
}


class TimelineEntry {
  final String date;
  final String description;

  TimelineEntry({required this.date, required this.description});

  factory TimelineEntry.fromJson(Map<String, dynamic> json) {
    return TimelineEntry(
      date: json['date'],
      description: json['description'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date,
      'description': description,
    };
  }
}
