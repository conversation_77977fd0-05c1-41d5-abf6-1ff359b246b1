class EmergencyContact {
  final String? emContactId;
  final String? firstName;
  final String? secondName;
  final String? lastName;
  final String? phoneNumber;
  final String? emailAddress;
  final String? relation;
  final String? address;
  final String? createdAt;
  final String? updatedAt;

  EmergencyContact({
    this.emContactId,
    this.firstName,
    this.secondName,
    this.lastName,
    this.phoneNumber,
    this.emailAddress,
    this.relation,
    this.address,
    this.createdAt,
    this.updatedAt,
  });

  factory EmergencyContact.fromJson(Map<String, dynamic> json) {
    return EmergencyContact(
      emContactId: json['emContactId'] as String?,
      firstName: json['firstName'] as String?,
      secondName: json['secondName'] as String?,
      lastName: json['lastName'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      emailAddress: json['emailAddress'] as String?,
      relation: json['relation'] as String?,
      address: json['address'] as String?,
      createdAt: json['createdAt'] as String?,
      updatedAt: json['updatedAt'] as String?,
    );
  }
}

