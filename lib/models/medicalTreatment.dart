class Followup {
  final String followupId;
  final String medicalTreatmentId;
  final String doctorName;
  final String followupDescription;
  final DateTime followupDate;
  final bool isActive;
  final DateTime createdAt;

  Followup({
    required this.followupId,
    required this.medicalTreatmentId,
    required this.doctorN<PERSON>,
    required this.followupDescription,
    required this.followupDate,
    required this.isActive,
    required this.createdAt,
  });

  factory Followup.fromJson(Map<String, dynamic> json) {
    return Followup(
      followupId: json['followupId'],
      medicalTreatmentId: json['medicalTreatmentId'],
      doctorName: json['doctorName'] ?? '',
      followupDescription: json['followupDescription'],
      followupDate: DateTime.parse(json['followupDate']),
      isActive: json['isActive'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'followupId': followupId,
      'medicalTreatmentId': medicalTreatmentId,
      'doctorName': doctorName,
      'followupDescription': followupDescription,
      'followupDate': followupDate.toIso8601String(),
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

class MedicalTreatment {
  final String medicalTreatmentId;
  final String doctorName;
  final String residentId;
  final String treatmentName;
  final String treatmentDescription;
  final DateTime treatmentDate;
  final bool isActive;
  final DateTime createdAt;
  final List<Followup> followups;
  final List<String> attachments;

  MedicalTreatment({
    required this.medicalTreatmentId,
    required this.doctorName,
    required this.residentId,
    required this.treatmentName,
    required this.treatmentDescription,
    required this.treatmentDate,
    required this.isActive,
    required this.createdAt,
    this.followups = const [],
    this.attachments = const [],
  });

  factory MedicalTreatment.fromJson(Map<String, dynamic> json) {
    return MedicalTreatment(
      medicalTreatmentId: json['medicalTreatmentId'],
      doctorName: json['doctorName'] ?? '',
      residentId: json['residentId'],
      treatmentName: json['treatmentName'],
      treatmentDescription: json['treatmentDescription'],
      treatmentDate: DateTime.parse(json['treatmentDate']),
      isActive: json['isActive'],
      createdAt: DateTime.parse(json['createdAt']),
      followups: (json['followups'] as List<dynamic>?)
              ?.map((item) => Followup.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
      attachments: (json['attachments'] as List<dynamic>?)
              ?.map((item) => item as String)
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'medicalTreatmentId': medicalTreatmentId,
      'doctorName': doctorName,
      'residentId': residentId,
      'treatmentName': treatmentName,
      'treatmentDescription': treatmentDescription,
      'treatmentDate': treatmentDate.toIso8601String(),
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'followups': followups.map((f) => f.toJson()).toList(),
      'attachments': attachments,
    };
  }
}