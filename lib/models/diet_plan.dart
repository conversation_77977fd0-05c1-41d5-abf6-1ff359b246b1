import 'package:equatable/equatable.dart';

class DietPlan extends Equatable {
  final String dietPlanId;
  final String dietTypeId;
  final String name;
  final String startTime;
  final String endTime;
  final DateTime createdAt;

  const DietPlan({
    required this.dietPlanId,
    required this.dietTypeId,
    required this.name,
    required this.startTime,
    required this.endTime,
    required this.createdAt,
  });

  factory DietPlan.fromJson(Map<String, dynamic> json) {
    return DietPlan(
      dietPlanId: json['dietPlanId'] as String,
      dietTypeId: json['dietTypeId'] as String,
      name: json['name'] as String,
      startTime: json['startTime'] as String,
      endTime: json['endTime'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dietPlanId': dietPlanId,
      'dietTypeId': dietTypeId,
      'name': name,
      'startTime': startTime,
      'endTime': endTime,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [
    dietPlanId,
    dietTypeId,
    name,
    startTime,
    endTime,
    createdAt,
  ];
}

