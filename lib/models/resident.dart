class Resident {
  final String id;
  final String userCode;
  final String name;
  final String gender;
  final String phone;
  final String email;
  final String age;
  final String image;
  final String roomNumber;
  final String home;
  final String unit;
  final String careLevel;
  final String admissionDate;
  final String status;
  final String nextOfKin;
  final String medicationSchedule;
  final String bloodType;
  final String dob;
  final String patientStatus;

  Resident({
    required this.id,
    required this.userCode,
    required this.name,
    required this.gender,
    required this.phone,
    required this.email,
    required this.image,
    required this.home,
    required this.unit,
    required this.age,
    required this.roomNumber,
    required this.careLevel,
    required this.admissionDate,
    required this.status,
    required this.nextOfKin,
    required this.medicationSchedule,
    this.bloodType = '',
    this.dob = '',
    this.patientStatus = '',
  });

  factory Resident.fromJson(Map<String, dynamic> json) {
    return Resident(
      id: json['userId'] ?? '',
      name: ((json['firstName'] ?? '') + ' ' + (json['lastName'] ?? '')).trim(),
      gender: json['gender'] ?? '',
      userCode: json['userCode'] ?? '',
      phone: json['phoneNumber'] ?? '',
      email: json['emailAddress'] ?? '',
      age: json['age']?.toString() ?? '',
      image: json['profileUrl'] ?? '',
      home: json['home'] ?? '',
      unit: json['unit'] ?? '',
      roomNumber: json['roomNumber'] ?? '',
      careLevel: json['careLevel'] ?? '',
      admissionDate: json['admissionDate'] ?? '',
      status: json['status'] ?? '',
      nextOfKin: json['nextOfKin'] ?? '',
      medicationSchedule: json['medicationSchedule'] ?? '',
      bloodType: json['bloodGroup'] ?? '',
      dob: (json['dateOfBirth'] ?? '').toString().split('T').first,
      patientStatus: json['patientStatus'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': id,
      'userCode': userCode,
      'firstName': name.split(' ').first,
      'lastName': name.split(' ').last,
      'gender': gender,
      'phoneNumber': phone,
      'emailAddress': email,
      'age': age,
      'profileUrl': image,
      'home': home,
      'unit': unit,
      'roomNumber': roomNumber,
      'careLevel': careLevel,
      'admissionDate': admissionDate,
      'status': status,
      'nextOfKin': nextOfKin,
      'medicationSchedule': medicationSchedule,
      'bloodType': bloodType,
      'dob': dob,
      'patientStatus': patientStatus,
    };
  }

  int get calculatedAge {
    if (dob == null || dob.isEmpty) return 0;
    try {
      final birthDate = DateTime.parse(dob);
      final now = DateTime.now();
      int age = now.year - birthDate.year;
      if (now.month < birthDate.month || (now.month == birthDate.month && now.day < birthDate.day)) {
        age--;
      }
      return age;
    } catch (_) {
      return 0;
    }
  }
}
