import 'package:equatable/equatable.dart';

class Meal extends Equatable {
  final String mealId;
  final String name;
  final String totalCal;
  final String time;
  final String note;
  final bool isActive;
  final List<MealFoodItem> mealFoodItems;
  final List<MealAttachment> mealAttachments;

  const Meal({
    required this.mealId,
    required this.name,
    required this.totalCal,
    required this.time,
    required this.note,
    required this.isActive,
    required this.mealFoodItems,
    required this.mealAttachments,
  });

  factory Meal.fromJson(Map<String, dynamic> json) {
    return Meal(
      mealId: json['mealId'] as String,
      name: json['name'] as String,
      totalCal: json['totalCal'] as String,
      time: json['time'] as String,
      note: json['note'] as String,
      isActive: json['isActive'] as bool,
      mealFoodItems: (json['mealFoodItems'] as List<dynamic>?)?.map((e) => MealFoodItem.fromJson(e)).toList() ?? [],
      mealAttachments: (json['mealAttachments'] as List<dynamic>?)?.map((e) => MealAttachment.fromJson(e)).toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'mealId': mealId,
      'name': name,
      'totalCal': totalCal,
      'time': time,
      'note': note,
      'isActive': isActive,
      'mealFoodItems': mealFoodItems.map((e) => e.toJson()).toList(),
      'mealAttachments': mealAttachments.map((e) => e.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [
    mealId,
    name,
    totalCal,
    time,
    note,
    isActive,
    mealFoodItems,
    mealAttachments,
  ];
}

class MealFoodItem extends Equatable {
  final int quantity;
  final FoodItem foodItem;

  const MealFoodItem({
    required this.quantity,
    required this.foodItem,
  });

  factory MealFoodItem.fromJson(Map<String, dynamic> json) {
    return MealFoodItem(
      quantity: json['quantity'] as int,
      foodItem: FoodItem.fromJson(json['foodItem'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'quantity': quantity,
      'foodItem': foodItem.toJson(),
    };
  }

  @override
  List<Object?> get props => [quantity, foodItem];
}

class FoodItem extends Equatable {
  final String foodItemId;
  final String name;
  final String calories;

  const FoodItem({
    required this.foodItemId,
    required this.name,
    required this.calories,
  });

  factory FoodItem.fromJson(Map<String, dynamic> json) {
    return FoodItem(
      foodItemId: json['foodItemId'] as String,
      name: json['name'] as String,
      calories: json['calories'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'foodItemId': foodItemId,
      'name': name,
      'calories': calories,
    };
  }

  @override
  List<Object?> get props => [foodItemId, name, calories];
}

class MealAttachment extends Equatable {
  // Define fields as needed, currently empty in the sample
  const MealAttachment();

  factory MealAttachment.fromJson(Map<String, dynamic> json) {
    return const MealAttachment();
  }

  Map<String, dynamic> toJson() => {};

  @override
  List<Object?> get props => [];
}

