class CalendarTask {
  final String id;
  final String title;
  final String notes;
  final DateTime dateTime;
  final String type;
  final bool isCompleted;

  CalendarTask({
    required this.id,
    required this.title,
    required this.notes,
    required this.dateTime,
    required this.type,
    this.isCompleted = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'notes': notes,
      'dateTime': dateTime.toIso8601String(),
      'type': type,
      'isCompleted': isCompleted,
    };
  }

  factory CalendarTask.fromJson(Map<String, dynamic> json) {
    return CalendarTask(
      id: json['id'] as String,
      title: json['title'] as String,
      notes: json['notes'] as String,
      dateTime: DateTime.parse(json['dateTime'] as String),
      type: json['type'] as String,
      isCompleted: json['isCompleted'] as bool? ?? false,
    );
  }
}
