import 'package:equatable/equatable.dart';

class HandoffNote extends Equatable {
  final String id;
  final String residentId;
  final String content;
  final String assignedTo;
  final DateTime createdAt;
  final String createdBy;
  final bool isCompleted;
  final DateTime? completedAt;
  final String? completedBy;
  final List<String> attachments;

  const HandoffNote({
    required this.id,
    required this.residentId,
    required this.content,
    required this.assignedTo,
    required this.createdAt,
    required this.createdBy,
    this.isCompleted = false,
    this.completedAt,
    this.completedBy,
    this.attachments = const [],
  });

  HandoffNote copyWith({
    String? id,
    String? residentId,
    String? content,
    String? assignedTo,
    DateTime? createdAt,
    String? createdBy,
    bool? isCompleted,
    DateTime? completedAt,
    String? completedBy,
    List<String>? attachments,
  }) {
    return HandoffNote(
      id: id ?? this.id,
      residentId: residentId ?? this.residentId,
      content: content ?? this.content,
      assignedTo: assignedTo ?? this.assignedTo,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      isCompleted: isCompleted ?? this.isCompleted,
      completedAt: completedAt ?? this.completedAt,
      completedBy: completedBy ?? this.completedBy,
      attachments: attachments ?? this.attachments,
    );
  }

  factory HandoffNote.fromJson(Map<String, dynamic> json) {
    return HandoffNote(
      id: json['id'] ?? '',
      residentId: json['residentId'] ?? '',
      content: json['content'] ?? '',
      assignedTo: json['assignedTo'] ?? '',
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      createdBy: json['createdBy'] ?? '',
      isCompleted: json['isCompleted'] ?? false,
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'])
          : null,
      completedBy: json['completedBy'],
      attachments: json['attachments'] != null
          ? List<String>.from(json['attachments'])
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'residentId': residentId,
      'content': content,
      'assignedTo': assignedTo,
      'createdAt': createdAt.toIso8601String(),
      'createdBy': createdBy,
      'isCompleted': isCompleted,
      'completedAt': completedAt?.toIso8601String(),
      'completedBy': completedBy,
      'attachments': attachments,
    };
  }

  @override
  List<Object?> get props => [
        id,
        residentId,
        content,
        assignedTo,
        createdAt,
        createdBy,
        isCompleted,
        completedAt,
        completedBy,
        attachments,
      ];
}