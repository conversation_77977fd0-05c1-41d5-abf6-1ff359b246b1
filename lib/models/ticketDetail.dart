import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' as rootBundle;

class TicketDetailModel {
  final String title;
  final String date;
  final String addedBy;
  final String category;
  final String location;
  final String notes;
  final String specialInstructions;
  final String status;
  final List<String> attachments;
  final List<TimelineEntry> timeline;

  TicketDetailModel({
    required this.title,
    required this.date,
    required this.addedBy,
    required this.category,
    required this.location,
    required this.notes,
    required this.specialInstructions,
    required this.status,
    required this.attachments,
    required this.timeline,
  });

  factory TicketDetailModel.fromJson(Map<String, dynamic> json) {
    return TicketDetailModel(
      title: json['title'],
      date: json['date'],
      addedBy: json['addedBy'],
      category: json['category'],
      location: json['location'],
      notes: json['notes'],
      specialInstructions: json['specialInstructions'],
      status: json['status'],
      attachments: List<String>.from(json['attachments']),
      timeline: (json['timeline'] as List)
          .map((e) => TimelineEntry.fromJson(e))
          .toList(),
    );
  }
}

class TimelineEntry {
  final String date;
  final String description;

  TimelineEntry({required this.date, required this.description});

  factory TimelineEntry.fromJson(Map<String, dynamic> json) {
    return TimelineEntry(
      date: json['date'],
      description: json['description'],
    );
  }
}
