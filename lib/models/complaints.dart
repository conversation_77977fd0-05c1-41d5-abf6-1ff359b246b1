import 'package:equatable/equatable.dart';

class ComplaintTimelineEvent extends Equatable {
  final String id;
  final String title;
  final String description;
  final DateTime timestamp;
  final String type; // e.g., 'creation', 'status_update', 'comment', etc.
  final String? userId; // ID of the user who created the event
  final String? userName; // Name of the user who created the event

  const ComplaintTimelineEvent({
    required this.id,
    required this.title,
    required this.description,
    required this.timestamp,
    required this.type,
    this.userId,
    this.userName,
  });

  factory ComplaintTimelineEvent.fromJson(Map<String, dynamic> json) {
    return ComplaintTimelineEvent(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'])
          : DateTime.now(),
      type: json['type'] ?? '',
      userId: json['userId'],
      userName: json['userName'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'timestamp': timestamp.toIso8601String(),
      'type': type,
      'userId': userId,
      'userName': userName,
    };
  }

  @override
  List<Object?> get props =>
      [id, title, description, timestamp, type, userId, userName];
}

class ComplaintAttachment {
  final String attachmentId;
  final String attachmentURL;
  final String attachmentName;

  const ComplaintAttachment({
    required this.attachmentId,
    required this.attachmentURL,
    required this.attachmentName,
  });

  factory ComplaintAttachment.fromJson(Map<String, dynamic> json) {
    return ComplaintAttachment(
      attachmentId: json['attachmentId'] ?? '',
      attachmentURL: json['attachmentURL'] ?? '',
      attachmentName: json['attachmentName'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'attachmentId': attachmentId,
      'attachmentURL': attachmentURL,
      'attachmentName': attachmentName,
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ComplaintAttachment &&
          runtimeType == other.runtimeType &&
          attachmentId == other.attachmentId &&
          attachmentURL == other.attachmentURL &&
          attachmentName == other.attachmentName;

  @override
  int get hashCode =>
      attachmentId.hashCode ^ attachmentURL.hashCode ^ attachmentName.hashCode;
}

class Complaints extends Equatable {
  final String complaintId;
  final String staffId;
  final String? residentId;
  final String complaintName;
  final String homeId;
  final String unitId;
  final String complaintLevel;
  final String status;
  final DateTime dateTime;
  final String description;
  final String note;
  final String placeName;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<ComplaintAttachment> complaintAttachments;
  final List<ComplaintTimelineEvent> complaintTimelines;
  final StaffDetails? staffDetails;
  final HomeDetails? homeDetails;
  final UnitDetails? unitDetails;
  final ResidentDetails? residentDetails;

  Complaints({
    required this.complaintId,
    required this.staffId,
    this.residentId,
    required this.complaintName,
    required this.homeId,
    required this.unitId,
    required this.complaintLevel,
    required this.status,
    required this.dateTime,
    required this.description,
    this.placeName = '',
    this.note = '',
    DateTime? createdAt,
    DateTime? updatedAt,
    this.complaintAttachments = const [],
    this.complaintTimelines = const [],
    this.staffDetails,
    this.homeDetails,
    this.unitDetails,
    this.residentDetails,
  })  : this.createdAt = createdAt ?? DateTime.now(),
        this.updatedAt = updatedAt ?? DateTime.now();

  factory Complaints.fromJson(Map<String, dynamic> json) {
    return Complaints(
      complaintId: json['complaintId'] ?? '',
      staffId: json['staffId'] ?? '',
      residentId: json['residentId'],
      complaintName: json['complaintName'] ?? '',
      homeId: json['homeId'] ?? '',
      unitId: json['unitId'] ?? '',
      complaintLevel: json['complaintLevel'] ?? '',
      status: json['status'] ?? '',
      placeName: json['placeName'] ?? '',
      dateTime: json['dateTime'] != null
          ? DateTime.parse(json['dateTime'])
          : DateTime.now(),
      description: json['description'] ?? '',
      note: json['note'] ?? '',
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : DateTime.now(),
      complaintAttachments: json['complaintAttachments'] != null
          ? (json['complaintAttachments'] as List)
              .map((attachment) => ComplaintAttachment.fromJson(attachment))
              .toList()
          : [],
      complaintTimelines: json['complaintTimelines'] != null
          ? (json['complaintTimelines'] as List)
              .map((event) => ComplaintTimelineEvent.fromJson(event))
              .toList()
          : [],
      staffDetails: json['staffDetails'] != null
          ? StaffDetails.fromJson(json['staffDetails'])
          : null,
      homeDetails: json['homeDetails'] != null
          ? HomeDetails.fromJson(json['homeDetails'])
          : null,
      unitDetails: json['unitDetails'] != null
          ? UnitDetails.fromJson(json['unitDetails'])
          : null,
      residentDetails: json['residentDetails'] != null
          ? ResidentDetails.fromJson(json['residentDetails'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'complaintId': complaintId,
      'staffId': staffId,
      'residentId': residentId,
      'complaintName': complaintName,
      'homeId': homeId,
      'unitId': unitId,
      'complaintLevel': complaintLevel,
      'placeName': placeName,
      'status': status,
      'dateTime': dateTime.toIso8601String(),
      'description': description,
      'note': note,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'complaintAttachments': complaintAttachments
          .map((attachment) => attachment.toJson())
          .toList(),
      'complaintTimelines':
          complaintTimelines.map((event) => event.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [
        complaintId,
        staffId,
        residentId,
        complaintName,
        homeId,
        unitId,
        complaintLevel,
        status,
        dateTime,
        placeName,
        description,
        note,
        createdAt,
        updatedAt,
        complaintAttachments,
        complaintTimelines,
        staffDetails,
        homeDetails,
        unitDetails,
        residentDetails,
      ];
}

class HomeDetails {
  final String homeId;
  final String homeName;

  const HomeDetails({
    required this.homeId,
    required this.homeName,
  });

  factory HomeDetails.fromJson(Map<String, dynamic> json) {
    return HomeDetails(
      homeId: json['homeId'] ?? '',
      homeName: json['homeName'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'homeId': homeId,
      'homeName': homeName,
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HomeDetails &&
          runtimeType == other.runtimeType &&
          homeId == other.homeId &&
          homeName == other.homeName;

  @override
  int get hashCode => homeId.hashCode ^ homeName.hashCode;
}

class UnitDetails {
  final String unitId;
  final String name;

  const UnitDetails({
    required this.unitId,
    required this.name,
  });

  factory UnitDetails.fromJson(Map<String, dynamic> json) {
    return UnitDetails(
      unitId: json['unitId'] ?? '',
      name: json['name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'unitId': unitId,
      'name': name,
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UnitDetails &&
          runtimeType == other.runtimeType &&
          unitId == other.unitId &&
          name == other.name;

  @override
  int get hashCode => unitId.hashCode ^ name.hashCode;
}

class StaffDetails {
  final String userId;
  final UserDetails? userDetails;

  const StaffDetails({
    required this.userId,
    this.userDetails,
  });

  factory StaffDetails.fromJson(Map<String, dynamic> json) {
    return StaffDetails(
      userId: json['userId'] ?? '',
      userDetails: json['userDetails'] != null
          ? UserDetails.fromJson(json['userDetails'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userDetails': userDetails?.toJson(),
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StaffDetails &&
          runtimeType == other.runtimeType &&
          userId == other.userId &&
          userDetails == other.userDetails;

  @override
  int get hashCode => userId.hashCode ^ userDetails.hashCode;
}

class ResidentDetails {
  final String userId;
  final UserDetails? userDetails;

  const ResidentDetails({
    required this.userId,
    this.userDetails,
  });

  factory ResidentDetails.fromJson(Map<String, dynamic> json) {
    return ResidentDetails(
      userId: json['userId'] ?? '',
      userDetails: json['userDetails'] != null
          ? UserDetails.fromJson(json['userDetails'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userDetails': userDetails?.toJson(),
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ResidentDetails &&
          runtimeType == other.runtimeType &&
          userId == other.userId &&
          userDetails == other.userDetails;

  @override
  int get hashCode => userId.hashCode ^ userDetails.hashCode;
}

class UserDetails {
  final String firstName;
  final String lastName;

  const UserDetails({
    required this.firstName,
    required this.lastName,
  });

  factory UserDetails.fromJson(Map<String, dynamic> json) {
    return UserDetails(
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserDetails &&
          runtimeType == other.runtimeType &&
          firstName == other.firstName &&
          lastName == other.lastName;

  @override
  int get hashCode => firstName.hashCode ^ lastName.hashCode;
}
