import 'package:equatable/equatable.dart';

class Doctor<PERSON>ote extends Equatable {
  final String doctorNoteId;
  final String userId;
  final String title;
  final String description;
  final DateTime date;
  final String doctorName;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<dynamic> doctorNoteAttachments;

  const Doctor<PERSON><PERSON>({
    required this.doctorNoteId,
    required this.userId,
    required this.title,
    required this.description,
    required this.date,
    required this.doctorName,
    this.createdAt,
    this.updatedAt,
    this.doctorNoteAttachments = const [],
  });

  factory DoctorNote.fromJson(Map<String, dynamic> json) {
    return DoctorNote(
      doctorNoteId: json['doctorNoteId'] ?? '',
      userId: json['userId'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      date: DateTime.parse(json['date']),
      doctorName: json['doctorName'] ?? '',
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      doctorNoteAttachments: json['doctorNoteAttachments'] ?? [],
    );
  }

  Map<String, dynamic> toJson() => {
    'doctorNoteId': doctorNoteId,
    'userId': userId,
    'title': title,
    'description': description,
    'date': date.toIso8601String(),
    'doctorName': doctorName,
    'createdAt': createdAt?.toIso8601String(),
    'updatedAt': updatedAt?.toIso8601String(),
    'doctorNoteAttachments': doctorNoteAttachments,
  };

  DoctorNote copyWith({
    String? doctorNoteId,
    String? userId,
    String? title,
    String? description,
    DateTime? date,
    String? doctorName,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<dynamic>? doctorNoteAttachments,
  }) {
    return DoctorNote(
      doctorNoteId: doctorNoteId ?? this.doctorNoteId,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      description: description ?? this.description,
      date: date ?? this.date,
      doctorName: doctorName ?? this.doctorName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      doctorNoteAttachments: doctorNoteAttachments ?? this.doctorNoteAttachments,
    );
  }

  @override
  List<Object?> get props => [doctorNoteId, userId, title, description, date, doctorName, createdAt, updatedAt, doctorNoteAttachments];
}
