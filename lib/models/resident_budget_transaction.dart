class ResidentBudgetTransaction {
  final String transactionId;
  final bool isAdd;
  final num amount;
  final String note;
  final String specifications;
  final String staffId;
  final bool isByAdmin;
  final bool isDiscarded;
  final String? reason;
  final bool? isDiscardedByAdmin;
  final String? discardedBy;
  final DateTime createdAt;
  final DateTime transactionDate;
  final TransactionCategory category;
  final TopUp? topUp;

  ResidentBudgetTransaction({
    required this.transactionId,
    required this.isAdd,
    required this.amount,
    required this.note,
    required this.specifications,
    required this.staffId,
    required this.isByAdmin,
    required this.isDiscarded,
    this.reason,
    this.isDiscardedByAdmin,
    this.discardedBy,
    required this.createdAt,
    required this.transactionDate,
    required this.category,
    this.topUp,
  });

  factory ResidentBudgetTransaction.fromJson(Map<String, dynamic> json) {
    return ResidentBudgetTransaction(
      transactionId: json['transactionId'],
      isAdd: json['isAdd'],
      amount: json['amount'],
      note: json['note'] ?? '',
      specifications: json['specifications'] ?? '',
      staffId: json['staffId'] ?? '',
      isByAdmin: json['isByAdmin'],
      isDiscarded: json['isDiscarded'],
      reason: json['reason'],
      isDiscardedByAdmin: json['isDiscardedByAdmin'],
      discardedBy: json['discardedBy'],
      createdAt: DateTime.parse(json['createdAt']),
      transactionDate: DateTime.parse(json['transactionDate']),
      category: TransactionCategory.fromJson(json['category']),
      topUp: json['topUp'] != null ? TopUp.fromJson(json['topUp']) : null,
    );
  }
}

class TransactionCategory {
  final String transactionCategoryId;
  final String name;
  final String description;

  TransactionCategory({
    required this.transactionCategoryId,
    required this.name,
    required this.description,
  });

  factory TransactionCategory.fromJson(Map<String, dynamic> json) {
    return TransactionCategory(
      transactionCategoryId: json['transactionCategoryId'],
      name: json['name'],
      description: json['description'],
    );
  }
}

class TopUp {
  final String topUpId;
  final String name;
  final String description;

  TopUp({
    required this.topUpId,
    required this.name,
    required this.description,
  });

  factory TopUp.fromJson(Map<String, dynamic> json) {
    return TopUp(
      topUpId: json['topUpId'],
      name: json['name'],
      description: json['description'],
    );
  }
}

