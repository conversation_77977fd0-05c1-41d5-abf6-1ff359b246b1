import 'package:equatable/equatable.dart';

class StaffTask extends Equatable {
  final String assignTaskId;
  final String task;
  final DateTime date;
  final String taskColor;
  final bool isTenant;
  final Home home;
  final List<Staff> staff;
  final AssignedBy? assignedBy;

  const StaffTask({
    required this.assignTaskId,
    required this.task,
    required this.date,
    required this.taskColor,
    required this.isTenant,
    required this.home,
    required this.staff,
    required this.assignedBy,
  });

  factory StaffTask.fromJson(Map<String, dynamic> json) {
    return StaffTask(
      assignTaskId: json['assignTaskId'],
      task: json['task'],
      date: DateTime.parse(json['date']),
      taskColor: json['taskColor'],
      isTenant: json['isTenant'],
      home: Home.fromJson(json['home']),
      staff: (json['staff'] as List).map((e) => Staff.fromJson(e)).toList(),
      assignedBy: json['assignedBy'] != null ? AssignedBy.fromJson(json['assignedBy']) : null,
    );
  }

  @override
  List<Object?> get props => [assignTaskId, task, date, taskColor, isTenant, home, staff, assignedBy];
}

class Home extends Equatable {
  final String homeId;
  final String homeName;
  final bool isActive;

  const Home({
    required this.homeId,
    required this.homeName,
    required this.isActive,
  });

  factory Home.fromJson(Map<String, dynamic> json) {
    return Home(
      homeId: json['homeId'],
      homeName: json['homeName'],
      isActive: json['isActive'],
    );
  }

  @override
  List<Object?> get props => [homeId, homeName, isActive];
}

class Staff extends Equatable {
  final String userId;
  final String userCode;
  final UserDetails userDetails;

  const Staff({
    required this.userId,
    required this.userCode,
    required this.userDetails,
  });

  factory Staff.fromJson(Map<String, dynamic> json) {
    return Staff(
      userId: json['userId'],
      userCode: json['userCode'],
      userDetails: UserDetails.fromJson(json['userDetails']),
    );
  }

  @override
  List<Object?> get props => [userId, userCode, userDetails];
}

class AssignedBy extends Equatable {
  final String userId;
  final String userCode;
  final UserDetails userDetails;

  const AssignedBy({
    required this.userId,
    required this.userCode,
    required this.userDetails,
  });

  factory AssignedBy.fromJson(Map<String, dynamic> json) {
    return AssignedBy(
      userId: json['userId'],
      userCode: json['userCode'],
      userDetails: UserDetails.fromJson(json['userDetails']),
    );
  }

  @override
  List<Object?> get props => [userId, userCode, userDetails];
}

class UserDetails extends Equatable {
  final String firstName;
  final String lastName;
  final String profileUrl;

  const UserDetails({
    required this.firstName,
    required this.lastName,
    required this.profileUrl,
  });

  factory UserDetails.fromJson(Map<String, dynamic> json) {
    return UserDetails(
      firstName: json['firstName'],
      lastName: json['lastName'],
      profileUrl: json['profileUrl'],
    );
  }

  @override
  List<Object?> get props => [firstName, lastName, profileUrl];
}
