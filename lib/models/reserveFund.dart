class ReserveFund {
  final String date;
  final String staff;
  final int amount;
  final String action;

  ReserveFund({
    required this.date,
    required this.staff,
    required this.amount,
    required this.action,
  });

  factory ReserveFund.fromJson(Map<String, dynamic> json) {
    return ReserveFund(
      date: json['date'],
      staff: json['staff'],
      amount: json['amount'],
      action: json['action'],
    );
  }
}
