import 'package:equatable/equatable.dart';

class ResidentAssignedMedicine extends Equatable {
  final String medicalTreatmentId;
  final String assignMedicineId;
  final bool isSequential;
  final DateTime startDate;
  final DateTime endDate;
  final String? notes;
  final bool isActive;
  final Medicine medicine;
  final List<dynamic> medicineAttachments;
  final List<SelectTime> selectTimes;
  final List<dynamic> customDates;
  final MedTreatmentAssignMed medTreatmentAssignMed;

  const ResidentAssignedMedicine({
    required this.medicalTreatmentId,
    required this.assignMedicineId,
    required this.isSequential,
    required this.startDate,
    required this.endDate,
    this.notes,
    required this.isActive,
    required this.medicine,
    required this.medicineAttachments,
    required this.selectTimes,
    required this.customDates,
    required this.medTreatmentAssignMed,
  });

  factory ResidentAssignedMedicine.fromJson(Map<String, dynamic> json) {
    return ResidentAssignedMedicine(
      medicalTreatmentId: json['medicalTreatmentId'],
      assignMedicineId: json['assignMedicineId'],
      isSequential: json['isSequential'],
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      notes: json['notes'],
      isActive: json['isActive'],
      medicine: Medicine.fromJson(json['medicine']),
      medicineAttachments: json['medicineAttachments'] ?? [],
      selectTimes: (json['selectTimes'] as List? ?? [])
          .map((e) => SelectTime.fromJson(e)).toList(),
      customDates: json['customDates'] ?? [],
      medTreatmentAssignMed: MedTreatmentAssignMed.fromJson(json['MedTreatmentAssignMed']),
    );
  }

  @override
  List<Object?> get props => [
    medicalTreatmentId,
    assignMedicineId,
    isSequential,
    startDate,
    endDate,
    notes,
    isActive,
    medicine,
    medicineAttachments,
    selectTimes,
    customDates,
    medTreatmentAssignMed,
  ];
}

class Medicine extends Equatable {
  final String medicineId;
  final String medicineName;
  final String description;
  final String dosage;
  final String usage;
  final String sideEffects;
  final MedicineType medicineType;
  final DosageUnit dosageUnit;

  const Medicine({
    required this.medicineId,
    required this.medicineName,
    required this.description,
    required this.dosage,
    required this.usage,
    required this.sideEffects,
    required this.medicineType,
    required this.dosageUnit,
  });

  factory Medicine.fromJson(Map<String, dynamic> json) {
    return Medicine(
      medicineId: json['medicineId'],
      medicineName: json['medicineName'],
      description: json['description'],
      dosage: json['dosage'],
      usage: json['usage'],
      sideEffects: json['sideEffects'],
      medicineType: MedicineType.fromJson(json['medicineType']),
      dosageUnit: DosageUnit.fromJson(json['dosageUnit']),
    );
  }

  @override
  List<Object?> get props => [medicineId, medicineName, description, dosage, usage, sideEffects, medicineType, dosageUnit];
}

class MedicineType extends Equatable {
  final String medicineTypeId;
  final String name;
  final String description;

  const MedicineType({
    required this.medicineTypeId,
    required this.name,
    required this.description,
  });

  factory MedicineType.fromJson(Map<String, dynamic> json) {
    return MedicineType(
      medicineTypeId: json['medicineTypeId'],
      name: json['name'],
      description: json['description'],
    );
  }

  @override
  List<Object?> get props => [medicineTypeId, name, description];
}

class DosageUnit extends Equatable {
  final String dosageUnitId;
  final String name;
  final String description;

  const DosageUnit({
    required this.dosageUnitId,
    required this.name,
    required this.description,
  });

  factory DosageUnit.fromJson(Map<String, dynamic> json) {
    return DosageUnit(
      dosageUnitId: json['dosageUnitId'],
      name: json['name'],
      description: json['description'],
    );
  }

  @override
  List<Object?> get props => [dosageUnitId, name, description];
}

class SelectTime extends Equatable {
  final String selectTimeId;
  final String name;
  final String startTime;
  final String endTime;
  final AssignMedicineSelectTime assignMedicineSelectTime;

  const SelectTime({
    required this.selectTimeId,
    required this.name,
    required this.startTime,
    required this.endTime,
    required this.assignMedicineSelectTime,
  });

  factory SelectTime.fromJson(Map<String, dynamic> json) {
    return SelectTime(
      selectTimeId: json['selectTimeId'],
      name: json['name'],
      startTime: json['startTime'],
      endTime: json['endTime'],
      assignMedicineSelectTime: AssignMedicineSelectTime.fromJson(json['AssignMedicineSelectTime']),
    );
  }

  @override
  List<Object?> get props => [selectTimeId, name, startTime, endTime, assignMedicineSelectTime];
}

class AssignMedicineSelectTime extends Equatable {
  final String dosage;

  const AssignMedicineSelectTime({required this.dosage});

  factory AssignMedicineSelectTime.fromJson(Map<String, dynamic> json) {
    return AssignMedicineSelectTime(
      dosage: json['dosage'],
    );
  }

  @override
  List<Object?> get props => [dosage];
}

class MedTreatmentAssignMed extends Equatable {
  final String medTreatmentAssignMedId;
  final String medicalTreatmentId;
  final String assignMedicineId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;

  const MedTreatmentAssignMed({
    required this.medTreatmentAssignMedId,
    required this.medicalTreatmentId,
    required this.assignMedicineId,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  factory MedTreatmentAssignMed.fromJson(Map<String, dynamic> json) {
    return MedTreatmentAssignMed(
      medTreatmentAssignMedId: json['medTreatmentAssignMedId'],
      medicalTreatmentId: json['medicalTreatmentId'],
      assignMedicineId: json['assignMedicineId'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      deletedAt: json['deletedAt'] != null ? DateTime.tryParse(json['deletedAt']) : null,
    );
  }

  @override
  List<Object?> get props => [medTreatmentAssignMedId, medicalTreatmentId, assignMedicineId, createdAt, updatedAt, deletedAt];
}

