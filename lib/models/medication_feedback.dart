import 'package:equatable/equatable.dart';

class MedicationFeedback extends Equatable {
  final String id;
  final String medicationId;
  final String residentId;
  final String staffId;
  final DateTime timestamp;
  final String feedback;
  final List<String>? sideEffects;
  final int? effectivenessRating;

  const MedicationFeedback({
    required this.id,
    required this.medicationId,
    required this.residentId,
    required this.staffId,
    required this.timestamp,
    required this.feedback,
    this.sideEffects,
    this.effectivenessRating,
  });

  factory MedicationFeedback.fromJson(Map<String, dynamic> json) {
    return MedicationFeedback(
      id: json['id'] ?? '',
      medicationId: json['medicationId'] ?? '',
      residentId: json['residentId'] ?? '',
      staffId: json['staffId'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
      feedback: json['feedback'] ?? '',
      sideEffects: (json['sideEffects'] as List?)?.map((e) => e.toString()).toList(),
      effectivenessRating: json['effectivenessRating'],
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'medicationId': medicationId,
    'residentId': residentId,
    'staffId': staffId,
    'timestamp': timestamp.toIso8601String(),
    'feedback': feedback,
    'sideEffects': sideEffects,
    'effectivenessRating': effectivenessRating,
  };

  MedicationFeedback copyWith({
    String? id,
    String? medicationId,
    String? residentId,
    String? staffId,
    DateTime? timestamp,
    String? feedback,
    List<String>? sideEffects,
    int? effectivenessRating,
  }) {
    return MedicationFeedback(
      id: id ?? this.id,
      medicationId: medicationId ?? this.medicationId,
      residentId: residentId ?? this.residentId,
      staffId: staffId ?? this.staffId,
      timestamp: timestamp ?? this.timestamp,
      feedback: feedback ?? this.feedback,
      sideEffects: sideEffects ?? this.sideEffects,
      effectivenessRating: effectivenessRating ?? this.effectivenessRating,
    );
  }

  @override
  List<Object?> get props => [id, medicationId, residentId, staffId, timestamp, feedback, sideEffects, effectivenessRating];
}

