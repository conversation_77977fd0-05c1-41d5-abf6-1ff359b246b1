class Balancesheet {
  final String date;
  final String addedBy;
  final int amount;
  final String category;

  Balancesheet({
    required this.date,
    required this.addedBy,
    required this.amount,
    required this.category,
  });

  factory Balancesheet.fromJson(Map<String, dynamic> json) {
    return Balancesheet(
      date: json['date'],
      addedBy: json['addedBy'],
      amount: json['amount'],
      category: json['category'],
    );
  }
}
