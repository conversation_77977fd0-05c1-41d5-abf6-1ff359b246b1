import 'package:equatable/equatable.dart';

class MedicationIntake extends Equatable {
  final String id;
  final String medicationId;
  final String residentId;
  final String staffId;
  final DateTime scheduledTime;
  final DateTime? actualTime;
  final bool taken;
  final String? notes;

  const MedicationIntake({
    required this.id,
    required this.medicationId,
    required this.residentId,
    required this.staffId,
    required this.scheduledTime,
    this.actualTime,
    required this.taken,
    this.notes,
  });

  factory MedicationIntake.fromJson(Map<String, dynamic> json) {
    return MedicationIntake(
      id: json['id'] ?? '',
      medicationId: json['medicationId'] ?? '',
      residentId: json['residentId'] ?? '',
      staffId: json['staffId'] ?? '',
      scheduledTime: DateTime.parse(json['scheduledTime']),
      actualTime: json['actualTime'] != null ? DateTime.tryParse(json['actualTime']) : null,
      taken: json['taken'] ?? false,
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'medicationId': medicationId,
    'residentId': residentId,
    'staffId': staffId,
    'scheduledTime': scheduledTime.toIso8601String(),
    'actualTime': actualTime?.toIso8601String(),
    'taken': taken,
    'notes': notes,
  };

  MedicationIntake copyWith({
    String? id,
    String? medicationId,
    String? residentId,
    String? staffId,
    DateTime? scheduledTime,
    DateTime? actualTime,
    bool? taken,
    String? notes,
  }) {
    return MedicationIntake(
      id: id ?? this.id,
      medicationId: medicationId ?? this.medicationId,
      residentId: residentId ?? this.residentId,
      staffId: staffId ?? this.staffId,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      actualTime: actualTime ?? this.actualTime,
      taken: taken ?? this.taken,
      notes: notes ?? this.notes,
    );
  }

  @override
  List<Object?> get props => [id, medicationId, residentId, staffId, scheduledTime, actualTime, taken, notes];
}

