class RecentTransaction {
  final String date;
  final String type;
  final int amount;
  final String category;

  RecentTransaction({
    required this.date,
    required this.type,
    required this.amount,
    required this.category,
  });

  factory RecentTransaction.fromJson(Map<String, dynamic> json) {
    return RecentTransaction(
      date: json['date'],
      type: json['type'],
      amount: json['amount'],
      category: json['category'],
    );
  }
}
