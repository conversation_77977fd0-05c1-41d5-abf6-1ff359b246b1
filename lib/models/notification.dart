class Notification {
  final String id;
  final String title;
  final String description;
  final String time;
  final String? image;
  final bool isRead;
  final String type; // 'alert', 'task', 'reminder', etc.

  Notification({
    required this.id,
    required this.title,
    required this.description,
    required this.time,
    this.image,
    this.isRead = false,
    required this.type,
  });

  Notification copyWith({
    String? id,
    String? title,
    String? description,
    String? time,
    String? image,
    bool? isRead,
    String? type,
  }) {
    return Notification(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      time: time ?? this.time,
      image: image ?? this.image,
      isRead: isRead ?? this.isRead,
      type: type ?? this.type,
    );
  }
}