class TicketCategory {
  final String id;
  final String name;
  final String description;

  TicketCategory({required this.id, required this.name, required this.description});

  factory TicketCategory.fromJson(Map<String, dynamic> json) {
    return TicketCategory(
      id: json['serviceRequestCategoryId'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
    );
  }
}

