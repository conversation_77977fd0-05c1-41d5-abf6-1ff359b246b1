import 'package:equatable/equatable.dart';

class CompanyInfo extends Equatable {
  final String? registrationNumber;
  final String? companyName;
  final String? companyLogoURL;
  final String? profileImageURL;
  final String? addressLine1;
  final String? addressLine2;
  final String? city;
  final String? state;
  final String? country;
  final String? postalCode;
  final String? taxIdentificationNumber;

  const CompanyInfo({
    this.registrationNumber,
    this.companyName,
    this.companyLogoURL,
    this.profileImageURL,
    this.addressLine1,
    this.addressLine2,
    this.city,
    this.state,
    this.country,
    this.postalCode,
    this.taxIdentificationNumber,
  });

  @override
  List<Object?> get props => [
        registrationNumber,
        companyName,
        companyLogoURL,
        profileImageURL,
        addressLine1,
        addressLine2,
        city,
        state,
        country,
        postalCode,
        taxIdentificationNumber,
      ];

  factory CompanyInfo.fromJson(Map<String, dynamic> json) {
    return CompanyInfo(
      registrationNumber: json['registrationNumber'],
      companyName: json['companyName'],
      companyLogoURL: json['companyLogoURL'],
      profileImageURL: json['profileImageURL'],
      addressLine1: json['addressLine1'],
      addressLine2: json['addressLine2'],
      city: json['city'],
      state: json['state'],
      country: json['country'],
      postalCode: json['postalCode'],
      taxIdentificationNumber: json['taxIdentificationNumber'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'registrationNumber': registrationNumber,
      'companyName': companyName,
      'companyLogoURL': companyLogoURL,
      'profileImageURL': profileImageURL,
      'addressLine1': addressLine1,
      'addressLine2': addressLine2,
      'city': city,
      'state': state,
      'country': country,
      'postalCode': postalCode,
      'taxIdentificationNumber': taxIdentificationNumber,
    };
  }
}

class UserModel extends Equatable {
  final String id;
  final String userCode;
  final String firstName;
  final String? middleName;
  final String lastName;
  final String emailAddress;
  final String? homeId;
  final String? unitId;
  final List<String> roles;
  final String phoneNumber;
  final String profileUrl;
  final String gender;
  final String? otherGenderName;
  final DateTime? dateOfBirth;
  final String? physicalAddress;
  final DateTime? contractExpiryDate;
  final String status;

  // Old format fields
  final String? tenantId;
  final String? tenantName;
  final String? tenantSubDomain;
  final DateTime? activeFrom;
  final DateTime? activeTill;
  final CompanyInfo? companyInfo;

  const UserModel({
    required this.id,
    this.userCode = '',
    required this.firstName,
    this.middleName,
    required this.lastName,
    required this.emailAddress,
    required this.phoneNumber,
    this.homeId,
    this.unitId,
    this.roles = const [],
    this.profileUrl = '',
    this.gender = '',
    this.otherGenderName,
    this.dateOfBirth,
    this.physicalAddress,
    this.contractExpiryDate,
    required this.status,

    // Old format fields
    this.tenantId,
    this.tenantName,
    this.tenantSubDomain,
    this.activeFrom,
    this.activeTill,
    this.companyInfo,
  });

  String get fullName => [firstName, middleName, lastName]
      .where((name) => name != null && name.isNotEmpty)
      .join(' ');

  @override
  List<Object?> get props => [
        id,
        userCode,
        firstName,
        middleName,
        lastName,
        emailAddress,
        phoneNumber,
        profileUrl,
        gender,
        homeId,
        unitId,
        roles,
        otherGenderName,
        dateOfBirth,
        physicalAddress,
        contractExpiryDate,
        status,
        tenantId,
        tenantName,
        tenantSubDomain,
        activeFrom,
        activeTill,
        companyInfo,
      ];

  factory UserModel.fromJson(Map<String, dynamic> json) {
    // Check if this is the new format (has userId field)
    if (json.containsKey('userId')) {
      return UserModel(
        id: json['userId'] ?? '',
        userCode: json['userCode'] ?? '',
        firstName: json['firstName'] ?? '',
        middleName: json['middleName'],
        lastName: json['lastName'] ?? '',
        emailAddress: json['emailAddress'] ?? '',
        phoneNumber: json['phoneNumber'] ?? '',
        profileUrl: json['profileUrl'] ?? '',
        homeId: json['homeId'],
        unitId: json['unitId'],
        roles: List<String>.from(json['roles'] ?? []),
        gender: json['gender'] ?? '',
        otherGenderName: json['othGenderName'],
        dateOfBirth: json['dateOfBirth'] != null
            ? DateTime.parse(json['dateOfBirth'])
            : null,
        physicalAddress: json['physicalAddress'],
        contractExpiryDate: json['contractExpiryDate'] != null
            ? DateTime.parse(json['contractExpiryDate'])
            : null,
        status: json['status'] ?? '',

      );
    }
    // Old format
    else {
      return UserModel(
        id: json['tenantId'] ?? '',
        userCode: json['userCode'] ?? '',
        firstName: json['tenantName']??'',
        lastName: '',
        emailAddress: json['emailAddress'] ?? '',
        phoneNumber: json['phoneNumber'] ?? '',
        status: json['status'] ?? '',
        homeId: json['homeId'],
        unitId: json['unitId'],
        roles: List<String>.from(json['roles'] ?? []),
        tenantId: json['tenantId'] ?? '',
        tenantName: json['tenantName'] ?? '',
        tenantSubDomain: json['tenantSubDomain'] ?? '',
        activeFrom: json['activeFrom'] != null
            ? DateTime.parse(json['activeFrom'])
            : null,
        activeTill: json['activeTill'] != null
            ? DateTime.parse(json['activeTill'])
            : null,
        companyInfo: json['companyInfo'] != null
            ? CompanyInfo.fromJson(json['companyInfo'])
            : null,
      );
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'userId': id,
      'firstName': firstName,
      'middleName': middleName,
      'lastName': lastName,
      'emailAddress': emailAddress,
      'phoneNumber': phoneNumber,
      'roles': roles,
      'userCode': userCode,
      'profileUrl': profileUrl,
      'gender': gender,
      'homeId': homeId,
      'unitId': unitId,
      'othGenderName': otherGenderName,
      'status': status,
    };

    // Add optional fields if they exist
    if (dateOfBirth != null) {
      data['dateOfBirth'] = dateOfBirth!.toIso8601String();
    }
    if (physicalAddress != null) {
      data['physicalAddress'] = physicalAddress;
    }
    if (contractExpiryDate != null) {
      data['contractExpiryDate'] = contractExpiryDate!.toIso8601String();
    }

    // Add old format fields if they exist
    if (tenantId != null) data['tenantId'] = tenantId;
    if (tenantName != null) data['tenantName'] = tenantName;
    if (tenantSubDomain != null) data['tenantSubDomain'] = tenantSubDomain;
    if (activeFrom != null) data['activeFrom'] = activeFrom!.toIso8601String();
    if (activeTill != null) data['activeTill'] = activeTill!.toIso8601String();
    if (companyInfo != null) data['companyInfo'] = companyInfo!.toJson();

    return data;
  }

  // Create a copy of this UserModel with the given fields replaced with new values
  UserModel copyWith({
    String? id,
    String? firstName,
    String? middleName,
    String? lastName,
    String? emailAddress,
    String? phoneNumber,
    String? profileUrl,
    String? gender,
    String ? homeId,
    String? unitId,
    List<String>? roles,
    String? otherGenderName,
    DateTime? dateOfBirth,
    String? physicalAddress,
    DateTime? contractExpiryDate,
    String? status,
    String? tenantId,
    String? tenantName,
    String? tenantSubDomain,
    DateTime? activeFrom,
    DateTime? activeTill,
    CompanyInfo? companyInfo,
  }) {
    return UserModel(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      middleName: middleName ?? this.middleName,
      lastName: lastName ?? this.lastName,
      emailAddress: emailAddress ?? this.emailAddress,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileUrl: profileUrl ?? this.profileUrl,
      gender: gender ?? this.gender,
      homeId: homeId ?? this.homeId,
      unitId: unitId ?? this.unitId,
      roles: roles ?? this.roles,
      otherGenderName: otherGenderName ?? this.otherGenderName,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      physicalAddress: physicalAddress ?? this.physicalAddress,
      contractExpiryDate: contractExpiryDate ?? this.contractExpiryDate,
      status: status ?? this.status,
      tenantId: tenantId ?? this.tenantId,
      tenantName: tenantName ?? this.tenantName,
      tenantSubDomain: tenantSubDomain ?? this.tenantSubDomain,
      activeFrom: activeFrom ?? this.activeFrom,
      activeTill: activeTill ?? this.activeTill,
      companyInfo: companyInfo ?? this.companyInfo,
    );
  }
}

class FamilyDetail {
  final String familyDetId;
  final String firstName;
  final String secondName;
  final String lastName;
  final String phoneNumber;
  final String emailAddress;
  final String address;
  final DateTime createdAt;
  final DateTime updatedAt;

  FamilyDetail({
    required this.familyDetId,
    required this.firstName,
    required this.secondName,
    required this.lastName,
    required this.phoneNumber,
    required this.emailAddress,
    required this.address,
    required this.createdAt,
    required this.updatedAt,
  });

  factory FamilyDetail.fromJson(Map<String, dynamic> json) {
    return FamilyDetail(
      familyDetId: json['familyDetId'] ?? '',
      firstName: json['firstName'] ?? '',
      secondName: json['secondName'] ?? '',
      lastName: json['lastName'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      emailAddress: json['emailAddress'] ?? '',
      address: json['address'] ?? '',
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'familyDetId': familyDetId,
      'firstName': firstName,
      'secondName': secondName,
      'lastName': lastName,
      'phoneNumber': phoneNumber,
      'emailAddress': emailAddress,
      'address': address,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}

class ResidentFinanceAttachments {
  final String? docTypeId;

  ResidentFinanceAttachments({this.docTypeId});

  factory ResidentFinanceAttachments.fromJson(Map<String, dynamic> json) {
    return ResidentFinanceAttachments(
      docTypeId: json['docTypeId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docTypeId': docTypeId,
    };
  }
}

class ResidentBudgetAttachment {
  final String attachmentId;
  final String attachmentName;
  final String attachmentURL;
  final String? description;
  final String? expiryDate;
  final ResidentFinanceAttachments residentFinanceAttachments;

  ResidentBudgetAttachment({
    required this.attachmentId,
    required this.attachmentName,
    required this.attachmentURL,
    this.description,
    this.expiryDate,
    required this.residentFinanceAttachments,
  });

  factory ResidentBudgetAttachment.fromJson(Map<String, dynamic> json) {
    return ResidentBudgetAttachment(
      attachmentId: json['attachmentId'] ?? '',
      attachmentName: json['attachmentName'] ?? '',
      attachmentURL: json['attachmentURL'] ?? '',
      description: json['description'],
      expiryDate: json['expiryDate'],
      residentFinanceAttachments: ResidentFinanceAttachments.fromJson(
        json['ResidentFinanceAttachments'] ?? {},
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'attachmentId': attachmentId,
      'attachmentName': attachmentName,
      'attachmentURL': attachmentURL,
      'description': description,
      'expiryDate': expiryDate,
      'ResidentFinanceAttachments': residentFinanceAttachments.toJson(),
    };
  }
}

class ResidentBudget {
  final String residentFinanceId;
  final String accountManager;
  final String emailAddress;
  final bool unlimited;
  final String budgetType;
  final String budgetAmount;
  final bool isReview;
  final DateTime reviewDate;
  final List<ResidentBudgetAttachment> attachments;

  ResidentBudget({
    required this.residentFinanceId,
    required this.accountManager,
    required this.emailAddress,
    required this.unlimited,
    required this.budgetType,
    required this.budgetAmount,
    required this.isReview,
    required this.reviewDate,
    required this.attachments,
  });

factory ResidentBudget.fromJson(Map<String, dynamic> json) {
  return ResidentBudget(
    residentFinanceId: json['residentFinanceId'] ?? '',
    accountManager: json['accountManager'] ?? '',
    emailAddress: json['emailAddress'] ?? '',
    unlimited: json['unlimited'] ?? false,
    budgetType: json['budgetType'] ?? '',
    budgetAmount: json['budgetAmount'] ?? '',
    isReview: json['isReview'] ?? false,
    reviewDate: json['reviewDate'] != null && json['reviewDate'].toString().isNotEmpty
        ? DateTime.parse(json['reviewDate'])
        : DateTime.fromMillisecondsSinceEpoch(0),
    attachments: (json['attachments'] as List<dynamic>?)
            ?.map((e) => ResidentBudgetAttachment.fromJson(e))
            .toList() ??
        [],
  );
}

  Map<String, dynamic> toJson() {
    return {
      'residentFinanceId': residentFinanceId,
      'accountManager': accountManager,
      'emailAddress': emailAddress,
      'unlimited': unlimited,
      'budgetType': budgetType,
      'budgetAmount': budgetAmount,
      'isReview': isReview,
      'reviewDate': reviewDate.toIso8601String(),
      'attachments': attachments.map((e) => e.toJson()).toList(),
    };
  }
}

class ServiceProviderAttachment {
  final String attachmentId;
  final String attachmentName;
  final String attachmentURL;

  ServiceProviderAttachment({
    required this.attachmentId,
    required this.attachmentName,
    required this.attachmentURL,
  });

  factory ServiceProviderAttachment.fromJson(Map<String, dynamic> json) {
    return ServiceProviderAttachment(
      attachmentId: json['attachmentId'] ?? '',
      attachmentName: json['attachmentName'] ?? '',
      attachmentURL: json['attachmentURL'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'attachmentId': attachmentId,
      'attachmentName': attachmentName,
      'attachmentURL': attachmentURL,
    };
  }
}

class ServiceProviderResident {
  final String svcProviderId;
  final String svcProviderTypeId;
  final String userId;
  final String emailAddress;
  final String phoneNumber;
  final String address;
  final String note;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<ServiceProviderAttachment> svcProviderAttachments;
  final ServiceProviderType? svcMaster;


  ServiceProviderResident({
    required this.svcProviderId,
    required this.svcProviderTypeId,
    required this.userId,
    required this.emailAddress,
    required this.phoneNumber,
    required this.address,
    required this.note,
    required this.createdAt,
    required this.updatedAt,
    required this.svcProviderAttachments,
    this.svcMaster,
  });

  factory ServiceProviderResident.fromJson(Map<String, dynamic> json) {
    return ServiceProviderResident(
      svcProviderId: json['svcProviderId'] ?? '',
      svcProviderTypeId: json['svcProviderTypeId'] ?? '',
      userId: json['userId'] ?? '',
      emailAddress: json['emailAddress'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      address: json['address'] ?? '',
      note: json['note'] ?? '',
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
        svcMaster: json['svcMaster'] != null
            ? ServiceProviderType.fromJson(json['svcMaster'])
            : null,
      svcProviderAttachments: (json['svcProviderAttachments'] as List<dynamic>? ?? [])
          .map((e) => ServiceProviderAttachment.fromJson(e))
          .toList(),

    );
  }

  Map<String, dynamic> toJson() {
    return {
      'svcProviderId': svcProviderId,
      'svcProviderTypeId': svcProviderTypeId,
      'userId': userId,
      'emailAddress': emailAddress,
      'phoneNumber': phoneNumber,
      'address': address,
      'note': note,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'svcProviderAttachments': svcProviderAttachments.map((e) => e.toJson()).toList(),
      if (svcMaster != null) 'svcMaster': svcMaster!.toJson(),
    };
  }
}

class ServiceProviderType {
  final String svcProviderTypeId;
  final String name;
  final String? description;

  ServiceProviderType({
    required this.svcProviderTypeId,
    required this.name,
    this.description,
  });

  factory ServiceProviderType.fromJson(Map<String, dynamic> json) {
    return ServiceProviderType(
      svcProviderTypeId: json['svcProviderTypeId'] ?? '',
      name: json['name'] ?? '',
      description: json['description'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'svcProviderTypeId': svcProviderTypeId,
      'name': name,
      'description': description,
    };
  }
}