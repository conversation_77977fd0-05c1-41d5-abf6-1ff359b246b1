class Unit {
  final String id;
  final String homeId;
  final String name;
  final String maxResidentCount;
  final String unitColorId;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  Unit({
    required this.id,
    required this.homeId,
    required this.name,
    required this.maxResidentCount,
    required this.unitColorId,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Unit.fromJson(Map<String, dynamic> json) {
    return Unit(
      id: json['unitId'] ?? '',
      homeId: json['homeId'] ?? '',
      name: json['name'] ?? '',
      maxResidentCount: json['maxResidentCount'] ?? '',
      unitColorId: json['unitColorId'] ?? '',
      isActive: json['isActive'] ?? true,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'unitId': id,
      'homeId': homeId,
      'name': name,
      'maxResidentCount': maxResidentCount,
      'unitColorId': unitColorId,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}
