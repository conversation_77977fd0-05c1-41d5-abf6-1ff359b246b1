import 'package:equatable/equatable.dart';

enum RequestStatus { pending, inProgress, completed, cancelled }
enum RequestPriority { low, medium, high, urgent }
enum RequestType { maintenance, housekeeping, dietary, medical, other }

class Request extends Equatable {
  final String id;
  final String residentId;
  final String title;
  final String description;
  final RequestType type;
  final RequestPriority priority;
  final RequestStatus status;
  final DateTime createdAt;
  final String createdBy;
  final String? assignedTo;
  final DateTime? completedAt;
  final String? completedBy;
  final List<String> attachments;
  final List<RequestComment> comments;

  const Request({
    required this.id,
    required this.residentId,
    required this.title,
    required this.description,
    required this.type,
    required this.priority,
    this.status = RequestStatus.pending,
    required this.createdAt,
    required this.createdBy,
    this.assignedTo,
    this.completedAt,
    this.completedBy,
    this.attachments = const [],
    this.comments = const [],
  });

  Request copyWith({
    String? id,
    String? residentId,
    String? title,
    String? description,
    RequestType? type,
    RequestPriority? priority,
    RequestStatus? status,
    DateTime? createdAt,
    String? createdBy,
    String? assignedTo,
    DateTime? completedAt,
    String? completedBy,
    List<String>? attachments,
    List<RequestComment>? comments,
  }) {
    return Request(
      id: id ?? this.id,
      residentId: residentId ?? this.residentId,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      assignedTo: assignedTo ?? this.assignedTo,
      completedAt: completedAt ?? this.completedAt,
      completedBy: completedBy ?? this.completedBy,
      attachments: attachments ?? this.attachments,
      comments: comments ?? this.comments,
    );
  }

  factory Request.fromJson(Map<String, dynamic> json) {
    return Request(
      id: json['id'] ?? '',
      residentId: json['residentId'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      type: _parseRequestType(json['type']),
      priority: _parseRequestPriority(json['priority']),
      status: _parseRequestStatus(json['status']),
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      createdBy: json['createdBy'] ?? '',
      assignedTo: json['assignedTo'],
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'])
          : null,
      completedBy: json['completedBy'],
      attachments: json['attachments'] != null
          ? List<String>.from(json['attachments'])
          : [],
      comments: json['comments'] != null
          ? (json['comments'] as List)
              .map((comment) => RequestComment.fromJson(comment))
              .toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'residentId': residentId,
      'title': title,
      'description': description,
      'type': type.toString().split('.').last,
      'priority': priority.toString().split('.').last,
      'status': status.toString().split('.').last,
      'createdAt': createdAt.toIso8601String(),
      'createdBy': createdBy,
      'assignedTo': assignedTo,
      'completedAt': completedAt?.toIso8601String(),
      'completedBy': completedBy,
      'attachments': attachments,
      'comments': comments.map((comment) => comment.toJson()).toList(),
    };
  }

  static RequestType _parseRequestType(dynamic value) {
    if (value == null) return RequestType.other;
    
    if (value is RequestType) return value;
    
    final String typeStr = value.toString().toLowerCase();
    
    if (typeStr.contains('maintenance')) return RequestType.maintenance;
    if (typeStr.contains('housekeeping')) return RequestType.housekeeping;
    if (typeStr.contains('dietary')) return RequestType.dietary;
    if (typeStr.contains('medical')) return RequestType.medical;
    
    return RequestType.other;
  }

  static RequestPriority _parseRequestPriority(dynamic value) {
    if (value == null) return RequestPriority.medium;
    
    if (value is RequestPriority) return value;
    
    final String priorityStr = value.toString().toLowerCase();
    
    if (priorityStr.contains('low')) return RequestPriority.low;
    if (priorityStr.contains('medium')) return RequestPriority.medium;
    if (priorityStr.contains('high')) return RequestPriority.high;
    if (priorityStr.contains('urgent')) return RequestPriority.urgent;
    
    return RequestPriority.medium;
  }

  static RequestStatus _parseRequestStatus(dynamic value) {
    if (value == null) return RequestStatus.pending;
    
    if (value is RequestStatus) return value;
    
    final String statusStr = value.toString().toLowerCase();
    
    if (statusStr.contains('pending')) return RequestStatus.pending;
    if (statusStr.contains('progress')) return RequestStatus.inProgress;
    if (statusStr.contains('completed')) return RequestStatus.completed;
    if (statusStr.contains('cancelled')) return RequestStatus.cancelled;
    
    return RequestStatus.pending;
  }

  @override
  List<Object?> get props => [
        id,
        residentId,
        title,
        description,
        type,
        priority,
        status,
        createdAt,
        createdBy,
        assignedTo,
        completedAt,
        completedBy,
        attachments,
        comments,
      ];
}

class RequestComment extends Equatable {
  final String id;
  final String content;
  final String authorId;
  final String authorName;
  final DateTime createdAt;

  const RequestComment({
    required this.id,
    required this.content,
    required this.authorId,
    required this.authorName,
    required this.createdAt,
  });

  RequestComment copyWith({
    String? id,
    String? content,
    String? authorId,
    String? authorName,
    DateTime? createdAt,
  }) {
    return RequestComment(
      id: id ?? this.id,
      content: content ?? this.content,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  factory RequestComment.fromJson(Map<String, dynamic> json) {
    return RequestComment(
      id: json['id'] ?? '',
      content: json['content'] ?? '',
      authorId: json['authorId'] ?? '',
      authorName: json['authorName'] ?? '',
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'authorId': authorId,
      'authorName': authorName,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [id, content, authorId, authorName, createdAt];
}