class MarSheetResponse {
  final List<Medication> data;

  MarSheetResponse({required this.data});

  factory MarSheetResponse.fromJson(Map<String, dynamic> json) {
    return MarSheetResponse(
      data: (json['data'] as List)
          .map((e) => Medication.fromJson(e))
          .toList(),
    );
  }
}

class Medication {
  final String medicalTreatmentName;
  final String assignMedicineId;
  final String medicineId;
  final String medicineName;
  final String dosage;
  final String unit;
  final bool isSequential;
  final DateTime startDate;
  final DateTime endDate;
  final bool isEnded;
  final List<AssignMedicineSelectTime> assignMedicineSelectTime;
  final List<dynamic> customDates;
  final List<MarSheet> marSheets;

  Medication({
    required this.medicalTreatmentName,
    required this.assignMedicineId,
    required this.medicineId,
    required this.medicineName,
    required this.dosage,
    required this.unit,
    required this.isSequential,
    required this.startDate,
    required this.endDate,
    required this.isEnded,
    required this.assignMedicineSelectTime,
    required this.customDates,
    required this.marSheets,
  });

  factory Medication.fromJson(Map<String, dynamic> json) {
    return Medication(
      medicalTreatmentName: json['medicalTreatmentName'],
      assignMedicineId: json['assignMedicineId'],
      medicineId: json['medicineId'],
      medicineName: json['medicineName'],
      dosage: json['dosage'],
      unit: json['unit'],
      isSequential: json['isSequential'],
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      isEnded: json['isEnded'],
      assignMedicineSelectTime: (json['AssignMedicineSelectTime'] as List)
          .map((e) => AssignMedicineSelectTime.fromJson(e))
          .toList(),
      customDates: json['customDates'] ?? [],
      marSheets: (json['marSheets'] as List)
          .map((e) => MarSheet.fromJson(e))
          .toList(),
    );
  }
}

class AssignMedicineSelectTime {
  final String selectTimeId;
  final String name;
  final String startTime;
  final String endTime;
  final int dosage;

  AssignMedicineSelectTime({
    required this.selectTimeId,
    required this.name,
    required this.startTime,
    required this.endTime,
    required this.dosage,
  });

  factory AssignMedicineSelectTime.fromJson(Map<String, dynamic> json) {
    return AssignMedicineSelectTime(
      selectTimeId: json['selectTimeId'],
      name: json['name'],
      startTime: json['startTime'],
      endTime: json['endTime'],
      dosage: json['dosage'],
    );
  }
}

class MarSheet {
  final String marSheetId;
  final DateTime dateOfInTake;
  final String timeOfInTake;
  final String amountTaken;
  final String note;
  final String marSheetStatusId;
  final MarSheetStatus marSheetStatus;
  final SelectTime? selectTime;
  final dynamic staff;
  final bool isEnded;

  MarSheet({
    required this.marSheetId,
    required this.dateOfInTake,
    required this.timeOfInTake,
    required this.amountTaken,
    required this.note,
    required this.marSheetStatusId,
    required this.marSheetStatus,
    required this.selectTime,
    required this.staff,
    required this.isEnded,
  });

  factory MarSheet.fromJson(Map<String, dynamic> json) {
    return MarSheet(
      marSheetId: json['marSheetId'],
      dateOfInTake: DateTime.parse(json['dateOfInTake']),
      timeOfInTake: json['timeOfInTake'],
      amountTaken: json['amountTaken'],
      note: json['note'],
      marSheetStatusId: json['marSheetStatusId'],
      marSheetStatus: MarSheetStatus.fromJson(json['marSheetStatus']),
      selectTime: json['selectTime'] != null
          ? SelectTime.fromJson(json['selectTime'])
          : null,
      staff: json['staff'],
      isEnded: json['isEnded'],
    );
  }
}

class MarSheetStatus {
  final String marSheetStatusId;
  final String symbol;
  final String name;

  MarSheetStatus({
    required this.marSheetStatusId,
    required this.symbol,
    required this.name,
  });

  factory MarSheetStatus.fromJson(Map<String, dynamic> json) {
    return MarSheetStatus(
      marSheetStatusId: json['marSheetStatusId'],
      symbol: json['symbol'],
      name: json['name'],
    );
  }
}

class SelectTime {
  final String selectTimeId;
  final String name;
  final String startTime;
  final String endTime;

  SelectTime({
    required this.selectTimeId,
    required this.name,
    required this.startTime,
    required this.endTime,
  });

  factory SelectTime.fromJson(Map<String, dynamic> json) {
    return SelectTime(
      selectTimeId: json['selectTimeId'],
      name: json['name'],
      startTime: json['startTime'],
      endTime: json['endTime'],
    );
  }
}

