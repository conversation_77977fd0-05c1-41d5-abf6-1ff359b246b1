import 'package:equatable/equatable.dart';

enum ActivityResponse { positive, neutral, negative, refused }

class Activity extends Equatable {
  final String id;
  final String residentId;
  final String staffId;
  final String category;
  final String activityName;
  final ActivityResponse response;
  final DateTime timestamp;
  final String? notes;
  final Map<String, dynamic>? additionalData;

  const Activity({
    required this.id,
    required this.residentId,
    required this.staffId,
    required this.category,
    required this.activityName,
    required this.response,
    required this.timestamp,
    this.notes,
    this.additionalData,
  });

  @override
  List<Object?> get props => [
    id, 
    residentId, 
    staffId, 
    category, 
    activityName, 
    response, 
    timestamp, 
    notes, 
    additionalData
  ];

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'residentId': residentId,
      'staffId': staffId,
      'category': category,
      'activityName': activityName,
      'response': response.toString().split('.').last,
      'timestamp': timestamp.toIso8601String(),
      'notes': notes,
      'additionalData': additionalData,
    };
  }

  factory Activity.fromJson(Map<String, dynamic> json) {
    return Activity(
      id: json['id'],
      residentId: json['residentId'],
      staffId: json['staffId'],
      category: json['category'],
      activityName: json['activityName'],
      response: ActivityResponse.values.firstWhere(
        (e) => e.toString().split('.').last == json['response'],
        orElse: () => ActivityResponse.neutral,
      ),
      timestamp: DateTime.parse(json['timestamp']),
      notes: json['notes'],
      additionalData: json['additionalData'],
    );
  }

  Activity copyWith({
    String? id,
    String? residentId,
    String? staffId,
    String? category,
    String? activityName,
    ActivityResponse? response,
    DateTime? timestamp,
    String? notes,
    Map<String, dynamic>? additionalData,
  }) {
    return Activity(
      id: id ?? this.id,
      residentId: residentId ?? this.residentId,
      staffId: staffId ?? this.staffId,
      category: category ?? this.category,
      activityName: activityName ?? this.activityName,
      response: response ?? this.response,
      timestamp: timestamp ?? this.timestamp,
      notes: notes ?? this.notes,
      additionalData: additionalData ?? this.additionalData,
    );
  }
}

class ResidentCareActivity {
  String residentId;
  final String activityName;
  final String mood;
  final List<ActivityAttachment> attachments;
  final String subActivityA;
  final String subActivityAValue;
  final String subActivityB;
  final String subActivityBValue;
  final String temperature;
  final String notes;
  final String handOffStaffId;

  ResidentCareActivity({
    required this.residentId,
    required this.activityName,
    required this.mood,
    required this.attachments,
    required this.subActivityA,
    required this.subActivityAValue,
    required this.subActivityB,
    required this.subActivityBValue,
    required this.temperature,
    required this.notes,
    required this.handOffStaffId,
  });

  Map<String, dynamic> toJson() => {
    'residentId': residentId,
    'activityName': activityName,
    'mood': mood,
    'attachments': attachments.map((a) => a.toJson()).toList(),
    'subActivityA': subActivityA,
    'subActivityAValue': subActivityAValue,
    'subActivityB': subActivityB,
    'subActivityBValue': subActivityBValue,
    'temperature': temperature,
    'notes': notes,
    'handOffStaffId': handOffStaffId,
  };
}

class ActivityAttachment {
  final String fileName;
  final String filePath;

  ActivityAttachment({required this.fileName, required this.filePath});

  Map<String, dynamic> toJson() => {
    'fileName': fileName,
    'filePath': filePath,
  };
}
