import 'package:flutter/material.dart';

// General Constants
const String appName = 'Ride Sharing';
const String baseUrl = 'http://localhost:8080';
const double defaultPadding = 16.0;

// Color Constants
const Color primaryColor = Color(0xFF2D9CDB);
const Color secondaryColor = Color(0xFFFF6F61);
const Color backgroundColor = Color(0xFFF5F5F5);
const Color textColor = Color(0xFF333333);

// Icon Sizes
const double iconSizeSmall = 20.0;
const double iconSizeMedium = 30.0; // <-- This is the missing constant
const double iconSizeLarge = 40.0;

// Security & Privacy Constants
const String privacyPolicyUrl = 'https://www.example.com/privacy-policy';
const String termsAndConditionsUrl =
    'https://www.example.com/terms-and-conditions';

// Font Sizes
const double headingFontSize = 24.0;
const double subheadingFontSize = 20.0;
const double bodyFontSize = 16.0;
const double captionFontSize = 12.0;

// Image Paths
const String logoPath = 'assets/images/logo.png';
const String carImagePath = 'assets/images/car.png';
const String bikeImagePath = 'assets/images/bike.png';

// Other Constants
const Duration apiTimeout = Duration(seconds: 30);
