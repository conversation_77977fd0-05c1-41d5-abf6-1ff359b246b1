import 'package:flutter/material.dart';

// Show a SnackBar for notifications
void showSnackBar(BuildContext context, String message,
    {Color backgroundColor = Colors.black}) {
  final snackBar = SnackBar(
    content: Text(message),
    backgroundColor: backgroundColor,
    behavior: SnackBarBehavior.floating,
  );
  ScaffoldMessenger.of(context).showSnackBar(snackBar);
}

// Validate email format
bool isValidEmail(String email) {
  final RegExp emailRegExp = RegExp(
    r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9]+\.[a-zA-Z]+",
  );
  return emailRegExp.hasMatch(email);
}

// Format DateTime to readable format
String formatDateTime(DateTime dateTime) {
  return "${dateTime.day}-${dateTime.month}-${dateTime.year} ${dateTime.hour}:${dateTime.minute}";
}

void navigatePath(BuildContext context, String path) {
  Navigator.pushNamed(context, path);
}

// Navigate to a new screen
void navigateTo(
  BuildContext context,
  Widget screen,
) {
  Navigator.push(
    context,
    MaterialPageRoute(builder: (context) => screen),
  );
}

// Close the current screen and return a value
void closeScreen(BuildContext context, {dynamic result}) {
  Navigator.pop(context, result);
}
