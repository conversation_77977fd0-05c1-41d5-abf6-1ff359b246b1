import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

class SyncedScrollBehavior extends MaterialScrollBehavior {
  @override
  Widget buildScrollbar(
    BuildContext context, 
    Widget child, 
    ScrollableDetails details
  ) {
    // Customize scrollbar appearance if needed
    return super.buildScrollbar(context, child, details);
  }
  
  @override
  Widget buildOverscrollIndicator(
    BuildContext context, 
    Widget child, 
    ScrollableDetails details
  ) {
    // Use ClampingScrollPhysics to prevent overscroll glow
    return child;
  }
  
  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    // Use custom physics for smoother synchronized scrolling
    return const ClampingScrollPhysics();
  }
  
  @override
  MultitouchDragStrategy getMultitouchDragStrategy(BuildContext context) {
    // Use sumAllPointers for better multi-touch handling
    return MultitouchDragStrategy.sumAllPointers;
  }
}