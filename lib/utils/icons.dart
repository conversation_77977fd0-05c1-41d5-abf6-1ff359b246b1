// Icons
const userIcon =
    '''<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.8331 14.6608C14.6271 14.9179 14.3055 15.0713 13.9729 15.0713H2.02715C1.69446 15.0713 1.37287 14.9179 1.16692 14.6608C0.972859 14.4191 0.906322 14.1271 0.978404 13.8382C1.77605 10.6749 4.66327 8.46512 8.0004 8.46512C11.3367 8.46512 14.2239 10.6749 15.0216 13.8382C15.0937 14.1271 15.0271 14.4191 14.8331 14.6608ZM4.62208 4.23295C4.62208 2.41197 6.13737 0.929467 8.0004 0.929467C9.86263 0.929467 11.3779 2.41197 11.3779 4.23295C11.3779 6.0547 9.86263 7.53565 8.0004 7.53565C6.13737 7.53565 4.62208 6.0547 4.62208 4.23295ZM15.9444 13.6159C15.2283 10.7748 13.0231 8.61461 10.2571 7.84315C11.4983 7.09803 12.3284 5.75882 12.3284 4.23295C12.3284 1.89921 10.387 0 8.0004 0C5.613 0 3.67155 1.89921 3.67155 4.23295C3.67155 5.75882 4.50168 7.09803 5.7429 7.84315C2.97688 8.61461 0.771665 10.7748 0.0556038 13.6159C-0.0861827 14.179 0.0460985 14.7692 0.419179 15.2332C0.808894 15.7212 1.39584 16 2.02715 16H13.9729C14.6042 16 15.1911 15.7212 15.5808 15.2332C15.9539 14.7692 16.0862 14.179 15.9444 13.6159Z" fill="#626262"/>
</svg>
''';

const phoneIcon =
    '''<svg width="11" height="18" viewBox="0 0 11 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.33333 15.0893C6.33333 15.5588 5.96 15.9384 5.5 15.9384C5.04 15.9384 4.66667 15.5588 4.66667 15.0893C4.66667 14.6197 5.04 14.2402 5.5 14.2402C5.96 14.2402 6.33333 14.6197 6.33333 15.0893ZM6.83333 2.63135C6.83333 2.91325 6.61 3.14081 6.33333 3.14081H4.66667C4.39 3.14081 4.16667 2.91325 4.16667 2.63135C4.16667 2.34945 4.39 2.12274 4.66667 2.12274H6.33333C6.61 2.12274 6.83333 2.34945 6.83333 2.63135ZM10 15.7923C10 16.4479 9.47667 16.9819 8.83333 16.9819H2.16667C1.52333 16.9819 1 16.4479 1 15.7923V2.2068C1 1.55215 1.52333 1.01807 2.16667 1.01807H8.83333C9.47667 1.01807 10 1.55215 10 2.2068V15.7923ZM8.83333 0H2.16667C0.971667 0 0 0.990047 0 2.2068V15.7923C0 17.01 0.971667 18 2.16667 18H8.83333C10.0283 18 11 17.01 11 15.7923V2.2068C11 0.990047 10.0283 0 8.83333 0Z" fill="#626262"/>
</svg>''';

const locationPointIcon =
    '''<svg width="15" height="18" viewBox="0 0 15 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.5 9.3384C6.38263 9.3384 5.47303 8.42383 5.47303 7.30037C5.47303 6.17691 6.38263 5.26235 7.5 5.26235C8.61737 5.26235 9.52697 6.17691 9.52697 7.30037C9.52697 8.42383 8.61737 9.3384 7.5 9.3384ZM7.5 4.24334C5.82437 4.24334 4.45955 5.61476 4.45955 7.30037C4.45955 8.98599 5.82437 10.3574 7.5 10.3574C9.17563 10.3574 10.5405 8.98599 10.5405 7.30037C10.5405 5.61476 9.17563 4.24334 7.5 4.24334ZM12.0894 12.1551L7.5 16.7695L2.9106 12.1551C0.380268 9.61098 0.380268 5.47125 2.9106 2.92711C4.17577 1.6542 5.83704 1.01816 7.5 1.01816C9.16212 1.01816 10.8242 1.65505 12.0894 2.92711C14.6197 5.47125 14.6197 9.61098 12.0894 12.1551ZM12.8064 2.20616C9.88 -0.735387 5.12 -0.735387 2.19356 2.20616C-0.731187 5.14771 -0.731187 9.93452 2.19356 12.8761L7.1419 17.8505C7.24072 17.9507 7.37078 18 7.5 18C7.62922 18 7.75928 17.9507 7.8581 17.8505L12.8064 12.8761C15.7312 9.93452 15.7312 5.14771 12.8064 2.20616Z" fill="#626262"/>
</svg>''';

// Icons
const mailIcon =
    '''<svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M15.3576 3.39368C15.5215 3.62375 15.4697 3.94447 15.2404 4.10954L9.80876 8.03862C9.57272 8.21053 9.29421 8.29605 9.01656 8.29605C8.7406 8.29605 8.4638 8.21138 8.22775 8.04204L2.76041 4.11039C2.53201 3.94618 2.47851 3.62546 2.64154 3.39454C2.80542 3.16362 3.12383 3.10974 3.35223 3.27566L8.81872 7.20645C8.93674 7.29112 9.09552 7.29197 9.2144 7.20559L14.6469 3.27651C14.8753 3.10974 15.1937 3.16447 15.3576 3.39368ZM16.9819 10.7763C16.9819 11.4366 16.4479 11.9745 15.7932 11.9745H2.20765C1.55215 11.9745 1.01892 11.4366 1.01892 10.7763V2.22368C1.01892 1.56342 1.55215 1.02632 2.20765 1.02632H15.7932C16.4479 1.02632 16.9819 1.56342 16.9819 2.22368V10.7763ZM15.7932 0H2.20765C0.990047 0 0 0.998092 0 2.22368V10.7763C0 12.0028 0.990047 13 2.20765 13H15.7932C17.01 13 18 12.0028 18 10.7763V2.22368C18 0.998092 17.01 0 15.7932 0Z" fill="#757575"/>
</svg>''';

const lockIcon =
    '''<svg width="15" height="18" viewBox="0 0 15 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.24419 11.5472C9.24419 12.4845 8.46279 13.2453 7.5 13.2453C6.53721 13.2453 5.75581 12.4845 5.75581 11.5472C5.75581 10.6098 6.53721 9.84906 7.5 9.84906C8.46279 9.84906 9.24419 10.6098 9.24419 11.5472ZM13.9535 14.0943C13.9535 15.6863 12.6235 16.9811 10.9884 16.9811H4.01163C2.37645 16.9811 1.04651 15.6863 1.04651 14.0943V9C1.04651 7.40802 2.37645 6.11321 4.01163 6.11321H10.9884C12.6235 6.11321 13.9535 7.40802 13.9535 9V14.0943ZM4.53488 3.90566C4.53488 2.31368 5.86483 1.01887 7.5 1.01887C8.28488 1.01887 9.03139 1.31943 9.59477 1.86028C10.1564 2.41387 10.4651 3.14066 10.4651 3.90566V5.09434H4.53488V3.90566ZM11.5116 5.12745V3.90566C11.5116 2.87151 11.0956 1.89085 10.3352 1.14028C9.5686 0.405 8.56221 0 7.5 0C5.2875 0 3.48837 1.7516 3.48837 3.90566V5.12745C1.52267 5.37792 0 7.01915 0 9V14.0943C0 16.2484 1.79913 18 4.01163 18H10.9884C13.2 18 15 16.2484 15 14.0943V9C15 7.01915 13.4773 5.37792 11.5116 5.12745Z" fill="#757575"/>
</svg>''';

const googleIcon =
    '''<svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15.9988 8.3441C15.9988 7.67295 15.9443 7.18319 15.8265 6.67529H8.1626V9.70453H12.6611C12.5705 10.4573 12.0807 11.5911 10.9923 12.3529L10.9771 12.4543L13.4002 14.3315L13.5681 14.3482C15.1099 12.9243 15.9988 10.8292 15.9988 8.3441Z" fill="#4285F4"/>
<path d="M8.16265 16.3254C10.3666 16.3254 12.2168 15.5998 13.5682 14.3482L10.9924 12.3528C10.3031 12.8335 9.37796 13.1691 8.16265 13.1691C6.00408 13.1691 4.17202 11.7452 3.51894 9.7771L3.42321 9.78523L0.903556 11.7352L0.870605 11.8268C2.2129 14.4933 4.9701 16.3254 8.16265 16.3254Z" fill="#34A853"/>
<path d="M3.519 9.77716C3.34668 9.26927 3.24695 8.72505 3.24695 8.16275C3.24695 7.6004 3.34668 7.05624 3.50994 6.54834L3.50537 6.44017L0.954141 4.45886L0.870669 4.49857C0.317442 5.60508 0 6.84765 0 8.16275C0 9.47785 0.317442 10.7204 0.870669 11.8269L3.519 9.77716Z" fill="#FBBC05"/>
<path d="M8.16265 3.15623C9.69541 3.15623 10.7293 3.81831 11.3189 4.3716L13.6226 2.12231C12.2077 0.807206 10.3666 0 8.16265 0C4.9701 0 2.2129 1.83206 0.870605 4.49853L3.50987 6.54831C4.17202 4.58019 6.00408 3.15623 8.16265 3.15623Z" fill="#EB4335"/>
</svg>''';

const facebookIcon =
    '''<svg width="8" height="15" viewBox="0 0 8 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5.02224 14.8963V8.10133H7.30305L7.64452 5.45323H5.02224V3.7625C5.02224 2.99583 5.23517 2.4733 6.33467 2.4733L7.73695 2.47265V0.104232C7.49432 0.0720777 6.66197 0 5.6936 0C3.67183 0 2.28768 1.23402 2.28768 3.50037V5.4533H0.000976562V8.1014H2.28761V14.8963L5.02224 14.8963Z" fill="#3C5A9A"/>
</svg>''';

const twitterIcon =
    '''<svg width="16" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15.9821 1.5867C15.3952 1.84714 14.7648 2.0232 14.102 2.10234C14.7785 1.69727 15.2967 1.05538 15.5414 0.291424C14.8988 0.673076 14.1955 0.941758 13.4622 1.0858C12.8654 0.449657 12.0143 0.0524902 11.0728 0.0524902C9.26556 0.0524902 7.79989 1.51757 7.79989 3.32586C7.79989 3.58208 7.82875 3.83204 7.88423 4.07203C5.16367 3.9353 2.75173 2.63213 1.13729 0.651959C0.855385 1.13557 0.694025 1.69786 0.694025 2.29779C0.694025 3.43331 1.27199 4.43519 2.15019 5.02206C1.63031 5.00595 1.12184 4.86563 0.66728 4.61281V4.65418C0.66728 6.24031 1.79545 7.56287 3.29302 7.86367C3.01792 7.93904 2.72921 7.97842 2.43053 7.97842C2.21936 7.97842 2.01448 7.95844 1.81433 7.92079C2.23089 9.22084 3.4398 10.1671 4.8718 10.1939C3.75149 11.0721 2.33986 11.5956 0.806669 11.5956C0.542595 11.5956 0.281606 11.5798 0.0253906 11.549C1.47425 12.478 3.19453 13.0203 5.04317 13.0203C11.0639 13.0203 14.3567 8.03242 14.3567 3.70685C14.3567 3.56484 14.3535 3.42389 14.3467 3.28344C14.9882 2.81942 15.542 2.24487 15.9821 1.5867Z" fill="#2DAAE1"/>
</svg>''';

const cameraIcon =
    '''<svg width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M10 12.0152C8.49151 12.0152 7.26415 10.8137 7.26415 9.33902C7.26415 7.86342 8.49151 6.6619 10 6.6619C11.5085 6.6619 12.7358 7.86342 12.7358 9.33902C12.7358 10.8137 11.5085 12.0152 10 12.0152ZM10 5.55543C7.86698 5.55543 6.13208 7.25251 6.13208 9.33902C6.13208 11.4246 7.86698 13.1217 10 13.1217C12.133 13.1217 13.8679 11.4246 13.8679 9.33902C13.8679 7.25251 12.133 5.55543 10 5.55543ZM18.8679 13.3967C18.8679 14.2226 18.1811 14.8935 17.3368 14.8935H2.66321C1.81887 14.8935 1.13208 14.2226 1.13208 13.3967V5.42346C1.13208 4.59845 1.81887 3.92664 2.66321 3.92664H4.75C5.42453 3.92664 6.03396 3.50952 6.26604 2.88753L6.81321 1.41746C6.88113 1.23198 7.06415 1.10739 7.26604 1.10739H12.734C12.9358 1.10739 13.1189 1.23198 13.1877 1.41839L13.734 2.88845C13.966 3.50952 14.5755 3.92664 15.25 3.92664H17.3368C18.1811 3.92664 18.8679 4.59845 18.8679 5.42346V13.3967ZM17.3368 2.82016H15.25C15.0491 2.82016 14.867 2.69466 14.7972 2.50917L14.2519 1.04003C14.0217 0.418041 13.4113 0 12.734 0H7.26604C6.58868 0 5.9783 0.418041 5.74906 1.0391L5.20283 2.50825C5.13302 2.69466 4.95094 2.82016 4.75 2.82016H2.66321C1.19434 2.82016 0 3.98846 0 5.42346V13.3967C0 14.8326 1.19434 16 2.66321 16H17.3368C18.8057 16 20 14.8326 20 13.3967V5.42346C20 3.98846 18.8057 2.82016 17.3368 2.82016Z" fill="#757575"/>
</svg>
''';

const noNotificationIllistration = '''
<svg width="1080" height="1080" viewBox="0 0 1080 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M823.7 629.49C845.367 628.317 867.13 627.02 888.99 625.6" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M270.59 630.37C275.37 605.51 297.8 590.76 329.32 590.76C330.653 590.76 331.957 590.787 333.23 590.84L328.48 633.63L270.59 630.37Z" fill="#ABABAB"/>
<path d="M181 625.6C311 635.293 445.63 639.263 584.89 637.51" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M827.45 706.11C848.26 706.11 868.82 706.11 888.98 706.03" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M181 736.4C229.46 708.72 393.36 704.65 578.41 704.9" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M373 707.89C338.16 723.643 315.627 739.393 305.4 755.14" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M404.2 707.61C369.93 720.47 343.56 737.61 339.2 767.11" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M552.71 538.26C562.413 534.954 570.946 528.899 577.27 520.832C583.595 512.764 587.438 503.033 588.333 492.822C589.227 482.61 587.135 472.359 582.31 463.315C577.484 454.271 570.135 446.824 561.155 441.881C552.175 436.938 541.951 434.711 531.729 435.472C521.507 436.233 511.726 439.949 503.577 446.167C495.427 452.385 489.261 460.838 485.828 470.496C482.395 480.155 481.843 490.603 484.24 500.57C512.9 509.66 537.72 521 552.71 538.26Z" fill="#ABABAB"/>
<path d="M328.48 634C332.57 541.4 372.72 478.56 475.88 499C554.71 517.43 584.88 562 584.88 637.86" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M460.08 495.79C456.544 480.027 457.667 463.577 463.312 448.441C468.958 433.305 478.882 420.137 491.876 410.54C504.871 400.943 520.375 395.331 536.502 394.387C552.629 393.443 568.682 397.208 582.707 405.224C596.733 413.24 608.125 425.16 615.498 439.534C622.87 453.908 625.905 470.115 624.232 486.182C622.559 502.25 616.25 517.484 606.075 530.031C595.899 542.578 582.296 551.896 566.92 556.85" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M215.23 628C218.555 615.278 224.533 603.403 232.771 593.153C241.009 582.904 251.321 574.513 263.03 568.53C274.739 562.546 287.582 559.107 300.714 558.436C313.846 557.766 326.972 559.88 339.23 564.64" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M616 537.66C738.34 483.2 826.26 519.61 840.47 637.75C846.47 685.67 845.04 728.28 840.47 774.75C835.25 798.4 777 795.62 767.78 774.75C762.46 768.88 762.18 709.75 767.78 686.69C768.33 684.42 764.85 683.21 763.06 684.81C746.88 699.31 701.53 699.51 683.25 683.46C682.858 683.116 682.377 682.888 681.863 682.803C681.348 682.717 680.82 682.777 680.337 682.975C679.855 683.174 679.438 683.503 679.132 683.927C678.827 684.35 678.646 684.85 678.61 685.37C682.89 716.67 683.61 746.86 677.03 774.76C669.52 804.01 605.03 801.12 601.94 774.76C595.15 741.76 603.79 679.21 601.94 637.94C595.46 597.83 600.63 564.6 616 537.66Z" fill="#D3D3D3"/>
<path d="M599 537.66C721.34 483.2 809.26 519.61 823.47 637.75C829.47 685.67 828.04 728.28 823.47 774.75C818.25 798.4 760 795.62 750.78 774.75C745.46 768.88 745.18 709.75 750.78 686.69C751.33 684.42 747.85 683.21 746.06 684.81C729.88 699.31 684.53 699.51 666.25 683.46C665.858 683.116 665.377 682.888 664.863 682.803C664.348 682.717 663.82 682.777 663.337 682.975C662.855 683.174 662.437 683.503 662.132 683.927C661.827 684.35 661.646 684.85 661.61 685.37C665.89 716.67 666.61 746.86 660.03 774.76C652.52 804.01 588.03 801.12 584.94 774.76C574.55 738.31 577.08 673.19 584.94 637.94" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M503.87 705.89C497.73 727.08 497.3 756.44 503.87 771.06C511.68 796.32 572.36 795.63 583.87 771.06" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M688.66 706.31C682.52 728.31 683.26 752.64 688.66 773.8C694.72 794.11 741.72 788.28 749.3 772.33" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M494.25 553.29C490.917 567.337 481.25 574.313 465.25 574.22" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M409.34 606.22C403.64 619.48 392.897 624.667 377.11 621.78" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M450.67 636.78L436.12 615.65C431.86 610.74 433.74 602.07 440.31 596.37C446.87 590.67 455.72 590.02 459.98 594.93L487.46 620.55C491.02 624.65 486.86 631.62 481.57 637.13" fill="#0E0E0E"/>
<path d="M516.31 631.74L508.95 637.06" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M239.38 657.85H377.11" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M353.02 671.04H471.27" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M488.79 657.85H520.41" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M688.105 445.974C688.581 445.974 688.947 446.102 689.204 446.358C689.46 446.615 689.588 446.944 689.588 447.347C689.588 447.75 689.442 448.079 689.149 448.336C688.892 448.592 688.545 448.72 688.105 448.72H668.554C668.005 448.72 667.565 448.574 667.236 448.281C666.943 447.988 666.796 447.622 666.796 447.182C666.796 446.67 666.979 446.212 667.346 445.809L684.59 424.775H668.224C667.785 424.775 667.437 424.665 667.181 424.445C666.924 424.189 666.796 423.86 666.796 423.457C666.796 423.054 666.924 422.725 667.181 422.468C667.437 422.212 667.785 422.084 668.224 422.084H687.227C687.776 422.084 688.215 422.23 688.545 422.523C688.874 422.816 689.039 423.182 689.039 423.622C689.039 424.098 688.856 424.537 688.49 424.94L671.245 445.974H688.105Z" fill="#0E0E0E"/>
<path d="M745.633 397.893C746.267 397.893 746.754 398.064 747.096 398.405C747.437 398.746 747.608 399.185 747.608 399.721C747.608 400.258 747.413 400.697 747.023 401.038C746.681 401.379 746.218 401.55 745.633 401.55H719.595C718.864 401.55 718.279 401.355 717.84 400.965C717.45 400.575 717.255 400.087 717.255 399.502C717.255 398.819 717.498 398.21 717.986 397.674L740.952 369.661H719.156C718.571 369.661 718.108 369.515 717.767 369.222C717.425 368.881 717.255 368.442 717.255 367.906C717.255 367.369 717.425 366.93 717.767 366.589C718.108 366.248 718.571 366.077 719.156 366.077H744.463C745.194 366.077 745.779 366.272 746.218 366.662C746.657 367.052 746.876 367.54 746.876 368.125C746.876 368.759 746.632 369.344 746.145 369.88L723.179 397.893H745.633Z" fill="#0E0E0E"/>
<path d="M802.206 339.88C802.891 339.88 803.417 340.064 803.786 340.433C804.155 340.802 804.339 341.276 804.339 341.855C804.339 342.434 804.128 342.908 803.707 343.277C803.338 343.646 802.838 343.83 802.206 343.83H774.082C773.292 343.83 772.66 343.619 772.186 343.198C771.765 342.777 771.554 342.25 771.554 341.618C771.554 340.881 771.817 340.222 772.344 339.643L797.15 309.386H773.608C772.976 309.386 772.476 309.228 772.107 308.912C771.738 308.543 771.554 308.069 771.554 307.49C771.554 306.911 771.738 306.437 772.107 306.068C772.476 305.699 772.976 305.515 773.608 305.515H800.942C801.732 305.515 802.364 305.726 802.838 306.147C803.312 306.568 803.549 307.095 803.549 307.727C803.549 308.412 803.286 309.044 802.759 309.623L777.953 339.88H802.206Z" fill="#0E0E0E"/>
<path d="M680.77 704.89L747.94 705.89" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
''';

const error404Illistration = '''
<svg width="1080" height="1080" viewBox="0 0 1080 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M541.25 279.47C578.25 249.12 667.8 262.99 691.7 308.91C712.54 343 711.93 403.16 670.77 446.27C639.83 475.71 657.51 514.97 644.31 576.87C619.44 682.87 466.85 645.71 483.2 545.35C489 486.52 521.03 442.86 510.2 407.15C492.36 349.79 514.08 300.51 541.25 279.47Z" fill="#D3D3D3"/>
<path d="M514.4 279.47C551.4 249.12 641 263 664.85 308.9C685.69 342.99 685.08 403.15 643.92 446.26C613 475.71 630.66 515 617.46 576.86C592.59 682.86 440 645.7 456.35 545.34C462.15 486.51 494.18 442.85 483.35 407.14C465.51 349.79 487.23 300.51 514.4 279.47Z" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M622.45 512.9C637.407 531.813 646.211 554.854 647.677 578.922C649.143 602.99 643.2 626.929 630.649 647.517C618.098 668.106 599.541 684.355 577.476 694.077C555.41 703.8 530.896 706.53 507.232 701.899C483.569 697.268 461.892 685.499 445.12 668.175C428.347 650.852 417.284 628.806 413.42 605.006C409.556 581.205 413.076 556.791 423.507 535.052C433.937 513.312 450.777 495.289 471.76 483.41" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M624.519 491.348L477.978 462.678C475.181 462.131 472.47 463.954 471.923 466.751L469.771 477.752C469.223 480.549 471.047 483.26 473.844 483.807L620.385 512.478C623.182 513.025 625.893 511.201 626.44 508.405L628.593 497.403C629.14 494.606 627.316 491.896 624.519 491.348Z" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M599.21 615.7L619.65 656.83L571.9 634.43C582.3 629.76 593 621.15 599.21 615.7Z" fill="#ABABAB"/>
<path d="M598.39 615.21L619.46 654.71C619.704 655.185 619.792 655.725 619.712 656.253C619.631 656.781 619.387 657.27 619.013 657.651C618.638 658.032 618.153 658.285 617.627 658.375C617.101 658.464 616.559 658.386 616.08 658.15L567.42 636.24" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M574.78 632.79L619.03 653.7" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M460.41 588.54L426.23 617.61L478.64 616.18C470.78 607.94 464.09 595.93 460.41 588.54Z" fill="#ABABAB"/>
<path d="M460.8 588.29L426.39 616.94C425.981 617.287 425.693 617.754 425.566 618.275C425.439 618.796 425.48 619.343 425.683 619.839C425.886 620.335 426.241 620.754 426.696 621.037C427.152 621.319 427.685 621.449 428.22 621.41L482.22 619.03" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M476.6 613.58L427.73 616.26" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M584.07 400.95L536.81 345.46C531.87 338.76 537.09 325.77 543.81 320.83C550.51 315.89 567.08 315.48 572.01 322.18L608.42 383C610.789 386.233 611.782 390.272 611.182 394.235C610.583 398.198 608.439 401.762 605.22 404.15V404.15C601.987 406.519 597.948 407.512 593.985 406.912C590.022 406.313 586.458 404.169 584.07 400.95V400.95Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M587.418 364.272L572.854 343.681C569.625 339.116 563.307 338.033 558.742 341.262L558.734 341.268C554.169 344.497 553.086 350.815 556.315 355.38L570.879 375.97C574.107 380.535 580.426 381.618 584.991 378.389L584.999 378.384C589.564 375.155 590.647 368.837 587.418 364.272Z" fill="#0E0E0E"/>
<path d="M604.657 383.151L600.601 381.421C599.483 380.944 598.191 381.464 597.714 382.581L597.71 382.59C597.233 383.708 597.753 385 598.871 385.477L602.927 387.207C604.045 387.684 605.337 387.165 605.814 386.047L605.818 386.038C606.294 384.92 605.775 383.628 604.657 383.151Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M585.411 392.28L585.137 396.681C585.062 397.894 585.984 398.938 587.197 399.014L587.207 399.014C588.42 399.089 589.464 398.167 589.539 396.955L589.812 392.553C589.887 391.34 588.965 390.296 587.752 390.221L587.742 390.22C586.53 390.145 585.486 391.067 585.411 392.28Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M596.671 395.87C597.357 396.839 598.699 397.068 599.668 396.381C600.637 395.695 600.866 394.353 600.179 393.384L597.693 389.876C597.007 388.907 595.665 388.678 594.696 389.364C593.727 390.051 593.498 391.393 594.185 392.362L596.671 395.87Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M672.85 326.27C678.64 324.14 685.03 323.62 688.77 326.27L742.61 372.39C745.882 374.704 748.106 378.218 748.795 382.166C749.485 386.114 748.584 390.174 746.29 393.46V393.46C743.976 396.732 740.462 398.956 736.514 399.645C732.566 400.335 728.506 399.434 725.22 397.14L678.11 368.74" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M714.424 359.026L694.198 344.146C689.714 340.847 683.404 341.808 680.105 346.293L680.099 346.301C676.8 350.785 677.761 357.095 682.246 360.394L702.472 375.273C706.956 378.572 713.266 377.611 716.565 373.127L716.571 373.119C719.87 368.634 718.909 362.325 714.424 359.026Z" fill="#0E0E0E"/>
<path d="M739.137 373.64L734.741 373.286C733.53 373.189 732.469 374.092 732.372 375.303L732.371 375.313C732.274 376.524 733.177 377.585 734.388 377.682L738.783 378.036C739.995 378.133 741.055 377.23 741.153 376.019L741.154 376.009C741.251 374.798 740.348 373.737 739.137 373.64Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M723.686 384.871L724.833 389.149C725.148 390.327 726.36 391.027 727.539 390.711L727.548 390.708C728.727 390.392 729.426 389.181 729.11 388.002L727.964 383.724C727.648 382.546 726.436 381.847 725.258 382.163L725.248 382.165C724.069 382.481 723.37 383.693 723.686 384.871Z" stroke="#0E0E0E" stroke-width="3.01272" stroke-miterlimit="10"/>
<path d="M735.54 388.252C736.498 388.954 737.843 388.745 738.544 387.787C739.245 386.828 739.037 385.483 738.079 384.782L734.608 382.243C733.65 381.542 732.305 381.75 731.603 382.709C730.902 383.667 731.111 385.012 732.069 385.713L735.54 388.252Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M603.7 577.59C611.919 579.811 619.883 582.888 627.46 586.77" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M567 573.27C570.273 573.203 573.49 573.247 576.65 573.4" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M606.77 567C618.575 568.778 630.06 572.254 640.87 577.32" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M568.33 567.19C570.75 566.83 573.143 566.533 575.51 566.3" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M607.06 554.69C619.654 554.65 632.18 556.525 644.21 560.25" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M569.06 560.76C570.987 560.16 572.903 559.607 574.81 559.1" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M471.32 551.63C462.638 550.509 453.861 550.328 445.14 551.09" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M506.21 561.37C503.437 560.177 500.673 559.087 497.92 558.1" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M469.15 540C458.368 537.648 447.321 536.743 436.3 537.31" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M507.3 555.25C504.987 553.877 502.653 552.587 500.3 551.38" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M470.17 526.79C457.974 522.552 445.205 520.192 432.3 519.79" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M509.14 548.76C506.58 546.673 503.993 544.717 501.38 542.89" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M484.23 488.81C472.01 509.48 466.3 530.81 471.65 553.08C476.56 568.59 491.79 566.94 497.5 558.14C506.15 546.09 495.98 516.73 509.44 497.14" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M589.68 509.44C577.46 530.11 571.75 551.44 577.1 573.71C582.01 589.22 597.24 587.57 602.95 578.77C611.6 566.72 601.43 537.36 614.89 517.77" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M575.439 609.419C577.102 600.92 571.56 592.683 563.061 591.02C554.563 589.357 546.325 594.899 544.663 603.398C543 611.896 548.541 620.134 557.04 621.797C565.539 623.459 573.776 617.918 575.439 609.419Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M571.111 608.572C572.306 602.464 568.323 596.543 562.215 595.348C556.106 594.153 550.186 598.136 548.99 604.244C547.795 610.353 551.778 616.273 557.887 617.468C563.995 618.664 569.916 614.681 571.111 608.572Z" fill="#231F20"/>
<path d="M542.42 579.94L533.27 586.1C533.114 586.207 532.937 586.282 532.752 586.32C532.566 586.359 532.374 586.36 532.188 586.324C532.002 586.287 531.825 586.214 531.668 586.109C531.51 586.004 531.375 585.868 531.27 585.71L525.11 576.56C525.003 576.404 524.928 576.227 524.89 576.042C524.851 575.856 524.85 575.664 524.886 575.478C524.923 575.292 524.996 575.115 525.101 574.958C525.206 574.8 525.342 574.665 525.5 574.56L542.8 577.94C542.908 578.096 542.983 578.272 543.022 578.457C543.061 578.642 543.063 578.833 543.028 579.019C542.993 579.206 542.921 579.383 542.816 579.541C542.712 579.699 542.577 579.834 542.42 579.94Z" fill="#231F20"/>
<path d="M550.77 545.79L539.39 549.54L530.69 539.96" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M566.239 613.545C566.519 612.114 565.586 610.727 564.155 610.447C562.725 610.167 561.338 611.1 561.058 612.531C560.778 613.962 561.711 615.349 563.142 615.629C564.573 615.909 565.96 614.976 566.239 613.545Z" fill="white"/>
<path d="M514.267 597.452C515.929 588.954 510.388 580.716 501.889 579.053C493.39 577.391 485.153 582.932 483.49 591.431C481.827 599.93 487.369 608.167 495.868 609.83C504.366 611.493 512.604 605.951 514.267 597.452Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M509.939 596.606C511.134 590.497 507.151 584.577 501.042 583.381C494.934 582.186 489.013 586.169 487.818 592.278C486.623 598.386 490.606 604.307 496.714 605.502C502.823 606.697 508.743 602.714 509.939 596.606Z" fill="#231F20"/>
<path d="M505.067 601.579C505.347 600.148 504.414 598.761 502.983 598.481C501.552 598.201 500.165 599.134 499.885 600.565C499.605 601.996 500.538 603.383 501.969 603.663C503.4 603.943 504.787 603.01 505.067 601.579Z" fill="white"/>
<path d="M516.87 273C514.7 265.61 510.13 258.42 502.61 252.06C479.88 235.62 458.61 220.96 438.37 214.51C410.37 204.92 382.02 223.83 408.98 255.72C420.42 267.18 431.92 252.94 439.53 250.28C446.11 246.64 449.95 254.44 447.18 264.1C440.41 281.22 417.5 286 401.5 271.32C370.3 237.32 377.61 188.41 427.95 193.05C457.95 196.29 490.84 223.54 523.05 249.26C528.103 253.997 531.768 260.024 533.65 266.69" fill="#D3D3D3"/>
<path d="M524.28 273C522.11 265.61 517.54 258.42 510.02 252.06C487.29 235.62 466.02 220.96 445.78 214.51C417.78 204.92 389.43 223.83 416.39 255.72C427.83 267.18 439.33 252.94 446.94 250.28C453.52 246.64 457.36 254.44 454.59 264.1C447.82 281.22 424.91 286 408.91 271.32C377.71 237.32 385.02 188.41 435.36 193.05C465.36 196.29 498.25 223.54 530.46 249.26C535.513 253.997 539.178 260.024 541.06 266.69" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M287.189 653.802C288.889 653.802 290.27 654.44 291.332 655.715C292.607 656.777 293.245 658.158 293.245 659.858C293.245 661.77 292.607 663.364 291.332 664.639C290.27 665.701 288.889 666.232 287.189 666.232H256.592V713.403C256.592 715.528 255.954 717.334 254.679 718.821C253.617 720.096 252.023 720.734 249.899 720.734C247.774 720.734 246.074 720.096 244.799 718.821C243.737 717.334 243.206 715.528 243.206 713.403V666.232H142.49L142.171 666.87V666.232H139.622C137.497 666.232 135.797 665.701 134.522 664.639C133.247 663.364 132.61 661.877 132.61 660.177C132.61 658.477 133.247 656.671 134.522 654.759L244.162 495.717C245.437 493.805 247.455 492.849 250.217 492.849C251.917 492.849 253.405 493.486 254.679 494.761C255.954 496.036 256.592 497.842 256.592 500.179V653.802H287.189ZM151.095 653.802H243.206V520.577L151.095 653.802Z" fill="#0E0E0E"/>
<path d="M913.119 653.802C914.819 653.802 916.2 654.44 917.262 655.715C918.537 656.777 919.174 658.158 919.174 659.858C919.174 661.77 918.537 663.364 917.262 664.639C916.2 665.701 914.819 666.232 913.119 666.232H882.522V713.403C882.522 715.528 881.884 717.334 880.609 718.821C879.547 720.096 877.953 720.734 875.829 720.734C873.704 720.734 872.004 720.096 870.729 718.821C869.667 717.334 869.135 715.528 869.135 713.403V666.232H768.42L768.101 666.87V666.232H765.551C763.427 666.232 761.727 665.701 760.452 664.639C759.177 663.364 758.54 661.877 758.54 660.177C758.54 658.477 759.177 656.671 760.452 654.759L870.092 495.717C871.366 493.805 873.385 492.849 876.147 492.849C877.847 492.849 879.334 493.486 880.609 494.761C881.884 496.036 882.522 497.842 882.522 500.179V653.802H913.119ZM777.025 653.802H869.135V520.577L777.025 653.802Z" fill="#0E0E0E"/>
<path d="M174.88 354.24C183.104 354.24 189.77 347.574 189.77 339.35C189.77 331.126 183.104 324.46 174.88 324.46C166.656 324.46 159.99 331.126 159.99 339.35C159.99 347.574 166.656 354.24 174.88 354.24Z" fill="#E2E2E2"/>
<path d="M148.49 392C152.08 392 154.99 389.09 154.99 385.5C154.99 381.91 152.08 379 148.49 379C144.9 379 141.99 381.91 141.99 385.5C141.99 389.09 144.9 392 148.49 392Z" fill="#E2E2E2"/>
<path d="M267.24 434.5C290.574 434.5 309.49 415.584 309.49 392.25C309.49 368.916 290.574 350 267.24 350C243.906 350 224.99 368.916 224.99 392.25C224.99 415.584 243.906 434.5 267.24 434.5Z" fill="#E2E2E2"/>
<path d="M696.1 866.22C701.131 866.22 705.21 862.141 705.21 857.11C705.21 852.079 701.131 848 696.1 848C691.069 848 686.99 852.079 686.99 857.11C686.99 862.141 691.069 866.22 696.1 866.22Z" fill="#E2E2E2"/>
<path d="M626.24 904.5C647.365 904.5 664.49 887.375 664.49 866.25C664.49 845.125 647.365 828 626.24 828C605.115 828 587.99 845.125 587.99 866.25C587.99 887.375 605.115 904.5 626.24 904.5Z" fill="#E2E2E2"/>
<path d="M692.99 924C696.304 924 698.99 921.314 698.99 918C698.99 914.686 696.304 912 692.99 912C689.676 912 686.99 914.686 686.99 918C686.99 921.314 689.676 924 692.99 924Z" fill="#E2E2E2"/>
<path d="M815.99 180L827.99 155.23" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M787.99 178L779.91 155.23" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M821.99 212L833.99 210" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M560.05 878L529.91 894" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M568.33 904.5L560.05 938" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M568.33 848L548.78 828" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
''';

const underMaintenanceIllistration =
    '''<svg width="1080" height="1080" viewBox="0 0 1080 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M524.01 410.31H995.89" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M991.05 381.06H528.85C526.177 381.06 524.01 383.227 524.01 385.9V400.68C524.01 403.353 526.177 405.52 528.85 405.52H991.05C993.723 405.52 995.89 403.353 995.89 400.68V385.9C995.89 383.227 993.723 381.06 991.05 381.06Z" fill="#D3D3D3"/>
<path d="M991.05 373.62H528.85C526.177 373.62 524.01 375.787 524.01 378.46V393.24C524.01 395.913 526.177 398.08 528.85 398.08H991.05C993.723 398.08 995.89 395.913 995.89 393.24V378.46C995.89 375.787 993.723 373.62 991.05 373.62Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M941.12 397.01V819.65" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M927.88 229.47V819.65" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M883.36 277V819.65" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M870.12 397.01V819.65" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M928.27 403.44L882.97 448.74" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M882.97 403.44L928.27 448.74" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M959.99 366.91V427.91C959.99 429.602 959.318 431.225 958.121 432.421C956.925 433.618 955.302 434.29 953.61 434.29H855.61C853.918 434.29 852.295 433.618 851.099 432.421C849.902 431.225 849.23 429.602 849.23 427.91V329.91C849.23 328.218 849.902 326.595 851.099 325.399C852.295 324.202 853.918 323.53 855.61 323.53H916.61C928.11 323.546 939.135 328.121 947.267 336.253C955.399 344.385 959.974 355.41 959.99 366.91Z" fill="#D3D3D3" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M957.15 495.37H852.04C850.471 495.37 849.2 496.642 849.2 498.21V510.78C849.2 512.348 850.471 513.62 852.04 513.62H957.15C958.718 513.62 959.99 512.348 959.99 510.78V498.21C959.99 496.642 958.718 495.37 957.15 495.37Z" fill="#D3D3D3" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M105.88 701.17V867.14" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M115.84 701.17V867.14" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M281.14 701.17V867.14" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M271.18 701.17V867.14" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M290.77 768.27H96.1401C94.3065 768.27 92.8201 769.756 92.8201 771.59V778.23C92.8201 780.064 94.3065 781.55 96.1401 781.55H290.77C292.604 781.55 294.09 780.064 294.09 778.23V771.59C294.09 769.756 292.604 768.27 290.77 768.27Z" fill="#DDDDDD"/>
<path d="M290.77 692.74H96.1401C94.3065 692.74 92.8201 694.226 92.8201 696.06V702.7C92.8201 704.534 94.3065 706.02 96.1401 706.02H290.77C292.604 706.02 294.09 704.534 294.09 702.7V696.06C294.09 694.226 292.604 692.74 290.77 692.74Z" fill="#DDDDDD"/>
<path d="M290.77 687.89H96.1401C94.3065 687.89 92.8201 689.376 92.8201 691.21V697.85C92.8201 699.684 94.3065 701.17 96.1401 701.17H290.77C292.604 701.17 294.09 699.684 294.09 697.85V691.21C294.09 689.376 292.604 687.89 290.77 687.89Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M290.77 764.41H96.1401C94.3065 764.41 92.8201 765.896 92.8201 767.73V774.37C92.8201 776.204 94.3065 777.69 96.1401 777.69H290.77C292.604 777.69 294.09 776.204 294.09 774.37V767.73C294.09 765.896 292.604 764.41 290.77 764.41Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M957.15 650.96H852.04C850.471 650.96 849.2 652.231 849.2 653.8V666.37C849.2 667.938 850.471 669.21 852.04 669.21H957.15C958.718 669.21 959.99 667.938 959.99 666.37V653.8C959.99 652.231 958.718 650.96 957.15 650.96Z" fill="#D3D3D3" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M928.27 514.28L882.97 559.58" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M882.97 514.28L928.27 559.58" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M928.27 449.4L882.97 494.7" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M882.97 449.4L928.27 494.7" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M928.27 277L882.97 322.3" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M882.97 277L928.27 322.3" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M928.27 559.84L882.97 605.14" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M882.97 559.84L928.27 605.14" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M928.27 605.4L882.97 650.7" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M882.97 605.4L928.27 650.7" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M928.27 669.48L882.97 714.78" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M882.97 669.48L928.27 714.78" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M928.27 715.04L882.97 760.34" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M882.97 715.04L928.27 760.34" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M928.27 760.6L882.97 805.9" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M882.97 760.6L928.27 805.9" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M844 819.65H954.7C956.103 819.65 957.449 820.207 958.441 821.199C959.433 822.191 959.99 823.537 959.99 824.94V862.49C959.99 863.893 959.433 865.239 958.441 866.231C957.449 867.223 956.103 867.78 954.7 867.78H810.54C809.137 867.78 807.791 867.223 806.799 866.231C805.807 865.239 805.25 863.893 805.25 862.49V858.44C805.25 848.159 809.331 838.299 816.597 831.025C823.863 823.752 833.719 819.661 844 819.65Z" fill="#D3D3D3" stroke="black" stroke-width="3" stroke-miterlimit="10"/>
<path d="M927.88 277H883.36L905.13 253.65L927.88 277Z" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M927.88 229.47C930.829 229.47 933.22 227.079 933.22 224.13C933.22 221.181 930.829 218.79 927.88 218.79C924.931 218.79 922.54 221.181 922.54 224.13C922.54 227.079 924.931 229.47 927.88 229.47Z" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M922.54 224.13L905.13 253.65" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M922.54 224.13L528.42 373.62" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M688.23 398.08H634.01C632.182 398.08 630.7 399.562 630.7 401.39V416.93C630.7 418.758 632.182 420.24 634.01 420.24H688.23C690.058 420.24 691.54 418.758 691.54 416.93V401.39C691.54 399.562 690.058 398.08 688.23 398.08Z" fill="#D3D3D3" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M650.42 414.91C653.596 414.91 656.17 412.336 656.17 409.16C656.17 405.984 653.596 403.41 650.42 403.41C647.244 403.41 644.67 405.984 644.67 409.16C644.67 412.336 647.244 414.91 650.42 414.91Z" fill="white" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M672.27 414.91C675.446 414.91 678.02 412.336 678.02 409.16C678.02 405.984 675.446 403.41 672.27 403.41C669.094 403.41 666.52 405.984 666.52 409.16C666.52 412.336 669.094 414.91 672.27 414.91Z" fill="white" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M205.39 658.66L253.49 566.49C254.097 565.327 255.009 564.351 256.129 563.667C257.249 562.984 258.534 562.618 259.846 562.61C261.158 562.602 262.447 562.951 263.576 563.621C264.704 564.29 265.629 565.254 266.25 566.41L315.8 658.58C316.392 659.68 316.688 660.915 316.66 662.163C316.631 663.412 316.28 664.632 315.639 665.704C314.998 666.776 314.09 667.664 313.003 668.28C311.917 668.896 310.689 669.22 309.44 669.22H211.79C210.549 669.22 209.329 668.9 208.247 668.29C207.166 667.681 206.26 666.804 205.617 665.742C204.973 664.681 204.614 663.471 204.575 662.231C204.535 660.99 204.816 659.76 205.39 658.66Z" fill="#BCBCBC"/>
<path d="M194.29 651.13L242.39 559C242.997 557.837 243.909 556.861 245.029 556.177C246.149 555.494 247.434 555.128 248.746 555.12C250.058 555.112 251.347 555.461 252.476 556.131C253.604 556.8 254.529 557.764 255.15 558.92L304.69 651C305.282 652.1 305.578 653.335 305.55 654.584C305.521 655.832 305.17 657.052 304.529 658.124C303.888 659.197 302.98 660.084 301.893 660.7C300.807 661.316 299.579 661.64 298.33 661.64H200.69C199.456 661.635 198.243 661.315 197.168 660.708C196.094 660.101 195.192 659.229 194.55 658.175C193.908 657.121 193.547 655.92 193.502 654.687C193.456 653.453 193.728 652.229 194.29 651.13V651.13Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M249.94 632.46H248.79C245.94 632.46 241.03 630.13 241.03 627.29L243.62 587C243.625 585.63 244.172 584.319 245.14 583.35C246.109 582.382 247.42 581.835 248.79 581.83H249.94C251.31 581.835 252.622 582.382 253.59 583.35C254.558 584.319 255.105 585.63 255.11 587L258 627.29C258 630.14 252.79 632.46 249.94 632.46Z" fill="white" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M249.51 653.3C253.84 653.3 257.35 649.79 257.35 645.46C257.35 641.13 253.84 637.62 249.51 637.62C245.18 637.62 241.67 641.13 241.67 645.46C241.67 649.79 245.18 653.3 249.51 653.3Z" fill="white" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M661.12 464.55V420.24" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M629.25 530.48L661.05 498.86L690.66 530.58" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M625.4 271.59C620.644 271.588 616.014 270.06 612.191 267.23C608.369 264.4 605.556 260.417 604.166 255.868C602.777 251.32 602.884 246.445 604.473 241.962C606.061 237.479 609.047 233.624 612.991 230.965C616.934 228.306 621.627 226.983 626.379 227.191C631.13 227.399 635.69 229.127 639.386 232.121C643.082 235.114 645.719 239.215 646.91 243.82C648.101 248.425 647.782 253.29 646 257.7" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M632.52 228.1C633.099 224.018 634.491 220.094 636.614 216.56C638.737 213.025 641.548 209.953 644.88 207.525C648.212 205.097 651.998 203.363 656.012 202.424C660.027 201.486 664.189 201.363 668.252 202.062C672.315 202.762 676.197 204.27 679.666 206.496C683.136 208.723 686.123 211.624 688.452 215.026C690.78 218.429 692.402 222.264 693.221 226.305C694.04 230.345 694.04 234.509 693.22 238.55" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M682.75 241.63C684.859 240.114 687.296 239.117 689.863 238.722C692.43 238.327 695.054 238.544 697.522 239.356C699.989 240.167 702.229 241.551 704.06 243.393C705.891 245.235 707.261 247.484 708.057 249.956C708.854 252.429 709.054 255.054 708.643 257.619C708.232 260.183 707.221 262.614 705.691 264.714C704.162 266.813 702.158 268.521 699.843 269.699C697.528 270.876 694.967 271.49 692.37 271.49" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M625.4 271.59H692.38" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M132.25 445.45C124.683 445.45 117.316 443.021 111.233 438.52C105.15 434.018 100.673 427.683 98.4615 420.447C96.2498 413.21 96.4199 405.454 98.947 398.322C101.474 391.189 106.224 385.056 112.499 380.826C118.773 376.596 126.24 374.492 133.799 374.824C141.359 375.156 148.613 377.907 154.492 382.671C160.371 387.435 164.565 393.961 166.457 401.288C168.349 408.615 167.838 416.355 165 423.37" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M143.57 376.27C144.519 369.803 146.753 363.59 150.14 357.999C153.526 352.408 157.997 347.551 163.29 343.715C168.582 339.878 174.588 337.139 180.955 335.659C187.322 334.178 193.921 333.987 200.363 335.096C206.805 336.205 212.96 338.592 218.466 342.116C223.971 345.639 228.716 350.229 232.421 355.615C236.126 361 238.715 367.073 240.038 373.474C241.36 379.876 241.388 386.477 240.12 392.89" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M223.48 397.79C226.835 395.375 230.712 393.788 234.797 393.157C238.882 392.526 243.058 392.87 246.985 394.161C250.912 395.451 254.477 397.652 257.392 400.583C260.306 403.514 262.486 407.092 263.754 411.026C265.022 414.96 265.342 419.138 264.688 423.22C264.034 427.301 262.424 431.169 259.99 434.51C257.556 437.851 254.367 440.569 250.683 442.442C246.998 444.315 242.923 445.291 238.79 445.29" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M132.25 445.45H238.79" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M309.3 393.85C304.709 393.847 300.239 392.37 296.55 389.636C292.861 386.903 290.147 383.057 288.807 378.666C287.468 374.274 287.573 369.569 289.108 365.241C290.644 360.914 293.527 357.194 297.335 354.629C301.143 352.063 305.674 350.788 310.261 350.991C314.848 351.194 319.248 352.863 322.815 355.755C326.382 358.646 328.926 362.606 330.074 367.051C331.222 371.497 330.912 376.194 329.19 380.45" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M316.17 351.88C316.739 347.947 318.09 344.167 320.144 340.765C322.198 337.363 324.913 334.406 328.129 332.071C331.344 329.736 334.996 328.069 338.867 327.168C342.737 326.268 346.75 326.153 350.666 326.829C354.582 327.506 358.323 328.96 361.668 331.107C365.012 333.253 367.893 336.049 370.139 339.327C372.385 342.606 373.951 346.302 374.745 350.196C375.539 354.09 375.544 358.104 374.76 362" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M364.66 364.94C366.695 363.474 369.048 362.51 371.527 362.127C374.006 361.744 376.541 361.952 378.924 362.735C381.307 363.517 383.471 364.853 385.24 366.631C387.008 368.41 388.331 370.581 389.101 372.969C389.871 375.356 390.065 377.892 389.668 380.368C389.271 382.845 388.294 385.193 386.816 387.22C385.339 389.247 383.403 390.896 381.167 392.033C378.931 393.17 376.458 393.761 373.95 393.76" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M309.3 393.85H373.95" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M351.37 754.53H236.81C233.596 754.53 230.99 757.136 230.99 760.35V778.23C230.99 781.444 233.596 784.05 236.81 784.05H351.37C354.584 784.05 357.19 781.444 357.19 778.23V760.35C357.19 757.136 354.584 754.53 351.37 754.53Z" fill="white" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M303.31 754.53H317.88L288.37 784.04L274.21 784.08L303.31 754.53Z" fill="#0E0E0E"/>
<path d="M267.88 754.53H282.44L252.94 784.04L238.78 784.08L267.88 754.53Z" fill="#0E0E0E"/>
<path d="M335.25 754.53H349.81L320.3 784.04L306.14 784.08L335.25 754.53Z" fill="#0E0E0E"/>
<path d="M249.92 784.78V863.85C249.92 864.72 249.574 865.554 248.959 866.169C248.344 866.784 247.51 867.13 246.64 867.13H240.09C239.22 867.13 238.386 866.784 237.771 866.169C237.156 865.554 236.81 864.72 236.81 863.85V784.05" fill="white"/>
<path d="M249.92 784.78V863.85C249.92 864.72 249.574 865.554 248.959 866.169C248.344 866.784 247.51 867.13 246.64 867.13H240.09C239.22 867.13 238.386 866.784 237.771 866.169C237.156 865.554 236.81 864.72 236.81 863.85V784.05" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M236.82 754.53V741.06C236.82 740.19 237.166 739.356 237.781 738.741C238.396 738.126 239.23 737.78 240.1 737.78H246.65C247.52 737.78 248.354 738.126 248.969 738.741C249.584 739.356 249.93 740.19 249.93 741.06V754.53" fill="white"/>
<path d="M236.82 754.53V741.06C236.82 740.19 237.166 739.356 237.781 738.741C238.396 738.126 239.23 737.78 240.1 737.78H246.65C247.52 737.78 248.354 738.126 248.969 738.741C249.584 739.356 249.93 740.19 249.93 741.06V754.53" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M350.38 784.08V863.86C350.38 864.73 350.034 865.564 349.419 866.179C348.804 866.794 347.97 867.14 347.1 867.14H340.55C339.68 867.14 338.846 866.794 338.231 866.179C337.616 865.564 337.27 864.73 337.27 863.86V784.08" fill="white"/>
<path d="M350.38 784.08V863.86C350.38 864.73 350.034 865.564 349.419 866.179C348.804 866.794 347.97 867.14 347.1 867.14H340.55C339.68 867.14 338.846 866.794 338.231 866.179C337.616 865.564 337.27 864.73 337.27 863.86V784.08" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M337.28 754.53V741.06C337.28 740.19 337.626 739.356 338.241 738.741C338.856 738.126 339.69 737.78 340.56 737.78H347.11C347.98 737.78 348.814 738.126 349.429 738.741C350.044 739.356 350.39 740.19 350.39 741.06V754.53" fill="white"/>
<path d="M337.28 754.53V741.06C337.28 740.19 337.626 739.356 338.241 738.741C338.856 738.126 339.69 737.78 340.56 737.78H347.11C347.98 737.78 348.814 738.126 349.429 738.741C350.044 739.356 350.39 740.19 350.39 741.06V754.53" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M243.37 750.4C245.38 750.4 247.01 748.77 247.01 746.76C247.01 744.75 245.38 743.12 243.37 743.12C241.36 743.12 239.73 744.75 239.73 746.76C239.73 748.77 241.36 750.4 243.37 750.4Z" fill="white" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M343.83 750.4C345.84 750.4 347.47 748.77 347.47 746.76C347.47 744.75 345.84 743.12 343.83 743.12C341.82 743.12 340.19 744.75 340.19 746.76C340.19 748.77 341.82 750.4 343.83 750.4Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M243.37 798.51C245.38 798.51 247.01 796.88 247.01 794.87C247.01 792.86 245.38 791.23 243.37 791.23C241.36 791.23 239.73 792.86 239.73 794.87C239.73 796.88 241.36 798.51 243.37 798.51Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M343.83 798.51C345.84 798.51 347.47 796.88 347.47 794.87C347.47 792.86 345.84 791.23 343.83 791.23C341.82 791.23 340.19 792.86 340.19 794.87C340.19 796.88 341.82 798.51 343.83 798.51Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M744.72 530.61H383.51C374.563 530.61 367.31 537.863 367.31 546.81V761.31C367.31 770.257 374.563 777.51 383.51 777.51H744.72C753.667 777.51 760.92 770.257 760.92 761.31V546.81C760.92 537.863 753.667 530.61 744.72 530.61Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M730.88 546.25H397.35C389.623 546.25 383.36 552.514 383.36 560.24V732.54C383.36 740.266 389.623 746.53 397.35 746.53H730.88C738.606 746.53 744.87 740.266 744.87 732.54V560.24C744.87 552.514 738.606 546.25 730.88 546.25Z" fill="#DBDBDB"/>
<path d="M730.88 546.25H397.35C389.623 546.25 383.36 552.514 383.36 560.24V732.54C383.36 740.266 389.623 746.53 397.35 746.53H730.88C738.606 746.53 744.87 740.266 744.87 732.54V560.24C744.87 552.514 738.606 546.25 730.88 546.25Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M596.6 777.51H531.63V841.19H596.6V777.51Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M684.78 848.14H445.05C441.438 848.14 438.51 851.068 438.51 854.68V868.94C438.51 872.552 441.438 875.48 445.05 875.48H684.78C688.392 875.48 691.32 872.552 691.32 868.94V854.68C691.32 851.068 688.392 848.14 684.78 848.14Z" fill="#DBDBDB"/>
<path d="M683.98 840.87H444.25C440.638 840.87 437.71 843.798 437.71 847.41V861.67C437.71 865.282 440.638 868.21 444.25 868.21H683.98C687.592 868.21 690.52 865.282 690.52 861.67V847.41C690.52 843.798 687.592 840.87 683.98 840.87Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M563.64 770.76C568.71 770.76 572.82 766.65 572.82 761.58C572.82 756.51 568.71 752.4 563.64 752.4C558.57 752.4 554.46 756.51 554.46 761.58C554.46 766.65 558.57 770.76 563.64 770.76Z" stroke="#0E0E0E" stroke-width="3" stroke-miterlimit="10"/>
<path d="M506.49 605.77L466.75 645.51" stroke="#0E0E0E" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M466.75 605.76L506.49 645.51" stroke="#0E0E0E" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M658.95 605.77L619.2 645.51" stroke="#0E0E0E" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M619.2 605.77L658.95 645.51" stroke="#0E0E0E" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M523.86 688.51H602" stroke="#0E0E0E" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M572.09 689.32H597.3V721.89C597.3 722.534 597.044 723.153 596.588 723.608C596.133 724.064 595.515 724.32 594.87 724.32H574.52C573.876 724.32 573.258 724.064 572.802 723.608C572.346 723.153 572.09 722.534 572.09 721.89V689.32Z" stroke="#0E0E0E" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M584.69 706.82V688.51" stroke="#0E0E0E" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M661.12 472.66C663.362 472.66 665.18 470.842 665.18 468.6C665.18 466.358 663.362 464.54 661.12 464.54C658.878 464.54 657.06 466.358 657.06 468.6C657.06 470.842 658.878 472.66 661.12 472.66Z" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M661.12 473.71V487.23C671.93 487.23 671.93 501.95 661.12 501.95C658.82 501.95 657.12 500.61 655.58 498.86" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M975.89 868.2H85.49" stroke="black" stroke-width="3" stroke-miterlimit="10"/>
<path d="M737.58 868.34H797L770 784.34C769.817 783.769 769.457 783.271 768.973 782.917C768.489 782.564 767.905 782.374 767.305 782.374C766.705 782.374 766.121 782.564 765.637 782.917C765.153 783.271 764.793 783.769 764.61 784.34L737.58 868.34Z" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M773.76 796.18L755.22 813.48L751.75 824.27L776.04 803.25L773.76 796.18Z" fill="#0E0E0E"/>
<path d="M780.02 815.65L745.64 843.27L741.98 854.67L782.42 823.11L780.02 815.65Z" fill="#0E0E0E"/>
<path d="M786.88 836.98L744.61 868.34H756.62L789.34 845.24L786.88 836.98Z" fill="#0E0E0E"/>
</svg>
''';

const paymentProcessIllistration = '''
<svg width="1080" height="1080" viewBox="0 0 1080 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M590.84 242.27H877.06C880.922 242.27 884.625 243.804 887.355 246.535C890.086 249.265 891.62 252.968 891.62 256.83V543C891.62 546.862 890.086 550.565 887.355 553.295C884.625 556.026 880.922 557.56 877.06 557.56H805.37C744.62 557.56 686.358 533.431 643.397 490.479C600.435 447.527 576.293 389.27 576.28 328.52V256.83C576.28 252.968 577.814 249.265 580.545 246.535C583.275 243.804 586.978 242.27 590.84 242.27Z" fill="#E5E5E5"/>
<path d="M270.444 736.1C275.627 720.148 266.897 703.015 250.945 697.832C234.993 692.649 217.86 701.378 212.677 717.33C207.494 733.282 216.224 750.416 232.176 755.599C248.128 760.782 265.261 752.052 270.444 736.1Z" fill="#E2E2E2"/>
<path d="M320.604 675.4C323.104 667.705 318.893 659.44 311.198 656.94C303.503 654.44 295.238 658.651 292.738 666.346C290.238 674.041 294.449 682.306 302.144 684.806C309.839 687.306 318.104 683.095 320.604 675.4Z" fill="#E2E2E2"/>
<path d="M220.94 658.42L182.76 630.24" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M235.32 647.7L228.22 634.87" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M250.88 643.74L254.21 605.75" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M391.35 766.91C443 684.73 495.94 512.78 505.11 412.34C505.282 404.783 508.398 397.591 513.794 392.298C519.191 387.004 526.441 384.027 534 384V384C541.665 384 549.016 387.045 554.435 392.465C559.855 397.884 562.9 405.235 562.9 412.9V714.26C552.9 758.54 534.99 800.12 477.08 801.67L453.67 840.88" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M507.52 656.43V558.58C507.52 551.236 510.437 544.193 515.63 539C520.823 533.807 527.866 530.89 535.21 530.89C538.846 530.89 542.447 531.606 545.807 532.998C549.166 534.389 552.219 536.429 554.79 539C557.361 541.572 559.401 544.624 560.792 547.984C562.184 551.343 562.9 554.944 562.9 558.58V656.42" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M370.77 769.02L463.36 878.91L434.95 912.74L337.49 797.06L370.77 769.02Z" fill="#E5E5E5"/>
<path d="M370.77 742.49L463.36 852.38L434.95 886.21L337.49 770.53L370.77 742.49Z" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M733.95 766.91C682.33 684.73 629.36 512.78 620.19 412.33C620.015 404.773 616.897 397.582 611.499 392.291C606.1 386.999 598.849 384.024 591.29 384V384C583.625 384 576.274 387.045 570.855 392.465C565.435 397.884 562.39 405.235 562.39 412.9V714.26C572.39 758.54 590.3 800.12 648.21 801.67L671.62 840.88" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M617.77 656.43V558.58C617.77 551.236 614.853 544.193 609.66 539C604.467 533.807 597.424 530.89 590.08 530.89V530.89C586.444 530.89 582.843 531.606 579.484 532.998C576.124 534.389 573.071 536.429 570.5 539C567.929 541.572 565.889 544.624 564.498 547.984C563.106 551.343 562.39 554.944 562.39 558.58V656.42" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M754.53 767.49L661.93 877.38L690.35 911.21L787.81 795.53L754.53 767.49Z" fill="#E5E5E5"/>
<path d="M754.53 742.49L661.93 852.38L690.35 886.21L787.81 770.53L754.53 742.49Z" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M524.65 350.05L462.06 269.05" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M561.06 345.64V182.05" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M597.05 345.64L662.72 279.97" stroke="#0E0E0E" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
''';

const profileIconSvg =
    '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.66667 7.83333C8.66667 9.67428 10.1591 11.1667 12 11.1667C13.8409 11.1667 15.3333 9.67428 15.3333 7.83333C15.3333 5.99238 13.8409 4.5 12 4.5C10.1591 4.5 8.66667 5.99238 8.66667 7.83333ZM11.9861 12.8333C8.05159 12.8333 4.82355 14.8554 4.50054 18.8327C4.48295 19.0493 4.89726 19.5 5.10625 19.5H18.8722C19.4983 19.5 19.508 18.9962 19.4983 18.8333C19.2541 14.7443 15.976 12.8333 11.9861 12.8333Z" fill="#010F07"/>
</svg>
''';

const lockIconSvg =
    '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M17 10C18.1046 10 19 10.8954 19 12V18C19 19.1046 18.1046 20 17 20H7C5.89543 20 5 19.1046 5 18V12C5 10.8954 5.89543 10 7 10V9C7 6.23858 9.23858 4 12 4C14.7614 4 17 6.23858 17 9V10ZM12 6C10.3431 6 9 7.34315 9 9V10H15V9C15 7.34315 13.6569 6 12 6Z" fill="#010F07"/>
</svg>
''';

const cardIconSvg =
    '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.66669 7.83334C3.66669 6.91287 4.41288 6.16667 5.33335 6.16667H18.6667C19.5872 6.16667 20.3334 6.91286 20.3334 7.83334V8.66667H3.66669V7.83334ZM3.66669 11.1667H20.3334V16.1667C20.3334 17.0871 19.5872 17.8333 18.6667 17.8333H5.33335C4.41288 17.8333 3.66669 17.0871 3.66669 16.1667V11.1667ZM16.1667 13.6667C15.7065 13.6667 15.3334 14.0398 15.3334 14.5C15.3334 14.9602 15.7064 15.3333 16.1667 15.3333H17.8334C18.2936 15.3333 18.6667 14.9602 18.6667 14.5C18.6667 14.0398 18.2936 13.6667 17.8334 13.6667H16.1667Z" fill="#010F07"/>
</svg>
''';

const markerIconSvg =
    '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.16666 10.75C6.16666 7 8.66666 4.5 12.4167 4.5C16.1667 4.5 18.6667 7.625 18.6667 10.75C18.6667 12.6938 16.853 15.3631 13.2258 18.7577C12.7628 19.191 12.0487 19.209 11.5645 18.7996C7.96595 15.7565 6.16666 13.0733 6.16666 10.75ZM12.4167 12C13.5672 12 14.5 11.0673 14.5 9.91667C14.5 8.76607 13.5672 7.83333 12.4167 7.83333C11.2661 7.83333 10.3333 8.76607 10.3333 9.91667C10.3333 11.0673 11.2661 12 12.4167 12Z" fill="#010F07"/>
</svg>
''';

const fbIconSvg =
    '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.64">
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.1547 19V11.9992H15.2255L15.5 9.58665H13.1547L13.1582 8.37916C13.1582 7.74992 13.2223 7.41278 14.1907 7.41278H15.4853V5H13.4142C10.9264 5 10.0507 6.17033 10.0507 8.13848V9.58692H8.5V11.9994H10.0507V19H13.1547Z" fill="#010F07"/>
</g>
</svg>
''';

const shareIconSvg =
    '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.62683 13.6939C7.6272 13.7341 7.62753 13.7697 7.62753 13.8006C7.62753 13.9503 7.62676 14.1578 7.62596 14.3719C7.62512 14.5999 7.62424 14.8354 7.62424 15.0163H7.74443C7.97199 15.0163 8.17547 14.8667 8.25435 14.6414L8.25437 14.6414C8.92315 12.731 9.86818 11.5708 11.0894 11.1607C11.9413 10.8746 12.879 10.7862 14.1953 10.748V13.7028C14.1953 13.8474 14.247 13.9866 14.3401 14.0927C14.5441 14.3252 14.888 14.3392 15.1083 14.1238L19.8257 9.51233C19.8373 9.50103 19.8484 9.4892 19.859 9.47688C20.0607 9.24216 20.044 8.87926 19.8216 8.66632L15.1042 4.14884C15.0042 4.05306 14.874 4 14.7389 4C14.4387 4 14.1953 4.25691 14.1953 4.57383V7.54677C12.1221 7.64403 10.3885 7.85731 9.23015 9.08696C7.6001 10.8174 7.62014 12.9747 7.62683 13.6939ZM11.176 5.46295C11.176 5.04039 10.8515 4.69784 10.4511 4.69784H6.89939C5.2981 4.69784 4 6.06804 4 7.75827V16.9396C4 18.6298 5.2981 20 6.89939 20H15.5976C17.1989 20 18.497 18.6298 18.497 16.9396V15.4094C18.497 14.9868 18.1724 14.6442 17.7721 14.6442C17.3718 14.6442 17.0473 14.9868 17.0473 15.4094V16.9396C17.0473 17.7847 16.3982 18.4698 15.5976 18.4698H6.89939C6.09875 18.4698 5.4497 17.7847 5.4497 16.9396V7.75827C5.4497 6.91316 6.09875 6.22806 6.89939 6.22806H10.4511C10.8515 6.22806 11.176 5.88551 11.176 5.46295Z" fill="#010F07"/>
</svg>
''';
