import 'package:flutter/material.dart';

class SynchronizedScrolling extends StatefulWidget {
  const SynchronizedScrolling({Key? key}) : super(key: key);

  @override
  State<SynchronizedScrolling> createState() => _SynchronizedScrollingState();
}

class _SynchronizedScrollingState extends State<SynchronizedScrolling> {
  final ScrollController _verticalController = ScrollController();
  final ScrollController _horizontalController = ScrollController();
  
  @override
  void initState() {
    super.initState();
    
    // Link the scroll controllers
    _verticalController.addListener(_syncHorizontalScroll);
    _horizontalController.addListener(_syncVerticalScroll);
  }
  
  void _syncHorizontalScroll() {
    // Calculate relative position for horizontal scroll based on vertical position
    if (!_horizontalController.hasClients || !_verticalController.hasClients) return;
    
    final verticalPosition = _verticalController.offset / _verticalController.position.maxScrollExtent;
    final targetHorizontalOffset = verticalPosition * _horizontalController.position.maxScrollExtent;
    
    if (_horizontalController.offset != targetHorizontalOffset) {
      _horizontalController.jumpTo(targetHorizontalOffset);
    }
  }
  
  void _syncVerticalScroll() {
    // Calculate relative position for vertical scroll based on horizontal position
    if (!_verticalController.hasClients || !_horizontalController.hasClients) return;
    
    final horizontalPosition = _horizontalController.offset / _horizontalController.position.maxScrollExtent;
    final targetVerticalOffset = horizontalPosition * _verticalController.position.maxScrollExtent;
    
    if (_verticalController.offset != targetVerticalOffset) {
      _verticalController.jumpTo(targetVerticalOffset);
    }
  }
  
  @override
  void dispose() {
    _verticalController.removeListener(_syncHorizontalScroll);
    _horizontalController.removeListener(_syncVerticalScroll);
    _verticalController.dispose();
    _horizontalController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // Vertical scrollable content
        Expanded(
          child: ListView.builder(
            controller: _verticalController,
            itemCount: 50,
            itemBuilder: (context, index) => Container(
              height: 100,
              color: Colors.blue[(index % 9 + 1) * 100],
              alignment: Alignment.center,
              child: Text('Vertical Item $index'),
            ),
          ),
        ),
        // Horizontal scrollable content
        SizedBox(
          height: MediaQuery.of(context).size.height,
          width: 200,
          child: ListView.builder(
            controller: _horizontalController,
            scrollDirection: Axis.horizontal,
            itemCount: 50,
            itemBuilder: (context, index) => Container(
              width: 100,
              color: Colors.red[(index % 9 + 1) * 100],
              alignment: Alignment.center,
              child: RotatedBox(
                quarterTurns: 3,
                child: Text('Horizontal Item $index'),
              ),
            ),
          ),
        ),
      ],
    );
  }
}