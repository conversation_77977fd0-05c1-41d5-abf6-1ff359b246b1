import 'dart:convert';
import 'package:carerez/models/user_model.dart';
import 'package:carerez/services/user_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/staff_task.dart';

class UserRepository {
  final UserService _userService;
  
  UserRepository(this._userService);
  
  Future<UserModel> getCurrentUser() async {
    try {
      final user = await _userService.getCurrentUser();
      await _cacheUserData(user);
      return user;
    } catch (e) {
      // Try to get cached data if API fails
      final cachedUser = await getCachedUser();
      if (cachedUser != null) {
        return cachedUser;
      }
      throw e;
    }
  }
  
  Future<UserModel?> getCachedUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userData = prefs.getString('current_user');
    
    if (userData != null) {
      return UserModel.fromJson(jsonDecode(userData));
    }
    return null;
  }
  
  Future<void> _cacheUserData(UserModel user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('current_user', jsonEncode(user.toJson()));
  }
  
  Future<void> clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('current_user');
  }

  Future<List<StaffTask>> getStaffTasks(String staffId) async {
    try {
      return await _userService.getStaffTasks(staffId);
    } catch (e) {
      throw Exception('Error fetching staff tasks: ${e.toString()}');
    }
  }
}