import 'package:carerez/models/complaints.dart';
import 'package:carerez/models/incident.dart';
import 'package:carerez/routes/router_constants.dart';
import 'package:carerez/screens/subscreens/complaintsDetailsScreen.dart';
import 'package:carerez/screens/subscreens/complaintsScreen.dart';
import 'package:carerez/screens/subscreens/formsScreen.dart';
import 'package:carerez/screens/subscreens/incidentDetailsScreen.dart';
import 'package:carerez/screens/subscreens/incidentsScreen.dart';
import 'package:carerez/screens/subscreens/notesScreen.dart';
import 'package:carerez/screens/subscreens/resident/medicine_detail_page.dart';
import 'package:carerez/screens/subscreens/resident/residentCareActivities.dart';
import 'package:carerez/screens/subscreens/resident/residentCurrentMedication.dart';
import 'package:carerez/screens/subscreens/resident/residentDietPlan.dart';
import 'package:carerez/screens/subscreens/resident/residentDoctorNotes.dart';
import 'package:carerez/screens/subscreens/resident/residentFinance.dart';
import 'package:carerez/screens/subscreens/resident/residentHandofNotes.dart';
import 'package:carerez/screens/subscreens/resident/residentMedicalHistory.dart';
import 'package:carerez/screens/subscreens/resident/residentMedicationHistory.dart';
import 'package:carerez/screens/subscreens/resident/residentMedicationIntake.dart';
import 'package:carerez/screens/subscreens/resident/residentMedicineFeedback.dart';
import 'package:carerez/screens/subscreens/residentPersonalScreen.dart';
import 'package:carerez/screens/subscreens/residentsScreen.dart';
import 'package:carerez/screens/subscreens/unitResidentsScreen.dart';
import 'package:carerez/screens/subscreens/unitsScreen.dart';
import 'package:carerez/screens/user/calenderScreen.dart';
import 'package:carerez/screens/user/chatsScreen.dart';
import 'package:carerez/screens/user/notificationsScreen.dart';
import 'package:carerez/screens/user/staffsScreen.dart';
import 'package:carerez/screens/user/ticketDetailScreen.dart';
import 'package:carerez/screens/user/ticketsScreen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import 'package:carerez/blocs/auth/auth_bloc.dart';
import 'package:carerez/blocs/auth/auth_state.dart';
import 'package:carerez/screens/auth/signInScreen.dart';
import 'package:carerez/screens/landing/splashScreen.dart';
import 'package:carerez/screens/subscreens/resident/residentOverview.dart';
import 'package:carerez/screens/user/homeScreen.dart';
import 'package:carerez/screens/user/profileScreen.dart';
import 'package:carerez/screens/user/settingsScreen.dart';
import 'package:carerez/screens/utils/errorScreen.dart';

import '../models/assigned_medicine.dart';
import '../models/medicalTreatment.dart';
import '../models/ticket.dart';
import '../models/unit.dart';
import '../screens/subscreens/resident/medication_history_detail.dart';

final GoRouter router = GoRouter(
  initialLocation: '/',
  routes: [
    GoRoute(
      path: '/',
      name: RouteConstants.splashRouteName,
      builder: (context, state) => BlocBuilder<AuthBloc, AuthState>(
        builder: (context, authState) {
          if (authState is AuthAuthenticated) {
            return const UserHomescreen(); // Auto-navigate to home if logged in
          } else {
            return SplashScreen(); // Show splash while loading
          }
        },
      ),
    ),
    GoRoute(
      path: '/signIn',
      name: RouteConstants.signInRouteName,
      builder: (context, state) => const SignInScreen(),
    ),
    GoRoute(
        path: '/home',
        name: RouteConstants.homescreenRouteName,
        builder: (context, state) => const UserHomescreen()),
    GoRoute(
        path: '/profile',
        name: RouteConstants.profileRouteName,
        builder: (context, state) => const ProfileScreen()),
    GoRoute(
        path: '/settings',
        name: RouteConstants.settingsRouteName,
        builder: (context, state) {
          return SettingsScreen();
        }),
    GoRoute(
      path: '/calender',
      name: RouteConstants.calenderRouteName,
      builder: (context, state) => CalendarPage(),
    ),
    GoRoute(
      path: '/chats',
      name: RouteConstants.chatsRouteName,
      builder: (context, state) => ChatsScreen(),
    ),
    GoRoute(
      path: '/notifications',
      name: RouteConstants.notificationsRouteName,
      builder: (context, state) => NotificationsScreen(),
    ),
    GoRoute(
      path: '/units',
      name: RouteConstants.unitsRouteName,
      builder: (context, state) => UnitsScreen(),
    ),
    GoRoute(
      path: '/unitResidents/:unitId',
      name: RouteConstants.unitResidentsRouteName,
      builder: (context, state) {
        final unit = state.extra as Unit;
        return UnitResidentsScreen(
          unitId: state.pathParameters['unitId'] ?? '',
          unit: unit,
        );
      }
    ),
    GoRoute(
      path: '/forms',
      name: RouteConstants.formRouteName,
      builder: (context, state) => Formsscreen(),
    ),
    GoRoute(
      path: '/incidents',
      name: RouteConstants.incidentsRouteName,
      builder: (context, state) => IncidentManagementScreen(),
    ),
    GoRoute(
        path: '/incidents/incidentDetails',
        name: RouteConstants.incidentDetailsRouteName,
        builder: (context, state) {
          final incident = state.extra as Incident;
          return IncidentDetailsScreen(
            incident: incident,
          );
        }),
    GoRoute(
      path: '/complaints',
      name: RouteConstants.complaintsRouteName,
      builder: (context, state) => ComplaintManagementScreen(),
    ),
    GoRoute(
      path: '/complaints/complaintDetails',
      name: RouteConstants.complaintDetailsRouteName,
      builder: (context, state) {
        final complaint = state.extra as Complaints;
        return ComplaintDetailsScreen(
          complaint: complaint,
        );
      },
    ),
    GoRoute(
      path: '/residents',
      name: RouteConstants.residentsRouteName,
      builder: (context, state) => ResidentsScreen(),
    ),
    GoRoute(
      path: '/notes',
      name: RouteConstants.notesRouteName,
      builder: (context, state) => Notesscreen(),
    ),
    GoRoute(
      path: '/staffs',
      name: RouteConstants.staffsRouteName,
      builder: (context, state) => StaffScreen(),
    ),
    GoRoute(
      path: '/tickets',
      name: RouteConstants.ticketsRouteName,
      builder: (context, state) => TicketsScreen(),
    ),
    GoRoute(
      path: '/ticketDetails',
      name: RouteConstants.ticketDetailScreenRouteName,
      builder: (context, state) => TicketDetailScreen(
        ticket: state.extra as TicketModel,
      ),
    ),
    GoRoute(
      path: '/residentsPersonal/:residentId/:residentUUID',
      name: RouteConstants.residentPersonalRouteName,
      builder: (context, state) {
        final residentId = state.pathParameters['residentId'];
        final residentUUID = state.pathParameters['residentUUID'];
        return Residentpersonalscreen(
          residentId: residentId!,
          residentUUID: residentUUID!,
        );
      },
    ),
    GoRoute(
      path: '/residentsOverview/:residentId',
      name: RouteConstants.residentOverviewRouteName,
      builder: (context, state) {
        final residentId = state.pathParameters['residentId'];
        final residentUUID = state.uri.queryParameters['residentUUID'];
        return Residentoverview(
          residentId: residentId!,
          residentUUID: residentUUID ?? '',
        );
      },
    ),
    GoRoute(
      path: '/residentsMedicalHistory/:residentId',
      name: RouteConstants.residentMedicalHistoryRouteName,
      builder: (context, state) {
        final residentId = state.pathParameters['residentId'];
        final residentUUID = state.uri.queryParameters['residentUUID'];
        return ResidentMedicalHistory(
          residentId: residentId!,
          residentUUID: residentUUID ?? '',
        );
      },
    ),

    GoRoute(
      path:'/residentsMedicalHistoryDetails/:residentId',
      name: RouteConstants.residentMedicalHistoryDetailsRouteName,
      builder: (context, state) {
        final treatment = state.extra as MedicalTreatment;
        return MedicationHistoryDetailPage(
          medication: treatment
        );
      },

    ),
    GoRoute(
      path: '/residentsDietPlan/:residentId',
      name: RouteConstants.residentDietPlanRouteName,
      builder: (context, state) {
        final residentId = state.pathParameters['residentId'];
        final residentUUID = state.uri.queryParameters['residentUUID'];
        return ResidentDietPlan(
          residentId: residentId!,
          residentUUID: residentUUID ?? '',
        );
      },
    ),
    GoRoute(
      path: '/residentsCareActivities/:residentId',
      name: RouteConstants.residentCareActivitesRouteName,
      builder: (context, state) {
        final residentId = state.pathParameters['residentId'];
        return ResidentCareActivities(
          residentId: residentId!,
          residentUUID: state.uri.queryParameters['residentUUID'] ?? '',
        );
      },
    ),
    GoRoute(
      path: '/residentsHandOffNotes/:residentId',
      name: RouteConstants.residentHandOffNotesRouteName,
      builder: (context, state) {
        final residentId = state.pathParameters['residentId'];
        return Residenthandofnotes(
          residentId: residentId!,
        );
      },
    ),
    GoRoute(
      path: '/residentMedicationIntake/:residentId',
      name: RouteConstants.residentMedicationIntakeRouteName,
      builder: (context, state) {
        final residentId = state.pathParameters['residentId'];
        return ResidentMedicationIntake(
          residentId: residentId!,
          residentUUID: state.uri.queryParameters['residentUUID'] ?? '',
        );
      },
    ),
    GoRoute(
      path: '/residentCurrentMedication/:residentId',
      name: RouteConstants.residentPrescribedMedicationRouteName,
      builder: (context, state) {
        final residentId = state.pathParameters['residentId'];
        return CurrentMedicationPage(
          residentId: residentId!,
          residentUUID: state.uri.queryParameters['residentUUID'] ?? '',
        );
      },
    ),
    GoRoute(
      path:'/residentMedicineDetails/:residentId',
      name: RouteConstants.residentMedicineDetailsRouteName,
      builder: (context, state) {
        final medicine = state.extra as ResidentAssignedMedicine;
        return MedicineDetailPage(
          assignedMedicine: medicine,
        );
      },

    ),


    GoRoute(
      path: '/residentDoctorNotes/:residentId',
      name: RouteConstants.residentDoctorNotesRouteName,
      builder: (context, state) {
        final residentId = state.pathParameters['residentId'];
        return ResidentNotesPage(
          residentId: residentId!,
          residentUUID: state.uri.queryParameters['residentUUID'] ?? '',
        );
      },
    ),
    GoRoute(
      path: '/residentFianance/:residentId',
      name: RouteConstants.residentFinanceRouteName,
      builder: (context, state) {
        final residentId = state.pathParameters['residentId'];
        return Residentfinance(
          residentId: residentId!,
          residentUUID: state.uri.queryParameters['residentUUID'] ?? '',
        );
      },
    ),
    GoRoute(
      path: '/residentMedicineFeedback/:residentId',
      name: RouteConstants.residentMedicineFeedbackRouteName,
      builder: (context, state) {
        final residentId = state.pathParameters['residentId'];
        return MedicineFeedbackPage(
          residentId: residentId!,
        );
      },
    ),

    GoRoute(
      path: '/residentMedicationHistory/:residentId',
      name: RouteConstants.residentMedicationHistoryRouteName,
      builder: (context, state) {
        final residentId = state.pathParameters['residentId'];
        return ResidentMedicationHistoryPage(
          residentId: residentId!,
          residentUUID: state.uri.queryParameters['residentUUID'] ?? '',
        );
      },
    ),

  ],
  errorPageBuilder: (context, state) =>
      MaterialPage(key: state.pageKey, child: const Error404Screen()),
);
