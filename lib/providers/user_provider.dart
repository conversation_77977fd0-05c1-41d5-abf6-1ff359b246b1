import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:carerez/blocs/user/user_bloc.dart';
import 'package:carerez/blocs/user/user_state.dart';
import 'package:carerez/models/user_model.dart';

class UserProvider extends StatelessWidget {
  final Widget Function(BuildContext context, UserModel? user) builder;
  
  const UserProvider({Key? key, required this.builder}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, state) {
        if (state is UserLoaded) {
          return builder(context, state.user);
        }
        return builder(context, null);
      },
    );
  }
  
  static UserModel? of(BuildContext context) {
    final state = context.read<UserBloc>().state;
    if (state is UserLoaded) {
      return state.user;
    }
    return null;
  }
}